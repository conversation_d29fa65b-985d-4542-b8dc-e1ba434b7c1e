[core]
	repositoryformatversion = 0
	filemode = true
	bare = false
	logallrefupdates = true
	ignorecase = true
	precomposeunicode = true
	hooksPath = .husky
[submodule]
	active = .
[remote "origin"]
	url = https://github.com/XPROZ-Technologies/mediasoft_admin.git
	fetch = +refs/heads/*:refs/remotes/origin/*
[branch "main"]
	remote = origin
	merge = refs/heads/main
[branch "fix/1206"]
	remote = origin
	merge = refs/heads/fix/1206
[branch "fix/1906"]
	remote = origin
	merge = refs/heads/fix/1906
[branch "MED_101"]
	remote = origin
	merge = refs/heads/MED_101
[branch "feature/issue"]
	remote = origin
	merge = refs/heads/feature/issue
[branch "feature/issueApprove"]
	remote = origin
	merge = refs/heads/feature/issueApprove
[branch "fix/config"]
	remote = origin
	merge = refs/heads/fix/config
[branch "fix/0707"]
	remote = origin
	merge = refs/heads/fix/0707
[branch "fix/issue"]
	remote = origin
	merge = refs/heads/fix/issue
[branch "autoComplete"]
	remote = origin
	merge = refs/heads/autoComplete
[branch "addUser"]
	remote = origin
	merge = refs/heads/addUser
[branch "fix/1607"]
	remote = origin
	merge = refs/heads/fix/1607
[branch "fix/1707"]
	remote = origin
	merge = refs/heads/fix/1707
[branch "fix/1807"]
	remote = origin
	merge = refs/heads/fix/1807
[branch "fix/1907"]
	remote = origin
	merge = refs/heads/fix/1907
[branch "fix/2007"]
	remote = origin
	merge = refs/heads/fix/2007
[branch "fix/2107"]
	remote = origin
	merge = refs/heads/fix/2107
[branch "feat/article-royalties"]
	remote = origin
	merge = refs/heads/feat/article-royalties
[branch "feat/implement-article-royaly-users"]
	remote = origin
	merge = refs/heads/feat/implement-article-royaly-users
