0000000000000000000000000000000000000000 86f6b5ee399f2e24b119e10daebd3aab0df48226 Hoàn XPROZ <<EMAIL>> 1749355658 +0700	clone: from https://github.com/hoanmuada/mediasoft_fe.git
86f6b5ee399f2e24b119e10daebd3aab0df48226 edf71e34b2b2e51fa15ad0b90b78d3cca4025cbf Hoàn XPROZ <<EMAIL>> 1749356867 +0700	commit: refactor: rename Notifications to Notification and update related types
edf71e34b2b2e51fa15ad0b90b78d3cca4025cbf 8c37fe1636d1b080179d55934347334c355bf0ea Hoàn XPROZ <<EMAIL>> 1749357250 +0700	commit: refactor: rename ListNotifications to ListNotification and update imports
8c37fe1636d1b080179d55934347334c355bf0ea b9b827a0f094cd412449bfb992a614276655d28d Hoàn XPROZ <<EMAIL>> 1749386118 +0700	commit: refactor: update data handling in list components to use optional chaining
b9b827a0f094cd412449bfb992a614276655d28d cacc8e8bd0422bcc62252aa7b948722675670966 Hoàn XPROZ <<EMAIL>> 1749713523 +0700	pull --no-stat -v --progress origin main: Fast-forward
cacc8e8bd0422bcc62252aa7b948722675670966 5189242a097a7704366feb5a71e677e5a348032f Hoàn XPROZ <<EMAIL>> 1749747842 +0700	pull --ff --recurse-submodules --progress origin: Fast-forward
5189242a097a7704366feb5a71e677e5a348032f 22d8e11d309a43118e99ce102990a6fbaf350cf2 Hoàn XPROZ <<EMAIL>> 1749747887 +0700	checkout: moving from main to fix/1206
22d8e11d309a43118e99ce102990a6fbaf350cf2 4e8236255a7d7d93ad042b058abd976bbeedd884 Hoàn XPROZ <<EMAIL>> 1749747999 +0700	commit (merge): Merge branch 'main' into fix/1206
4e8236255a7d7d93ad042b058abd976bbeedd884 6e878e65806e598e7b9926d1fb8c00111344b866 Hoàn XPROZ <<EMAIL>> 1749748674 +0700	commit: refactor: update variable names and formatting for consistency
6e878e65806e598e7b9926d1fb8c00111344b866 8eda8ebf38b9320273f0179384b2bc6858852223 Hoàn XPROZ <<EMAIL>> 1749808435 +0700	pull --ff --recurse-submodules --progress origin: Fast-forward
8eda8ebf38b9320273f0179384b2bc6858852223 83091bad08330728acf3072d3d1dbd51e99096e9 Hoàn XPROZ <<EMAIL>> 1749808457 +0700	merge main: Merge made by the 'ort' strategy.
83091bad08330728acf3072d3d1dbd51e99096e9 adb0362fe424994393491bb73ce07deb4fe4a26a Hoàn XPROZ <<EMAIL>> 1749818239 +0700	commit: refactor: update variable names and formatting for consistency
adb0362fe424994393491bb73ce07deb4fe4a26a 0335025f1dbc3dbe673ac637d3c293b5813cfda0 Hoàn XPROZ <<EMAIL>> 1749818396 +0700	checkout: moving from fix/1206 to main
0335025f1dbc3dbe673ac637d3c293b5813cfda0 41339d8419b8a1b5593f4b349ea0c378fcea96b3 Hoàn XPROZ <<EMAIL>> 1749818406 +0700	pull --ff --recurse-submodules --progress origin: Fast-forward
41339d8419b8a1b5593f4b349ea0c378fcea96b3 cea99b26e2af7f2ed1c72baa8dda53c98ac57a35 Hoàn XPROZ <<EMAIL>> 1749818477 +0700	commit: refactor: format code for better readability in ListUser and UserList components
cea99b26e2af7f2ed1c72baa8dda53c98ac57a35 3489ad85b120b77ff6b163c0d6c11d1d55690f0c Hoàn XPROZ <<EMAIL>> 1750064942 +0700	pull --no-stat -v --progress origin main: Fast-forward
3489ad85b120b77ff6b163c0d6c11d1d55690f0c 22b33892f83ac96cc25ff9be22930cd67f7e1b25 Hoàn XPROZ <<EMAIL>> 1750265478 +0700	pull --no-stat -v --progress origin main: Fast-forward
22b33892f83ac96cc25ff9be22930cd67f7e1b25 a87c095d513047bf98cd92c71cdd6388e8748c08 Hoàn XPROZ <<EMAIL>> 1750266569 +0700	commit: Update Portlet model to allow nullable sql and desc, adjust UpdatePortletForm to make sql optional and add JSON view
a87c095d513047bf98cd92c71cdd6388e8748c08 8cc2ab3bf58e99c7500e4ac2a151b3f9308743ac Hoàn XPROZ <<EMAIL>> 1750320371 +0700	pull --ff --recurse-submodules --progress origin: Fast-forward
8cc2ab3bf58e99c7500e4ac2a151b3f9308743ac cc084b8e78afec18f245a206b1b63afea88ee90a Hoàn XPROZ <<EMAIL>> 1750320381 +0700	checkout: moving from main to fix/1906
cc084b8e78afec18f245a206b1b63afea88ee90a e065cc9e27e8ad67a99e5cc1fdcd1fbbc329cd5a Hoàn XPROZ <<EMAIL>> 1750345590 +0700	commit: refactor: update Config interfaces and improve Config modal handling
e065cc9e27e8ad67a99e5cc1fdcd1fbbc329cd5a 2a44a9ae34e2a4160d8ad4381dd3d184d5340311 Hoàn XPROZ <<EMAIL>> 1750345814 +0700	commit: refactor: simplify input class handling in ModalConfigUpdate component
2a44a9ae34e2a4160d8ad4381dd3d184d5340311 8cc2ab3bf58e99c7500e4ac2a151b3f9308743ac Hoàn XPROZ <<EMAIL>> 1750345879 +0700	checkout: moving from fix/1906 to main
8cc2ab3bf58e99c7500e4ac2a151b3f9308743ac 66fc9f20302f94a4a4f84fef07f604a1a289f673 Hoàn XPROZ <<EMAIL>> 1750345887 +0700	pull --ff --recurse-submodules --progress origin: Fast-forward
66fc9f20302f94a4a4f84fef07f604a1a289f673 b9df8e19e59a521bbf003c85e7068f69a5cf1c75 Hoàn XPROZ <<EMAIL>> 1750348777 +0700	commit: refactor: update layout type handling and improve imports in related components
b9df8e19e59a521bbf003c85e7068f69a5cf1c75 ee6630abbbe1b55fa1a979c9c38f2242f66b1b2d Hoàn XPROZ <<EMAIL>> 1750350447 +0700	commit: feat: add SQL execution functionality in UpdatePortletForm and enhance UI for SQL results
ee6630abbbe1b55fa1a979c9c38f2242f66b1b2d b4aff4f319ff62f570a899747e2ff96e31883829 Hoàn XPROZ <<EMAIL>> 1750350756 +0700	commit: refactor: update sqlResult state type for improved type safety
b4aff4f319ff62f570a899747e2ff96e31883829 2d81e5a6b721cd08e347a9e16d87dcb25ef40afc Hoàn XPROZ <<EMAIL>> 1750430618 +0700	pull --no-stat -v --progress origin main: Fast-forward
2d81e5a6b721cd08e347a9e16d87dcb25ef40afc 6a8a06fcd83bf5dcdd8d420b41b9908eb36b5cc0 Hoàn XPROZ <<EMAIL>> 1750430926 +0700	commit: refactor: improve formatting of tag display in InfoTab component
6a8a06fcd83bf5dcdd8d420b41b9908eb36b5cc0 b9d8485a5d53a48e4a02963d8103da400017daf5 Hoàn XPROZ <<EMAIL>> 1750433082 +0700	commit: feat: add require_change_password field to User model and UpdateUserForm
b9d8485a5d53a48e4a02963d8103da400017daf5 67146d16d9bc2354fc68f321c42ff3657d4dab85 Hoàn XPROZ <<EMAIL>> 1750434009 +0700	commit: feat: add is_default field to Layout model and UpdateLayoutForm
67146d16d9bc2354fc68f321c42ff3657d4dab85 cf7c5d2cdb34190499ca15c12945c3584eae9709 Hoàn XPROZ <<EMAIL>> 1750439082 +0700	commit: feat: update Category model and queries to include article layout fields and modify category list query
cf7c5d2cdb34190499ca15c12945c3584eae9709 4cb403d3012b28ff4a31e049eb57726f77a6e9dd Hoàn XPROZ <<EMAIL>> 1750439095 +0700	merge origin/main: Merge made by the 'ort' strategy.
4cb403d3012b28ff4a31e049eb57726f77a6e9dd 997caa8dc1788659bafdb225adec8db254ae3fdc Hoàn XPROZ <<EMAIL>> 1750439410 +0700	commit: fix: add non-null assertion for access and refresh tokens in authentication flow
997caa8dc1788659bafdb225adec8db254ae3fdc af20762203168b5b7bda0617df1cecd483df8f87 Hoàn XPROZ <<EMAIL>> 1750440605 +0700	commit: feat: update Category model to allow null values for layout IDs and integrate layout data in CategoryList and ModalCategoryUpdate components
af20762203168b5b7bda0617df1cecd483df8f87 557169f87a574185b7d059a7b5aaa05750c10c7b Hoàn XPROZ <<EMAIL>> 1750483121 +0700	commit: feat: add is_mobile field to Layout model and update related components for mobile layout support
557169f87a574185b7d059a7b5aaa05750c10c7b e261f8f8a653a5b018bf8b254a6eef128f803d0e Hoàn XPROZ <<EMAIL>> 1750493976 +0700	commit: feat: enhance ModalCategoryUpdate with layout selection for web and mobile
e261f8f8a653a5b018bf8b254a6eef128f803d0e ad181c08c6931f9d680abfa389c65a70df0368fd Hoàn XPROZ <<EMAIL>> 1750494091 +0700	commit: feat: add layouts dependency to useEffect in ModalCategoryUpdate for layout updates
ad181c08c6931f9d680abfa389c65a70df0368fd 9c33faa1393ad0c82b19bb4bb84dc14c9e0287f6 Hoàn XPROZ <<EMAIL>> 1750495543 +0700	commit: feat: update ListCategory and ModalCategoryUpdate to include articleTypeId and conditional rendering for layouts
9c33faa1393ad0c82b19bb4bb84dc14c9e0287f6 f09fb5a071e3aaf0d894aa4e1b8f75eec9ce3294 Hoàn XPROZ <<EMAIL>> 1750496431 +0700	commit: feat: update ModalCategoryUpdate to set default layouts based on layout type and mobile status
f09fb5a071e3aaf0d894aa4e1b8f75eec9ce3294 12d201c8d410fe49f0f74011af818f3a358a6cb0 Hoàn XPROZ <<EMAIL>> 1750861097 +0700	pull --no-stat -v --progress origin main: Fast-forward
12d201c8d410fe49f0f74011af818f3a358a6cb0 4d98bf4c4c0a229da04bb4bb69fa6ebd8ed44ce9 Hoàn XPROZ <<EMAIL>> ********** +0700	checkout: moving from main to MED_101
4d98bf4c4c0a229da04bb4bb69fa6ebd8ed44ce9 f677f682df1a5019475e79489ccf0d2d1c9a7fd0 Hoàn XPROZ <<EMAIL>> ********** +0700	pull --ff --recurse-submodules --progress origin: Fast-forward
f677f682df1a5019475e79489ccf0d2d1c9a7fd0 a009a9168c61b0e9c116fa5fa6d199d25cd03ce2 Hoàn XPROZ <<EMAIL>> ********** +0700	commit: feat: refactor publications components to support press publications and update routing
a009a9168c61b0e9c116fa5fa6d199d25cd03ce2 12d201c8d410fe49f0f74011af818f3a358a6cb0 Hoàn XPROZ <<EMAIL>> ********** +0700	checkout: moving from MED_101 to main
12d201c8d410fe49f0f74011af818f3a358a6cb0 a3433cdd3f1db812598b64ccc234ad29f52593cc Hoàn XPROZ <<EMAIL>> ********** +0700	pull --ff --recurse-submodules --progress origin: Fast-forward
a3433cdd3f1db812598b64ccc234ad29f52593cc a9cb55e5a846f1cd3b028f710e5253b5b1176f07 Hoàn XPROZ <<EMAIL>> ********** +0700	checkout: moving from main to feature/issue
a9cb55e5a846f1cd3b028f710e5253b5b1176f07 148363039b335914791499660ad981f9eae483d9 Hoàn XPROZ <<EMAIL>> ********** +0700	commit (merge): Merge branch 'main' into feature/issue
148363039b335914791499660ad981f9eae483d9 7703a84aa35dc2f355b6b675d93c00e5219af8b8 Hoàn XPROZ <<EMAIL>> ********** +0700	commit: feat: refactor Issue and IssueList to streamline PressPublication handling and improve query structure
7703a84aa35dc2f355b6b675d93c00e5219af8b8 14021bd0b0efc3146b51b25d777e81678d880cdf Hoàn XPROZ <<EMAIL>> 1750925319 +0700	pull --ff --recurse-submodules --progress origin: Fast-forward
14021bd0b0efc3146b51b25d777e81678d880cdf f51220e5e09ee0084eed2647c7a0365d5158db25 Hoàn XPROZ <<EMAIL>> ********** +0700	commit: feat: update AdvertiseItem and Issue interfaces to use FileResponse for file handling
f51220e5e09ee0084eed2647c7a0365d5158db25 bfb4c6b2c45ac2d231d4e3a8d9aad484f2caab96 Hoàn XPROZ <<EMAIL>> ********** +0700	checkout: moving from feature/issue to main
bfb4c6b2c45ac2d231d4e3a8d9aad484f2caab96 6e8924a70772159c1e3293f61138de468bf33c5d Hoàn XPROZ <<EMAIL>> ********** +0700	commit: feat: refactor routing for PressPublicationList and update issue_days handling in ModalPressPublicationUpdate
6e8924a70772159c1e3293f61138de468bf33c5d e9802a0f43c46b44f4aa965977cc18dc8959c7b0 Hoàn XPROZ <<EMAIL>> ********** +0700	commit: feat: update Issue and IssuePage interfaces to use ItemStatus for status_id and remove deprecated enums
e9802a0f43c46b44f4aa965977cc18dc8959c7b0 d2e3673b4a1d12ad2a37518ee80618cbb3630f29 Hoàn XPROZ <<EMAIL>> ********** +0700	pull --no-stat -v --progress origin main: Fast-forward
d2e3673b4a1d12ad2a37518ee80618cbb3630f29 bf9377640cbc7e17f9e95d43d97ad9cd55b6c0d8 Hoàn XPROZ <<EMAIL>> ********** +0700	commit: feat: update Issue and ModalIssueUpdate components to handle nullable file and avatar IDs, and refactor press publication handling
bf9377640cbc7e17f9e95d43d97ad9cd55b6c0d8 c94ce4a46da08abbe9d86163e8922c913635cc5f Hoàn XPROZ <<EMAIL>> ********** +0700	commit: feat: enhance Issue and IssuePage components to support approve_status_id and refactor related logic
c94ce4a46da08abbe9d86163e8922c913635cc5f 63458dddb6673e68d5898f5a74e656a5c3999591 Hoàn XPROZ <<EMAIL>> 1751440984 +0700	pull --no-stat -v --progress origin main: Fast-forward
63458dddb6673e68d5898f5a74e656a5c3999591 f3d5c8e98ba532420ba4435fe34bc0354608f762 Hoàn XPROZ <<EMAIL>> 1751441559 +0700	commit: feat: make publish_date optional in Issue interface and update related components
f3d5c8e98ba532420ba4435fe34bc0354608f762 a9664b310d98b49677a6a33217bd9173e9f7f122 Hoàn XPROZ <<EMAIL>> 1751445785 +0700	pull --no-stat -v --progress origin main: Fast-forward
a9664b310d98b49677a6a33217bd9173e9f7f122 fef224446ea53beb05aea8fb1d797b65fab009cb Hoàn XPROZ <<EMAIL>> 1751445848 +0700	commit: feat: update UpdatePortletForm to set is_client to false and improve error handling
fef224446ea53beb05aea8fb1d797b65fab009cb 1471004b207bfe133420e63a4c86408c42f0528e Hoàn XPROZ <<EMAIL>> 1751623273 +0700	pull --no-stat -v --progress origin main: Fast-forward
1471004b207bfe133420e63a4c86408c42f0528e f3d34b23a2cfba5b2e9bd9c920b80913926b50ce Hoàn XPROZ <<EMAIL>> 1751627619 +0700	commit: feat: add VITE_STATIC_TOKEN to environment and update components to use CALL_STATIC_TOKEN operation
f3d34b23a2cfba5b2e9bd9c920b80913926b50ce 0ffba51517d8be5781dc59676a9d8b6293ec00c8 Hoàn XPROZ <<EMAIL>> 1751683352 +0700	pull --ff --recurse-submodules --progress origin: Fast-forward
0ffba51517d8be5781dc59676a9d8b6293ec00c8 4521706ad56daf0fcfbf9bcb8afbee3810ca22b2 Hoàn XPROZ <<EMAIL>> 1751683386 +0700	checkout: moving from main to feature/issueApprove
4521706ad56daf0fcfbf9bcb8afbee3810ca22b2 c15007d9fa02fd30762517fc358e03812ade26f6 Hoàn XPROZ <<EMAIL>> 1751683418 +0700	merge main: Merge made by the 'ort' strategy.
c15007d9fa02fd30762517fc358e03812ade26f6 13743ce86519127835b8a9af772513a98f6bb495 Hoàn XPROZ <<EMAIL>> 1751684131 +0700	commit: feat: refactor components for improved readability and performance
13743ce86519127835b8a9af772513a98f6bb495 59dd402f243d4d029a408fc2298103b2c6489a8c Hoàn XPROZ <<EMAIL>> 1751686187 +0700	commit: feat: rename IssueApprove to IssuePageFile and update related components
59dd402f243d4d029a408fc2298103b2c6489a8c e58d3b432f9f47ef17d1f35a4fb540d79fda6312 Hoàn XPROZ <<EMAIL>> 1751687482 +0700	commit: feat: update imports and rename PressPublication components for consistency
e58d3b432f9f47ef17d1f35a4fb540d79fda6312 448365b56a3efa9acc9660aeb34c2e7a8571449e Hoàn XPROZ <<EMAIL>> 1751687695 +0700	commit: feat: remove APPROVED status and update filtering logic to use ACTIVE status
448365b56a3efa9acc9660aeb34c2e7a8571449e 0ffba51517d8be5781dc59676a9d8b6293ec00c8 Hoàn XPROZ <<EMAIL>> 1751687813 +0700	checkout: moving from feature/issueApprove to main
0ffba51517d8be5781dc59676a9d8b6293ec00c8 1bc22ecf2b31236e382328d9cd4eedacea4286ae Hoàn XPROZ <<EMAIL>> 1751687825 +0700	pull --ff --recurse-submodules --progress origin: Fast-forward
1bc22ecf2b31236e382328d9cd4eedacea4286ae b0d1e760c6c928e98faa9ac16f0cac6156a3df8a Hoàn XPROZ <<EMAIL>> 1751693695 +0700	commit: feat: add press publication support to IssuePageFile and related components
b0d1e760c6c928e98faa9ac16f0cac6156a3df8a 9a013eb30cd10d14676302d1f5803c754e871194 Hoàn XPROZ <<EMAIL>> 1751699692 +0700	commit: feat: update modal title in ModalIssuePageFileUpdate for approval context
9a013eb30cd10d14676302d1f5803c754e871194 53303a4ac0e19ec654845a4d2568c45c78856776 Hoàn XPROZ <<EMAIL>> 1751773305 +0700	commit: feat: add departmentId prop to IssuePageFileList and ModalIssuePageFileUpdate components
53303a4ac0e19ec654845a4d2568c45c78856776 29767b7de7bd72d31ca392e62603be7866b71107 Hoàn XPROZ <<EMAIL>> 1751773450 +0700	merge origin/main: Merge made by the 'ort' strategy.
29767b7de7bd72d31ca392e62603be7866b71107 6b7878503eebb5131cb410f2c60df5a6798d8a3f Hoàn XPROZ <<EMAIL>> 1751774557 +0700	commit: feat: enhance IssuePageFile components with file handling and UI updates
6b7878503eebb5131cb410f2c60df5a6798d8a3f ca2e0cb0dccca12eedb595db7f894844356dd2d7 Hoàn XPROZ <<EMAIL>> 1751777907 +0700	commit: feat: replace anchor tag with Link component for file navigation in ListIssuePageFile
ca2e0cb0dccca12eedb595db7f894844356dd2d7 be6be27224a64dcb55d692c1fce7bda164889e82 Hoàn XPROZ <<EMAIL>> 1751788413 +0700	commit: feat: add file_ids property to IssuePageFile and update related services and components
be6be27224a64dcb55d692c1fce7bda164889e82 79c4e641f5d788ab22ae54be2dcdcedfeb72be0f Hoàn XPROZ <<EMAIL>> 1751788424 +0700	merge origin/main: Merge made by the 'ort' strategy.
79c4e641f5d788ab22ae54be2dcdcedfeb72be0f c93793dfea8059dbf2a284a5d28bca7710f2c7cd Hoàn XPROZ <<EMAIL>> 1751789788 +0700	commit: feat: add files property to IssuePageFile and update related components for file display
c93793dfea8059dbf2a284a5d28bca7710f2c7cd bc8b78cbe0bc63ba6fc346defdac6f0e7e70b2f2 Hoàn XPROZ <<EMAIL>> 1751790149 +0700	pull --no-stat -v --progress origin main: Fast-forward
bc8b78cbe0bc63ba6fc346defdac6f0e7e70b2f2 a201345e16f32e9200af19d137b35f48e80a5e57 Hoàn XPROZ <<EMAIL>> 1751793108 +0700	commit: feat: update ListIssuePageFile and related components for improved status handling and code formatting
a201345e16f32e9200af19d137b35f48e80a5e57 9f5f7d511acf2c3019ee5a6c4616e0fcf5a4eaeb Hoàn XPROZ <<EMAIL>> 1751796923 +0700	checkout: moving from main to fix/config
9f5f7d511acf2c3019ee5a6c4616e0fcf5a4eaeb 29b51f7bd511f57cddf8897740541cf740fc7c0e Hoàn XPROZ <<EMAIL>> 1751797768 +0700	merge main: Merge made by the 'ort' strategy.
29b51f7bd511f57cddf8897740541cf740fc7c0e b18fa488e11f711c7fa04aee0db7f45dcc28ed17 Hoàn XPROZ <<EMAIL>> 1751798000 +0700	commit: feat: enhance configuration and watermark components with new filters and improved code structure
b18fa488e11f711c7fa04aee0db7f45dcc28ed17 061ad51b7465c9a14e4c8af4b22098e7abacfcbd Hoàn XPROZ <<EMAIL>> 1751882208 +0700	checkout: moving from fix/config to main
061ad51b7465c9a14e4c8af4b22098e7abacfcbd 09eb3c8b988c5d49300af8a8cccf04c718ee41ee Hoàn XPROZ <<EMAIL>> 1751882221 +0700	checkout: moving from main to fix/0707
09eb3c8b988c5d49300af8a8cccf04c718ee41ee b760a822a5693c3a1758e11728661afde87ba257 Hoàn XPROZ <<EMAIL>> 1751882465 +0700	commit: fix: simplify error handling in ModalEditImage component
b760a822a5693c3a1758e11728661afde87ba257 67c827e0802918a510d1357efbebd68260f80539 Hoàn XPROZ <<EMAIL>> 1751901418 +0700	checkout: moving from fix/0707 to main
67c827e0802918a510d1357efbebd68260f80539 8135248c9fa88b4a19ae861cd1d005a939c0a350 Hoàn XPROZ <<EMAIL>> 1751906459 +0700	commit: fix: simplify error handling in ModalEditImage component
8135248c9fa88b4a19ae861cd1d005a939c0a350 fe153969d232fae86223f8ab3cb3075d71e43167 Hoàn XPROZ <<EMAIL>> 1751907028 +0700	commit: feat: refine issue filters in PaperPagePlan component for improved query handling
fe153969d232fae86223f8ab3cb3075d71e43167 8c44203306b812a04440ded270e4b112a690ddf9 Hoàn XPROZ <<EMAIL>> 1751940058 +0700	pull --ff --recurse-submodules --progress origin: Fast-forward
8c44203306b812a04440ded270e4b112a690ddf9 ff0f14a5e9973cdfed615faab6e7a790f594de75 Hoàn XPROZ <<EMAIL>> 1751959068 +0700	pull --ff --recurse-submodules --progress origin: Fast-forward
ff0f14a5e9973cdfed615faab6e7a790f594de75 b6eeb046eafd57b425f9a2ca4740f66a955ca844 Hoàn XPROZ <<EMAIL>> 1751959079 +0700	checkout: moving from main to fix/issue
b6eeb046eafd57b425f9a2ca4740f66a955ca844 9da72f2cb1117dcb3ccd4710861f339d79406663 Hoàn XPROZ <<EMAIL>> 1751959115 +0700	merge main: Merge made by the 'ort' strategy.
9da72f2cb1117dcb3ccd4710861f339d79406663 ff0f14a5e9973cdfed615faab6e7a790f594de75 Hoàn XPROZ <<EMAIL>> 1751962849 +0700	checkout: moving from fix/issue to main
ff0f14a5e9973cdfed615faab6e7a790f594de75 e81dc22252a8f8254109a248198b8f7aea9469cf Hoàn XPROZ <<EMAIL>> 1751962858 +0700	pull --ff --recurse-submodules --progress origin: Fast-forward
e81dc22252a8f8254109a248198b8f7aea9469cf 64ed90c0f1e4a54a7a1f713d818f7163b6339324 Hoàn XPROZ <<EMAIL>> 1751963413 +0700	commit: refactor: clean up imports and improve code formatting across multiple components
64ed90c0f1e4a54a7a1f713d818f7163b6339324 8c4465abb819fb39b0ce4cee9ba716921eddb161 Hoàn XPROZ <<EMAIL>> 1751996494 +0700	commit: feat: enhance issue filtering and improve query handling in Issue components
8c4465abb819fb39b0ce4cee9ba716921eddb161 3dd82a54a70238620cbb2b7e60635cf0fba48542 Hoàn XPROZ <<EMAIL>> 1752043813 +0700	pull --ff --recurse-submodules --progress origin: Fast-forward
3dd82a54a70238620cbb2b7e60635cf0fba48542 83c899a4307a4dc474508275352a47f88cb81dd0 Hoàn XPROZ <<EMAIL>> 1752329518 +0700	checkout: moving from main to autoComplete
83c899a4307a4dc474508275352a47f88cb81dd0 83c899a4307a4dc474508275352a47f88cb81dd0 Hoàn XPROZ <<EMAIL>> 1752376221 +0700	reset: moving to HEAD
83c899a4307a4dc474508275352a47f88cb81dd0 7240dbe04ec127f0e7aae763e02b7e4d440398af Hoàn XPROZ <<EMAIL>> 1752376222 +0700	pull --ff --recurse-submodules --progress origin: Fast-forward
7240dbe04ec127f0e7aae763e02b7e4d440398af 9ccc737fb74833eb51ca14994c9c04b7befaaf39 Hoàn XPROZ <<EMAIL>> 1752392744 +0700	pull --ff --recurse-submodules --progress origin: Fast-forward
9ccc737fb74833eb51ca14994c9c04b7befaaf39 1175182122270c3fce4d6742babb5fc26df1fac4 Hoàn XPROZ <<EMAIL>> 1752394321 +0700	pull --ff --recurse-submodules --progress origin: Fast-forward
1175182122270c3fce4d6742babb5fc26df1fac4 763b0289a71dba61fc7ee53bb96a69a8422f792c Hoàn XPROZ <<EMAIL>> 1752481638 +0700	checkout: moving from autoComplete to main
763b0289a71dba61fc7ee53bb96a69a8422f792c d202aa0da3633f6f44b2a03da4f2810273533318 Hoàn XPROZ <<EMAIL>> 1752481656 +0700	checkout: moving from main to addUser
d202aa0da3633f6f44b2a03da4f2810273533318 ea517ffdc40bdd7eef2993b7e4712c4cf49fa794 Hoàn XPROZ <<EMAIL>> 1752482234 +0700	pull --ff --recurse-submodules --progress origin: Fast-forward
ea517ffdc40bdd7eef2993b7e4712c4cf49fa794 4ce6c710ab872d27e4b2516cc4ad4475e325a819 Hoàn XPROZ <<EMAIL>> 1752483034 +0700	commit: refactor: streamline imports and enhance group query handling in FolderFile component
4ce6c710ab872d27e4b2516cc4ad4475e325a819 29c8bc777e932a6fc07c30235dc7c28703697953 Hoàn XPROZ <<EMAIL>> 1752725462 +0700	pull --ff --recurse-submodules --progress origin: Fast-forward
29c8bc777e932a6fc07c30235dc7c28703697953 65efcb20278af641ea42187a9de3d14cf6b4f7a0 Hoàn XPROZ <<EMAIL>> 1752726122 +0700	commit: refactor: clean up code formatting and streamline component structure across multiple files
65efcb20278af641ea42187a9de3d14cf6b4f7a0 9b9ad59785ad5d66ee831c4f94b28af27b9429ca Hoàn XPROZ <<EMAIL>> 1752726334 +0700	checkout: moving from addUser to main
9b9ad59785ad5d66ee831c4f94b28af27b9429ca e9c7a040ebba6196b1a322583d9e1cdb90852ff7 Hoàn XPROZ <<EMAIL>> 1752726341 +0700	pull --ff --recurse-submodules --progress origin: Fast-forward
e9c7a040ebba6196b1a322583d9e1cdb90852ff7 ab84d1618861aa9380aeb48e9c2763f65c175adf Hoàn XPROZ <<EMAIL>> 1752726406 +0700	checkout: moving from main to fix/1607
ab84d1618861aa9380aeb48e9c2763f65c175adf a31032c6307b5a4771d78d1a7ad1889005d5abe8 Hoàn XPROZ <<EMAIL>> 1752727907 +0700	commit: feat: enhance template management with improved type handling and search functionality
a31032c6307b5a4771d78d1a7ad1889005d5abe8 2bf4ce1586b34ab5e942cef92386f1f31f29d981 Hoàn XPROZ <<EMAIL>> 1752748658 +0700	checkout: moving from fix/1607 to main
2bf4ce1586b34ab5e942cef92386f1f31f29d981 b1382281659199f0ce2b7c1bdfbdd90e2424fb47 Hoàn XPROZ <<EMAIL>> 1752748665 +0700	pull --ff --recurse-submodules --progress origin: Fast-forward
b1382281659199f0ce2b7c1bdfbdd90e2424fb47 aed818b5b5c279b73682c8f8fe5ed3c830b80115 Hoàn XPROZ <<EMAIL>> 1752762988 +0700	checkout: moving from main to fix/1707
aed818b5b5c279b73682c8f8fe5ed3c830b80115 2055cbe2f8268a173942adde4f5b70917d54ca91 Hoàn XPROZ <<EMAIL>> 1752764834 +0700	commit: fix: streamline state management in ModalPressPublicationUpdate component
2055cbe2f8268a173942adde4f5b70917d54ca91 50f525e996c4579041a83a9129a8697a365faf3c Hoàn XPROZ <<EMAIL>> 1752821473 +0700	checkout: moving from fix/1707 to main
50f525e996c4579041a83a9129a8697a365faf3c 8101fb34443b869195aabf660aa54251a2acfe20 Hoàn XPROZ <<EMAIL>> 1752821517 +0700	checkout: moving from main to fix/1807
8101fb34443b869195aabf660aa54251a2acfe20 0c477e1c8c28ea78417258bffd67b172cdba8f8e Hoàn XPROZ <<EMAIL>> 1752829756 +0700	commit: fix: update ArticleRoyaltyType interfaces and improve query handling in related components
0c477e1c8c28ea78417258bffd67b172cdba8f8e 10cad7951e5c4c86bf6b5ae3b81b9de81454e604 Hoàn XPROZ <<EMAIL>> 1752853266 +0700	pull --ff --recurse-submodules --progress origin: Fast-forward
10cad7951e5c4c86bf6b5ae3b81b9de81454e604 191a4c3dd2563f3c71be1d9c689fe85e2d60883d Hoàn XPROZ <<EMAIL>> 1752854527 +0700	commit: fix: update ModalArticleRoyaltyTypeUpdate to use local state for royalty values and improve validation logic
191a4c3dd2563f3c71be1d9c689fe85e2d60883d 50f525e996c4579041a83a9129a8697a365faf3c Hoàn XPROZ <<EMAIL>> 1752854903 +0700	checkout: moving from fix/1807 to main
50f525e996c4579041a83a9129a8697a365faf3c e5dd08453fbf5753cc2595cc7c6b9bcab435200e Hoàn XPROZ <<EMAIL>> 1752854914 +0700	pull --ff --recurse-submodules --progress origin: Fast-forward
e5dd08453fbf5753cc2595cc7c6b9bcab435200e ecb28ad5a63ed7b6c2cfaffebea36a7e560e8a1e Hoàn XPROZ <<EMAIL>> 1752855740 +0700	commit: feat: enhance ArticlePage and SearchArticleForm components for improved department handling and query structure
ecb28ad5a63ed7b6c2cfaffebea36a7e560e8a1e f05c1a9e84405559dc3518a133229a1e97a12f53 Hoàn XPROZ <<EMAIL>> 1752908751 +0700	checkout: moving from main to fix/1907
f05c1a9e84405559dc3518a133229a1e97a12f53 acc44e9a51aab3823cbd83968171f44292432074 Hoàn XPROZ <<EMAIL>> 1752910124 +0700	commit: fix: improve code formatting and clean up unnecessary imports in article-related components
acc44e9a51aab3823cbd83968171f44292432074 9211e60a8ce376b2450ab7f7958cb3bd5fe87a6a Hoàn XPROZ <<EMAIL>> 1752935115 +0700	pull --ff --recurse-submodules --progress origin: Fast-forward
9211e60a8ce376b2450ab7f7958cb3bd5fe87a6a a4913ad9bfd6bd1844fdc4ed166b37ac18481d0f Hoàn XPROZ <<EMAIL>> 1752984586 +0700	checkout: moving from fix/1907 to main
a4913ad9bfd6bd1844fdc4ed166b37ac18481d0f a808fbc6733fac9d18704553d3a0edf041c42afd Hoàn XPROZ <<EMAIL>> 1752984588 +0700	pull --ff --recurse-submodules --progress origin: Fast-forward
a808fbc6733fac9d18704553d3a0edf041c42afd 48daf4993cd49c5c80613634e1ab2069f0cf99f5 Hoàn XPROZ <<EMAIL>> 1752984637 +0700	commit: feat: update ArticleFormContainer, CategoryList, and LayoutList to use OPERATION_NAME.CALL_STATIC_TOKEN for improved query handling
48daf4993cd49c5c80613634e1ab2069f0cf99f5 cbdb3bce439d77bf36912a742b7f695a0929d3d7 Hoàn XPROZ <<EMAIL>> 1752984769 +0700	checkout: moving from main to fix/2007
cbdb3bce439d77bf36912a742b7f695a0929d3d7 c90719fbdbd79261c1905ed7e3b25600f492dbd0 Hoàn XPROZ <<EMAIL>> 1752985011 +0700	commit: fix: simplify JSX structure and improve code formatting in multiple components
c90719fbdbd79261c1905ed7e3b25600f492dbd0 e0c23b95b46025990c731d638a5fe5b6445569bb Hoàn XPROZ <<EMAIL>> 1753072752 +0700	checkout: moving from fix/2007 to main
e0c23b95b46025990c731d638a5fe5b6445569bb f3342e15501798c853dbea71d584f30529933b29 Hoàn XPROZ <<EMAIL>> 1753072765 +0700	pull --ff --recurse-submodules --progress origin: Fast-forward
f3342e15501798c853dbea71d584f30529933b29 e258ffe06ca2016138ef544f4115e7e6ff546ea7 Hoàn XPROZ <<EMAIL>> 1753072793 +0700	checkout: moving from main to fix/2107
e258ffe06ca2016138ef544f4115e7e6ff546ea7 dbebbefdc0e22f846cce5c9305af8cc180641993 Hoàn XPROZ <<EMAIL>> 1753073139 +0700	commit: refactor: clean up code formatting and improve readability in multiple components
dbebbefdc0e22f846cce5c9305af8cc180641993 4c0110ae0bcb33f7c9f0013c9d11487a1cd075e9 Hoàn XPROZ <<EMAIL>> 1753090674 +0700	pull --ff --recurse-submodules --progress origin: Fast-forward
4c0110ae0bcb33f7c9f0013c9d11487a1cd075e9 ae8a623699b68dd2d83c8a85aee535ef6f10ad44 Hoàn XPROZ <<EMAIL>> 1753090993 +0700	commit: refactor: clean up code formatting and improve readability in HandleTableForRoyaltyTab, UpdateArticleForm, and UpdateArticleFormTabRoyalties components
ae8a623699b68dd2d83c8a85aee535ef6f10ad44 f3342e15501798c853dbea71d584f30529933b29 Hoàn XPROZ <<EMAIL>> 1753091267 +0700	checkout: moving from fix/2107 to main
f3342e15501798c853dbea71d584f30529933b29 97ac95e0c34599480b22f1f7c182fd7d9f583fcd Hoàn XPROZ <<EMAIL>> 1753104177 +0700	pull --ff --recurse-submodules --progress origin: Fast-forward
97ac95e0c34599480b22f1f7c182fd7d9f583fcd 627c04bed06e10199f7a0d0be9a318fb69bd5269 Hoàn XPROZ <<EMAIL>> 1753104323 +0700	checkout: moving from main to fix/2107
627c04bed06e10199f7a0d0be9a318fb69bd5269 627c04bed06e10199f7a0d0be9a318fb69bd5269 Hoàn XPROZ <<EMAIL>> 1753195079 +0700	reset: moving to HEAD
627c04bed06e10199f7a0d0be9a318fb69bd5269 dd32164bb67a1a97a897304ddeb1ff0fafc6993b Hoàn XPROZ <<EMAIL>> 1753195090 +0700	checkout: moving from fix/2107 to main
dd32164bb67a1a97a897304ddeb1ff0fafc6993b 727148b9ca12d7353854121140485d221110fadf Hoàn XPROZ <<EMAIL>> 1753195364 +0700	commit: feat: add radio article type and update translations in vi.json
727148b9ca12d7353854121140485d221110fadf 16c1380b47153f72b8fa56bc85f0ccf1ea902000 Hoàn XPROZ <<EMAIL>> 1753334678 +0700	pull --ff --recurse-submodules --progress origin: Fast-forward
16c1380b47153f72b8fa56bc85f0ccf1ea902000 cd6a0dce7a223d2667bac6af5a7ea60600b95d0d Hoàn XPROZ <<EMAIL>> 1753427935 +0700	pull --no-stat -v --progress origin main: Fast-forward
cd6a0dce7a223d2667bac6af5a7ea60600b95d0d 3a1a653f23cb78ad208b4c249a89b5dc53ad5cea Hoàn XPROZ <<EMAIL>> 1753457112 +0700	checkout: moving from main to feat/article-royalties
3a1a653f23cb78ad208b4c249a89b5dc53ad5cea e914e0ca391dc58a493b7115480b693b9f543a72 Hoàn XPROZ <<EMAIL>> 1753497739 +0700	checkout: moving from feat/article-royalties to main
e914e0ca391dc58a493b7115480b693b9f543a72 95d82a328fb0008074855970fe0a7fce67c1c421 Hoàn XPROZ <<EMAIL>> 1753497746 +0700	pull --ff --recurse-submodules --progress origin: Fast-forward
95d82a328fb0008074855970fe0a7fce67c1c421 ec21cdb4085b6145d243f94b2ae1509d03e19176 Hoàn XPROZ <<EMAIL>> 1753497892 +0700	commit: refactor: remove console log for articleRoyalties in UpdateArticleFormTabRoyalties
ec21cdb4085b6145d243f94b2ae1509d03e19176 8423a47dd2acfdca6a4a6418683b16d2ac710240 Hoàn XPROZ <<EMAIL>> 1753623996 +0700	pull --ff --recurse-submodules --progress origin: Fast-forward
8423a47dd2acfdca6a4a6418683b16d2ac710240 a59eb4a507446c5f6d7052280ea1a385fea062f1 Hoàn XPROZ <<EMAIL>> 1753624010 +0700	checkout: moving from main to feat/implement-article-royaly-users
a59eb4a507446c5f6d7052280ea1a385fea062f1 8423a47dd2acfdca6a4a6418683b16d2ac710240 Hoàn XPROZ <<EMAIL>> 1753674729 +0700	checkout: moving from feat/implement-article-royaly-users to main
8423a47dd2acfdca6a4a6418683b16d2ac710240 87430fdc40e1ae4cd76e6f49d736370e2688a7fb Hoàn XPROZ <<EMAIL>> 1753678600 +0700	commit: feat: update workflow types and enhance button visibility in ListWorkflow component
