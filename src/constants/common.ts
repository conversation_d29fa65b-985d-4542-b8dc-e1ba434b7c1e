import Action from '../types/Action';
import { SelectOption, SelectOptionModel } from '../types/common/Item';

export const REACT_APP_API_URL = import.meta.env.VITE_REACT_APP_API_URL ?? '';
export const REACT_APP_WATERMARK_URL = import.meta.env.VITE_WATERMARK_URL ?? ('' as const);
export const REACT_APP_CHAT_GPT_KEY = import.meta.env.VITE_CHAT_GPT_KEY ?? ('' as const);
export const REACT_APP_FILE_MAX_SIZE = import.meta.env.VITE_FILE_MAX_SIZE ?? (0 as const);
export const VIDEO_MAX_SIZE = import.meta.env.VITE_VIDEO_MAX_SIZE ?? (0 as const);
export const TINY_MCE_API_KEY = import.meta.env.VITE_TINY_MCE_KEY ?? ('' as const);
export const STATIC_TOKEN = import.meta.env.VITE_STATIC_TOKEN ?? ('' as const);

export const PAGINATION = {
    countItem: 0,
    totalPage: 1,
    currentPage: 1,
    limit: 10,
};

export enum QUERY_KEY {
    ACTIONS = 'actions',
    USERS = 'users',
    USER = 'user',
    PROFILE = 'profile',
    //USER_ACTIONS = 'user_actions',
    GROUPS = 'groups',
    // USER_GROUPS = 'user_groups',
    // GROUP_ACTIONS = 'group_action',
    LOGOUT = 'logout',
    DEPARTMENTS = 'departments',
    PSEUDONYMS = 'pseudonyms',
    WORKFLOWS = 'workflows',
    WORKFLOW_PERMISSIONS = 'workflow_permissions',
    WORKFLOW_PERMISSION_ARTICLE_TYPES = 'workflow_permission_article_types',
    TAGS = 'tags',
    CATEGORIES = 'categories',
    LAYOUTS = 'layouts',
    LAYOUT = 'layout',
    TEMPLATES = 'templates',
    PORTLETS = 'portlets',
    PORTLET = 'portlet',
    ARTICLES = 'articles',
    ARTICLE = 'article',
    CONFIGS = 'configs',
    ADVERTISES = 'advertises',
    ADVERTISE_ITEMS = 'advertise_items',
    ARTICLE_NOTES = 'article_notes',
    ARTICLE_LOGS = 'article_logs',
    ADVERTISE_ADV_ITEMS = 'advertise_adv_items',
    FILES = 'files',
    FOLDER_DETAIL = 'folder_detail',
    LOGIN_DEVICES = 'login_devices',
    FOLDERS = 'folders',
    NOTIFICATIONS = 'notifications',
    ARTICLE_CATEGORIES = 'article_categories',
    ISSUES = 'issues',
    ISSUE_PAGES = 'issue_pages',
    PRESS_PUBLICATIONS = 'press_publications',
    FILES_IN_ARTICLE = 'files_in_article',
    ISSUE_PAGE_FILES = 'issue_page_files',
    ISSUE_PAGE_FILE = 'issue_page_file',
    ISSUE_PAGE = 'issue_page',
    ARTICLE_ROYALTY_TYPES = 'article_royalty_types',
    ROYALTY_PARAMS = 'royalty_params',
    FILE_BY_URL = 'file_by_url',
}

export const OPERATION_NAME = {
    LOGIN: 'login',
    LOGOUT: 'logout',
    ACTION_LIST: 'action_list',
    ACTION_CREATE: 'action_create',
    ACTION_DELETE: 'action_delete',
    FORGOT_PASSWORD: 'forgot_password',
    CHANGE_PASSWORD: 'change_password',
    CALL_STATIC_TOKEN: 'call_static_token',
} as const;

export const FILTER_CONDITIONS = {
    EQUAL: '=',
    NOT_EQUAL: '!=',
    GREATER_THAN: '>',
    LESS_THAN: '<',
    GREATER_OR_EQUAL: '>=',
    LESS_OR_EQUAL: '<=',
    LIKE: '~',
    NOT_LIKE: '!~',
    IN: '[]',
    NOT_INT: '![]',
} as const;

export const PATH = {
    HOME: '/',
    NOT_FOUND: '/not-found',
} as const;

export const PAGE_NUMBER_DEFAULT = 1;
export const LIMIT_MAX = 999;
export const DEFAULT_IMAGE = '/assets/images/custom/default.png';
export const DEFAULT_COLOR = '#a42c48';

export const USER_MENU: Action[] = [
    {
        id: 999,
        name: 'Tài khoản',
        url: '',
        icon: 'users',
        display_order: 1,
        children: [
            { id: 990, name: 'Thông tin cá nhân', url: '/user/profile', icon: 'user', display_order: 1 },
            { id: 991, name: 'Thông báo cá nhân', url: '/notification', icon: 'bell', display_order: 1 },
            { id: 992, name: 'Tài nguyên của tôi', url: '/folder', icon: 'folder', display_order: 1 },
            { id: 993, name: 'Quản lý thiết bị', url: '/loginDevice', icon: 'activity', display_order: 1 },
            { id: 994, name: 'Quản lý bút danh', url: '/user/pseudonym', icon: 'pen-tool', display_order: 1 },
        ],
    },
];

export enum ARTICLE_TAB {
    INFORMATION = 'information',
    PUBLISH = 'publish',
    SEO = 'seo',
    ROYALTIES = 'royalties',
    MEDIA = 'media',
    EXCHANGE = 'exchange',
    HISTORY = 'history',
    OTHER = 'other',
    UTILITIES = 'utilities',
}

export const optionModelDefault: SelectOptionModel = { value: '', label: '' };
export const optionConstantDefault: SelectOption = { value: 0, label: '' };

export enum COMMON_MESSAGE {
    ERROR_MESSAGE = 'Có lỗi xảy ra trong quá trình thực hiện',
    SUCCESS_MESSAGE = 'Cập nhật thành công',
    PERMISSION_DENY = 'Bạn không có quyền thực hiện hành động này',
    EXPIRED_TOKEN = 'Phiên làm việc của bạn đã hết hạn, vui lòng đăng nhập lại',
    FIELD_REQUIRED = 'Trường này không được bỏ trống',
    FIELD_PASSWORD_LENGTH = 'Mật khẩu ít nhất 6 ký tự',
    FIELD_PASSWORD_MATCH = 'Mật khẩu không giống nhau',
    FIELD_EMAIL = 'Email không hợp lệ',
    FIELD_PHONE = 'Số điện thoại không hợp lệ',
    FIELD_NUMBER = 'Trường này phải là số',
    MIN_NUMBER = 'Giá trị bé nhất là ',
    MAX_NUMBER = 'Giá trị lớn nhất nhất là ',
    FIELD_DATE = 'Ngày tháng không hợp lệ',
    FIELD_REQUIRED_NAME = ' không được bỏ trống',
    FIELD_NUMBER_POSITIVE = 'Trường này phải lớn hơn 0',
    DELETE_CONFIRM = 'Bạn có thực sự muốn xoá đối tượng này?',
    ACTION_CONFIRM = 'Bạn có thực sự muốn thực hiện hành động này?',
}
