import { yupResolver } from '@hookform/resolvers/yup';
import classNames from 'classnames';
import UpdateButton from 'components/partials/UpdateButton';
import { useEffect, useLayoutEffect } from 'react';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { ItemParam, ItemParamType } from 'types/common/Item';
import { toggleModalOpen } from 'utils/common';
import * as yup from 'yup';

interface IProps {
    itemTypeId: ItemParamType;
    show: boolean;
    itemParam: ItemParam | undefined;
    isLoading: boolean;
    changeShow: (s: boolean) => void;
    submitAction: (data: ItemParam) => void;
}

export default function ModalItemParamUpdate({
    itemTypeId,
    show,
    itemParam,
    isLoading,
    changeShow,
    submitAction,
}: Readonly<IProps>) {
    const { t } = useTranslation();
    useLayoutEffect(() => toggleModalOpen(show), [show]);
    const schema = yup
        .object({
            name: yup.string().required(t('error.required')).trim(),
        })
        .required();
    const {
        register,
        handleSubmit,
        reset,
        formState: { errors },
    } = useForm<ItemParam>({
        resolver: yupResolver(schema),
    });

    useEffect(() => {
        if (itemParam && show) {
            reset(itemParam);
        } else {
            reset({
                name: '',
                desc: '',
            });
        }
    }, [itemParam, show, reset]);

    return (
        <>
            <div
                className={classNames('modal fade text-start modal-primary', { show })}
                style={{ display: show ? 'block' : 'none' }}
            >
                <div className="modal-dialog modal-dialog-centered modal-lg">
                    <div className="modal-content">
                        <div className="modal-header">
                            <h5 className="modal-title">{t(`${itemTypeId}.${itemParam ? 'edit' : 'add'}`)}</h5>
                            <button type="button" className="btn-close" onClick={() => changeShow(false)} />
                        </div>
                        <form className="validate-form pt-50" onSubmit={handleSubmit(submitAction)}>
                            <div className="modal-body">
                                <div className="row">
                                    <div className="col-12 mb-1">
                                        <label className="form-label">
                                            {t(`${itemTypeId}.single`)} <span className="error">*</span>
                                        </label>
                                        <input
                                            {...register('name')}
                                            type="text"
                                            className={classNames('form-control', {
                                                'is-invalid': Boolean(errors.name?.message),
                                            })}
                                        />
                                        <span className="error">{errors.name?.message}</span>
                                    </div>
                                    <div className="col-12 mb-1">
                                        <label className="form-label">Mô tả</label>
                                        <input type="text" {...register('desc')} className="form-control" />
                                    </div>
                                </div>
                            </div>
                            <div className="modal-footer">
                                <UpdateButton btnText="Cập nhật" isLoading={isLoading} hasDivWrap={false} />
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            {show && <div className="modal-backdrop fade show" />}
        </>
    );
}
