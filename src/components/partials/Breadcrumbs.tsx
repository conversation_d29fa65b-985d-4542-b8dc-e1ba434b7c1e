import classNames from 'classnames';
import { Link } from 'react-router-dom';
import { ItemLink } from 'types/common/Item';

interface IProps {
    breadcrumbs: ItemLink[];
}

export default function Breadcrumbs({ breadcrumbs }: Readonly<IProps>) {
    return (
        <div className="breadcrumb-wrapper h-100 d-flex items-center">
            <ol className="breadcrumb p-0">
                {breadcrumbs.map((menu: ItemLink, index: number) => (
                    <li
                        className={classNames('breadcrumb-item', {
                            active: index === breadcrumbs.length - 1,
                        })}
                        key={index}
                    >
                        {menu.to ? <Link to={menu.to}>{menu.text}</Link> : menu.text}
                    </li>
                ))}
            </ol>
        </div>
    );
}
