import ClassicEditor from '@ckeditor/ckeditor5-build-classic';
import { CKEditor } from '@ckeditor/ckeditor5-react';
import { forwardRef, useImperativeHandle, useRef } from 'react';
import FileService from 'services/FileService';

interface MyUploadAdapterProps {
    loader: any; // tslint:disable-line
    uploadImageToAPI: (file: File) => Promise<string>;
}

class MyUploadAdapter {
    loader: any; // tslint:disable-line
    uploadImageToAPI: (file: File) => Promise<string>;

    constructor({ loader, uploadImageToAPI }: MyUploadAdapterProps) {
        this.loader = loader;
        this.uploadImageToAPI = uploadImageToAPI;
    }

    upload() {
        return this.loader.file.then(
            (file: File) =>
                this.uploadImageToAPI(file).then((url: string) => {
                    this.loader.uploaded = true;
                    return { default: url };
                })
            //.catch((err) => console.error(err))
        );
    }

    abort() {
        //TODO: cancel upload
    }
}

const uploadImageToAPI = async (file: File) => {
    const response = await FileService.upload(file, 'image', 1);
    return response.upload.url;
};

export interface EditorComponentRef {
    getContent: () => string;
}

interface EditorComponentProps {
    initialData?: string;
    onReady?: (editor: any) => void; // tslint:disable-line
}

const EditorComponent = forwardRef<EditorComponentRef, EditorComponentProps>(function EditorComponent(props, ref) {
    const editorRef = useRef<any>(null); // tslint:disable-line

    useImperativeHandle(ref, () => ({
        getContent: () => {
            if (editorRef.current?.editor) {
                return editorRef.current.editor.getData();
            }
            return '';
        },
    }));

    return (
        <div>
            <CKEditor
                editor={ClassicEditor}
                data={props.initialData ?? ''}
                onReady={(editor) => {
                    editorRef.current = { editor };
                    editor.plugins.get('FileRepository').createUploadAdapter = (loader) =>
                        new MyUploadAdapter({ loader, uploadImageToAPI });
                    const rootElement = editor.editing.view.document.getRoot();
                    if (rootElement) {
                        editor.editing.view.change((writer) => {
                            writer.setStyle('height', '500px', rootElement);
                        });
                    }
                    if (props.onReady) {
                        props.onReady(editor);
                    }
                }}
                config={{
                    simpleUpload: {
                        uploadUrl: '',
                        withCredentials: true,
                        headers: {
                            'X-CSRF-TOKEN': 'CSFR-Token',
                        },
                    },
                }}
            />
        </div>
    );
});

export default EditorComponent;
