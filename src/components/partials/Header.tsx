import classNames from 'classnames';
import { DEFAULT_IMAGE, USER_MENU } from 'constants/common';
import { includes, isEmpty } from 'lodash';
import { useEffect, useLayoutEffect, useState } from 'react';
import { ChevronLeft, Circle, Disc, Grid, Menu, Moon, Plus, Power, Sun } from 'react-feather';
import PerfectScrollbar from 'react-perfect-scrollbar';
import 'react-perfect-scrollbar/dist/css/styles.css';
import { Link, NavLink, useLocation } from 'react-router-dom';
import Action from 'types/Action';
import User, { UserRole, UserRoleNames } from 'types/User';
import { getIcon } from 'utils/actionIcon';
import { getFieldInArrayObject } from 'utils/common';
import { getLocalStorage, setLocalStorage } from 'utils/localStorage';
import DepartmentSwitch from './DepartmentSwitch';
import { getFlatDepartments } from '../../services/DepartmentService';
import { ArticleTypeNames } from '../../types/common/Item';
import { useTranslation } from 'react-i18next';

interface IProps {
    actions: Action[];
    user: User | null | undefined;
    handleLogout: () => void;
    isHiddenSidebar: boolean;
    isHiddenHeader: boolean;
}

export default function Header({ actions, user, handleLogout, isHiddenSidebar, isHiddenHeader }: Readonly<IProps>) {
    const { t } = useTranslation();
    const [onScrollMenu, setOnScrollMenu] = useState(false);
    const [collapsedMenu, setCollapsedMenu] = useState(true);
    const [fixedMenu, setFixedMenu] = useState(!getLocalStorage('prp_fixed_menu'));
    const [openActionIds, setOpenActionIds] = useState<string[]>([]);
    const [openDropdown, setOpenDropdown] = useState<'user' | 'addArticle' | 'department' | null>(null);
    const location = useLocation();

    useLayoutEffect(() => {
        document.body.classList.remove('blank-page');
        document.body.classList.add('menu-expanded');
    }, [actions]);

    useLayoutEffect(() => {
        const menuElement = document.getElementById('main-menu-navigation');
        if (menuElement) {
            Array.from(menuElement.getElementsByClassName('nav-item')).forEach((element) => {
                Array.from(element.getElementsByTagName('a')).forEach((aEle) => {
                    if (aEle.classList.contains('active')) {
                        if (element.getElementsByClassName('menu-content').length > 0) {
                            aEle.closest('li')?.classList.add('active');
                            element.classList.add('sidebar-group-active');
                            element.classList.add('has-sub');
                            element.classList.add('open');
                        } else {
                            element.classList.add('active');
                        }
                        return false;
                    } else {
                        aEle.closest('li')?.classList.remove('active');
                    }
                });
            });
        }
    });

    useLayoutEffect(() => {
        if (fixedMenu) expandedMenuFn();
        else collapsedMenuFn();
        setLocalStorage('prp_fixed_menu', fixedMenu ? '' : '1');
    }, [fixedMenu]);

    useLayoutEffect(() => {
        function changBodyClass() {
            if (window.innerWidth < 1200) {
                document.body.classList.add('vertical-overlay-menu');
                document.body.classList.add('menu-close');
                document.body.classList.remove('menu-collapsed');
                document.body.classList.remove('menu-expanded');
            } else {
                document.body.classList.remove('vertical-overlay-menu');
                document.body.classList.remove('menu-close');
            }
        }

        window.addEventListener('resize', changBodyClass);
        changBodyClass();
        return () => window.removeEventListener('resize', changBodyClass);
    }, []);

    const toggleMenuItem = (id: string) => {
        const items = [...openActionIds];
        if (includes(items, id)) {
            const index = items.indexOf(id);
            if (index > -1) items.splice(index, 1);
        } else items.push(id);
        setOpenActionIds(items);
    };

    const handleToggleMenu = () => {
        if (document.body.classList.contains('menu-close')) {
            document.body.classList.remove('menu-close');
            document.body.classList.add('menu-open');
            document.body.classList.remove('vertical-menu-modern');
        } else {
            document.body.classList.remove('menu-open');
            document.body.classList.add('menu-close');
            document.body.classList.add('vertical-menu-modern');
        }
    };

    const enterMenu = () => {
        if (!fixedMenu && window.innerWidth >= 1200) {
            expandedMenuFn();
        }
    };

    const leaveMenu = () => {
        if (!fixedMenu && window.innerWidth >= 1200) {
            collapsedMenuFn();
        }
    };

    const expandedMenuFn = () => {
        document.body.classList.remove('menu-collapsed');
        document.body.classList.add('menu-expanded');
        setCollapsedMenu(false);
    };

    const collapsedMenuFn = () => {
        document.body.classList.remove('menu-expanded');
        document.body.classList.add('menu-collapsed');
        setCollapsedMenu(true);
    };

    // Lưu lại darkmode
    const [darkMode, setDarkMode] = useState(() => {
        const stored = localStorage.getItem('darkMode');
        return stored ? JSON.parse(stored) : false;
    });

    useEffect(() => {
        localStorage.setItem('darkMode', JSON.stringify(darkMode));

        if (darkMode) {
            document.documentElement.classList.add('dark-layout');
        } else {
            document.documentElement.classList.remove('dark-layout');
        }
    }, [darkMode]);

    // Tự động đóng menu khi sang tab khác
    useEffect(() => {
        document.body.classList.remove('menu-open');
        document.body.classList.add('menu-close');
        document.body.classList.add('vertical-menu-modern');
    }, [location]);

    // Chỉ mở 1 trong 3 dropdown cùng lúc
    const toggleUser = () => {
        setOpenDropdown((prev) => (prev === 'user' ? null : 'user'));
    };

    const toggleAddArticle = () => {
        setOpenDropdown((prev) => (prev === 'addArticle' ? null : 'addArticle'));
    };

    const toggleDepartment = () => {
        setOpenDropdown((prev) => (prev === 'department' ? null : 'department'));
    };

    return (
        <>
            <nav
                className={classNames(
                    'header-navbar navbar navbar-expand-lg align-items-center  navbar-light navbar-shadow container-xxl !m-0 !rounded-none !shadow-xs !w-[calc(100vw-(100vw-100%))] !max-w-[999999px]',
                    { 'pc:!w-[calc(100vw-(100vw-100%)-260px)] !fixed top-0': !isHiddenSidebar },
                    { 'floating-nav': !isHiddenSidebar },
                    { hidden: isHiddenHeader }
                )}
            >
                <div className="navbar-container d-flex content">
                    <div className="bookmark-wrapper d-flex align-items-center">
                        <ul
                            className={classNames('nav navbar-nav d-xl-none', {
                                'd-none': isHiddenSidebar,
                            })}
                        >
                            <li className="nav-item">
                                <a className="nav-link menu-toggle" onClick={handleToggleMenu}>
                                    <Menu size={14} />
                                </a>
                            </li>
                        </ul>
                        <DepartmentSwitch
                            items={getFlatDepartments((user?.userDepartments ?? []).map((item) => item.department))}
                            isOpen={openDropdown === 'department'}
                            onToggle={toggleDepartment}
                        />
                    </div>
                    <ul className="nav navbar-nav align-items-center ms-auto flex-nowrap">
                        {/* Nút Bài viết mới */}
                        <li className="nav-item btn-group dropdown cursor-pointer d-flex align-items-center gap-2">
                            <Link
                                to="/article/add/personal/1"
                                type="button"
                                className="waves-effect waves-light items-center flex btn btn-sm bg-[#FCF3F5] rounded-[4px] text-[#A42D49] sm:text-sm md:text-base"
                            >
                                <Plus size={14} className="align-bottom md:me-1" />
                                Bài viết mới
                            </Link>
                        </li>
                        <li className="nav-item d-lg-block">
                            <a
                                className="nav-link nav-link-style"
                                onClick={() => setDarkMode((prevDarkMode: boolean) => !prevDarkMode)}
                            >
                                {darkMode ? <Sun size={14} /> : <Moon size={14} />}
                            </a>
                        </li>

                        {/* User + Avatar */}
                        <li className="nav-item btn-group dropdown">
                            <button
                                type="button"
                                className="d-flex align-items-center gap-2 dropdown-toggle waves-effect waves-light"
                                data-bs-toggle="dropdown"
                                aria-expanded="false"
                                data-trigger="hover"
                            >
                                <div className="d-none d-sm-flex flex-column text-end">
                                    <span className="fw-bolder">{user?.full_name}</span>
                                    <span className="text-muted small">
                                        {getFieldInArrayObject(UserRoleNames, user?.role_id ?? UserRole.ADMIN)}
                                    </span>
                                </div>

                                {/* Avatar */}
                                <span className="avatar position-relative">
                                    <img
                                        className="rounded-circle"
                                        src={user?.avatar?.file_url ?? DEFAULT_IMAGE}
                                        alt="avatar"
                                        height={40}
                                        width={40}
                                    />
                                    <span
                                        className="avatar-status-online position-absolute bottom-0 end-0 bg-success rounded-circle border border-white"
                                        style={{ width: 10, height: 10 }}
                                    />
                                </span>
                            </button>

                            {/* Dropdown */}
                            <ul className="dropdown-menu">
                                <li>
                                    <Link
                                        to="/"
                                        className="dropdown-item waves-effect"
                                        onClick={(e) => {
                                            e.preventDefault();
                                            handleLogout();
                                        }}
                                    >
                                        <span className="me-2">
                                            <Power size={14} />
                                        </span>
                                        Đăng xuất
                                    </Link>
                                </li>
                            </ul>
                        </li>
                    </ul>
                </div>
            </nav>
            <div
                className={classNames('main-menu menu-fixed menu-light menu-accordion menu-shadow top-0', {
                    expanded: !collapsedMenu,
                    'd-none': isHiddenSidebar,
                })}
                onMouseEnter={() => {
                    setCollapsedMenu(false);
                }}
                onMouseLeave={() => {
                    setCollapsedMenu(true);
                }}
            >
                <div className="navbar-header !pl-[15px]">
                    <ul className="flex-row nav navbar-nav gap-x-2">
                        <li className="flex-1 nav-item me-auto">
                            <Link className="flex items-center gap-x-1" to="/dashboard">
                                <img src="/assets/images/svg/logo.svg" className="h-[60px]" alt="" />
                                <span className="text-[#363962] text-[24px] font-serif">MEDIASOFT</span>
                            </Link>
                        </li>
                        <li className="nav-item nav-toggle pc:hidden">
                            <a className="nav-link modern-nav-toggle pe-0">
                                <span
                                    className="d-block d-xl-none text-primary toggle-icon font-medium-4"
                                    onClick={handleToggleMenu}
                                >
                                    <ChevronLeft className="text-[#363962] text-base" />
                                </span>
                                <span
                                    className="d-none d-xl-block collapse-toggle-icon font-medium-4 text-primary"
                                    onClick={() => setFixedMenu((prevFixedMenu) => !prevFixedMenu)}
                                >
                                    {!fixedMenu && (
                                        <span>
                                            <Circle size={14} />
                                        </span>
                                    )}
                                    {fixedMenu && (
                                        <span>
                                            <Disc size={14} />
                                        </span>
                                    )}
                                </span>
                            </a>
                        </li>
                    </ul>
                </div>
                <div className={classNames('shadow-bottom', { onScrollMenu })} />
                <PerfectScrollbar
                    className="main-menu-content"
                    onScrollY={() => setOnScrollMenu((prevOnScrollMenu) => !prevOnScrollMenu)}
                >
                    <ul className="navigation navigation-main" id="main-menu-navigation" data-menu="menu-navigation">
                        <li className="nav-item">
                            <NavLink to="/dashboard" className="d-flex align-items-center">
                                <Grid size={14} />
                                <span className="menu-title text-truncate">Bảng điều khiển</span>
                            </NavLink>
                        </li>
                        {[...USER_MENU, ...(actions ?? [])].map((action) => (
                            <li
                                className={classNames('nav-item', {
                                    'has-sub': !isEmpty(action.children),
                                    open:
                                        !isEmpty(action.children) &&
                                        includes(openActionIds, action.id?.toString() ?? ''),
                                })}
                                key={action.id}
                                onClick={() => toggleMenuItem(action.id?.toString() ?? '')}
                            >
                                {!isEmpty(action.children) && (
                                    <>
                                        <a className="d-flex align-items-center">
                                            {getIcon(action.icon)}
                                            <span className="menu-title text-truncate">{action.name}</span>
                                        </a>
                                        <ul className="menu-content">
                                            {action.children!.map((children) => (
                                                <li key={children.id}>
                                                    <NavLink
                                                        to={
                                                            children.url === '/user/pseudonym'
                                                                ? `/user/pseudonym/${user?.id}`
                                                                : children.url
                                                        }
                                                        className="d-flex align-items-center"
                                                    >
                                                        {getIcon(children.icon)}
                                                        <span className="menu-title text-truncate">
                                                            {children.name}
                                                        </span>
                                                    </NavLink>
                                                </li>
                                            ))}
                                        </ul>
                                    </>
                                )}
                                {!action.parent_id && action.children?.length === 0 && (
                                    <NavLink to={action.url} className="d-flex align-items-center">
                                        {getIcon(action.icon)}
                                        <span className="menu-title text-truncate">{action.name}</span>
                                    </NavLink>
                                )}
                            </li>
                        ))}
                    </ul>
                </PerfectScrollbar>
            </div>
        </>
    );
}
