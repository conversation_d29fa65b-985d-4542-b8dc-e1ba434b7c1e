import classNames from 'classnames';
import { ChangeEvent, DragEvent, useEffect, useRef, useState } from 'react';
import { AlertCircle, UploadCloud, X } from 'react-feather';
import { FileWithTitle } from '../ModalEditImage/ModalEditImage';

const ALLOWED_TYPES = {
    image: ['image/png', 'image/jpg', 'image/jpeg', 'image/gif'],
    video: [
        'video/mp4',
        'video/webm',
        'video/ogg',
        'video/avi',
        'video/mov',
        'video/wmv',
        'video/flv',
        'video/mkv',
        'video/m4v',
        'video/3gp',
    ],
};

interface Action {
    title: string;
    onClick: () => void;
    icon?: React.ReactNode;
}

interface IProps {
    type?: 'image' | 'video';
    onFileChange: (file: File) => void;
    percent: number;
    uploadSuccess?: boolean; // Prop để biết upload có thành công không
    onClearPreview?: (index: number) => void;
    onSaveTitle?: (title: string, fileId: number) => void; // Callback khi user click "Lưu"
    uploadedFiles?: FileWithTitle[]; // List files đã upload từ ModalEditImage
    isSavingTitle?: boolean; // Loading state cho button "Lưu"
    onTitleChange?: (index: number, title: string) => void; // Callback khi title thay đổi
    isAllChecked?: boolean; // Trạng thái check all
    onCheckAllChange?: (checked: boolean) => void; // Callback khi check all thay đổi
    onFileCheckboxChange?: (index: number, checked: boolean) => void; // Callback khi checkbox file thay đổi
}

export default function FileUploadImage({
    type = 'image',
    onFileChange,
    percent,
    uploadSuccess = false,
    onClearPreview,
    onSaveTitle,
    uploadedFiles = [],
    isSavingTitle = false,
    onTitleChange,
    isAllChecked = false,
    onCheckAllChange,
    onFileCheckboxChange,
}: Readonly<IProps>) {
    const inputRef = useRef<HTMLInputElement>(null);
    const [error, setError] = useState<string | null>(null);
    const [dragActive, setDragActive] = useState(false);

    const validateFile = (file: File): boolean => {
        setError(null);

        const maxSize = type === 'video' ? 50 * 1024 * 1024 : 10 * 1024 * 1024; // 50MB for video, 10MB for image
        if (file.size > maxSize) {
            const maxSizeText = type === 'video' ? '50MB' : '10MB';
            setError(`Dung lượng file phải nhỏ hơn ${maxSizeText}`);
            return false;
        }

        return true;
    };

    const onChooseFile = () => inputRef.current?.click();

    // Cleanup URLs khi component unmount
    useEffect(
        () => () => {
            uploadedFiles.forEach((fileData) => {
                if (fileData.previewUrl.startsWith('blob:')) {
                    URL.revokeObjectURL(fileData.previewUrl);
                }
            });
        },
        [uploadedFiles]
    );

    const handleClearPreview = (index: number) => {
        if (onClearPreview) onClearPreview(index);
    };

    const handleFile = (file: File): void => {
        if (validateFile(file)) {
            onFileChange(file);
        }
    };

    const handleTitleChange = (index: number, title: string) => {
        // Notify parent component about title change
        if (onTitleChange) {
            onTitleChange(index, title);
        }
    };

    const handleSaveTitle = (index: number) => {
        const fileData = uploadedFiles[index];
        if (fileData && onSaveTitle && fileData.title.trim()) {
            onSaveTitle(fileData.title.trim(), fileData.id);
        }
    };

    const handleChange = (e: ChangeEvent<HTMLInputElement>) => {
        e.preventDefault();
        if (e.target.files) {
            const files = Array.from(e.target.files);
            files.forEach((file) => {
                handleFile(file);
            });
        }
    };

    const handleDrop = (e: DragEvent<HTMLDivElement>) => {
        e.preventDefault();
        e.stopPropagation();
        setDragActive(false);
        if (e.dataTransfer.files) {
            const files = Array.from(e.dataTransfer.files);
            files.forEach((file) => {
                handleFile(file);
            });
        }
    };

    const handleDrag = (e: DragEvent<HTMLDivElement>) => {
        e.preventDefault();
        e.stopPropagation();
        if (e.type === 'dragenter' || e.type === 'dragover') {
            setDragActive(true);
        } else {
            setDragActive(false);
        }
    };

    return (
        <div className="w-full mx-auto">
            <div
                className={classNames(`relative border-2 border-dashed rounded-lg p-6`, {
                    'border-blue-500 bg-blue-50': dragActive,
                    'border-gray-300': !dragActive,
                })}
                onDragEnter={handleDrag}
                onDragLeave={handleDrag}
                onDragOver={handleDrag}
                onDrop={handleDrop}
            >
                <input
                    type="file"
                    className="hidden"
                    ref={inputRef}
                    accept={ALLOWED_TYPES[type].join(',')}
                    onChange={handleChange}
                    multiple
                />
                <div className="text-center">
                    <UploadCloud className="mx-auto h-12 w-12 text-gray-400" />
                    <div className="mt-4">
                        {'Kéo thả file vào đây hoặc '}
                        <button type="button" className="text-primary" onClick={onChooseFile}>
                            nhấn chọn file
                        </button>
                    </div>
                    <p className="mt-2 text-sm text-gray-500">
                        {type === 'image' && 'Hỗ trợ các định dạng: PNG, JPG, JPEG, GIF'}
                        {type === 'video' && 'Hỗ trợ các định dạng: MP4, WebM, OGG, AVI, MOV'}
                    </p>
                </div>

                {error && (
                    <div className="mt-4 text-red-500 d-flex items-center gap-2">
                        <AlertCircle className="h-4 w-4" />
                        <span>{error}</span>
                    </div>
                )}
            </div>
            {uploadedFiles.length > 0 && (
                <>
                    {/* Check all checkbox for image */}
                    {type === 'image' && (
                        <div className="flex items-center gap-2 py-2 border-b">
                            <input
                                type="checkbox"
                                id="check-all"
                                checked={isAllChecked}
                                onChange={(e) => onCheckAllChange?.(e.target.checked)}
                                className="form-check-input"
                            />
                            <label htmlFor="check-all" className="form-check-label">
                                Chọn tất cả ({uploadedFiles.length} ảnh)
                            </label>
                        </div>
                    )}

                    {uploadedFiles.map((fileData, idx) => (
                        <div key={idx} className="h-full flex justify-between items-center gap-1 py-4">
                            {type === 'video' ? (
                                <video
                                    src={fileData.previewUrl}
                                    className="w-[100px] h-[100px] min-w-[100px] min-h-[100px] object-cover"
                                    muted
                                    preload="metadata"
                                />
                            ) : (
                                <>
                                    <input
                                        type="checkbox"
                                        checked={fileData.isChecked}
                                        onChange={(e) => onFileCheckboxChange?.(idx, e.target.checked)}
                                        className="form-check-input"
                                    />
                                    <img
                                        src={fileData.previewUrl}
                                        className="w-[100px] h-[100px] min-w-[100px] min-h-[100px] object-cover"
                                        alt={`Preview ${idx + 1}`}
                                    />
                                </>
                            )}
                            <div className="w-[50%]">
                                {type === 'image' && (
                                    <div className="flex">
                                        <input
                                            type="text"
                                            className="form-control w-full"
                                            placeholder="Nhập chú thích"
                                            value={fileData.title}
                                            onChange={(e) => handleTitleChange(idx, e.target.value)}
                                        />
                                        {/* <button
                                            type="button"
                                            className="btn btn-sm btn-primary"
                                            disabled={!fileData.title.trim() || isSavingTitle}
                                            onClick={() => onSaveTitle?.(fileData.title, fileData.id)}
                                        >
                                            {isSavingTitle ? 'Đang lưu...' : 'Lưu'}
                                        </button> */}
                                    </div>
                                )}
                                <div className="flex flex-col">
                                    <div className="w-max self-end">{percent}%</div>
                                    <div className="relative w-full h-2 bg-[#FCF3F5FF] rounded-md">
                                        <div
                                            className="absolute left-0 top-0 h-full bg-[#A42D49FF] rounded-md"
                                            style={{ width: `${percent}%` }}
                                        ></div>
                                    </div>
                                </div>
                            </div>
                            <X className="cursor-pointer" onClick={() => handleClearPreview(idx)} />
                        </div>
                    ))}
                </>
            )}
        </div>
    );
}
