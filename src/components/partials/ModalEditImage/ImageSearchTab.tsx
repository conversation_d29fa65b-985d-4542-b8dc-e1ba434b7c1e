import { debounce } from 'lodash';
import { ChangeEvent, useEffect, useMemo, useState } from 'react';
import { Download, MoreHorizontal } from 'react-feather';
import { FileData } from 'types/UploadFile';
import { DataList } from '../../../types/common';
import ModalConfirm from '../ModalConfirm';
import PaginationTable from '../PaginationTable';
import { FileCategory } from './ModalEditImage';

interface SearchFilters {
    startDate: string;
    endDate: string;
    caption: string;
    hashtag: string;
}

interface IProps {
    listFileCategories: Record<number, string>;
    listFile: DataList<FileData> | undefined;
    imageActive: number;
    onChooseImage: (image: FileData) => void;
    handlePageChange: (event: ChangeEvent<unknown>, page: number) => void;
    fileCategory: number;
    setFileCategory: (category: number) => void;
    searchFilters: SearchFilters;
    setSearchFilters: (filters: SearchFilters) => void;
    filesInArticle: FileData[];
    selectedImages: { id: number; url: string; order: number }[];
    onImageSelect: (image: FileData) => void;
    getImageOrder: (imageId: number) => number;
    onDeleteImage: (imageId: number) => void;
    deletePending: boolean;
}

export default function ImageSearchTab({
    listFileCategories,
    listFile,
    imageActive,
    onChooseImage,
    handlePageChange,
    fileCategory,
    setFileCategory,
    searchFilters,
    setSearchFilters,
    filesInArticle,
    selectedImages,
    onImageSelect,
    getImageOrder,
    onDeleteImage,
    deletePending,
}: Readonly<IProps>) {
    const [openDropdown, setOpenDropdown] = useState<number | null>(null);
    const [localFilters, setLocalFilters] = useState(searchFilters);
    const [fileGenerated, setFileGenerated] = useState<FileData[]>([]);
    const [imgDeleteId, setImgDeleteId] = useState<number | null>(null);
    const [showDeleteModal, setShowDeleteModal] = useState(false);

    useEffect(() => {
        if (fileCategory === FileCategory.IN_ARTICLE) {
            setFileGenerated(filesInArticle);
        } else {
            setFileGenerated(listFile?.data ?? []);
        }
    }, [fileCategory, filesInArticle, listFile]);

    const debouncedSetSearchFilters = useMemo(
        () =>
            debounce((filters: SearchFilters) => {
                setSearchFilters(filters);
            }, 800),
        [setSearchFilters]
    );

    useEffect(() => {
        debouncedSetSearchFilters(localFilters);
    }, [localFilters, debouncedSetSearchFilters]);

    useEffect(
        () => () => {
            debouncedSetSearchFilters.cancel();
        },
        [debouncedSetSearchFilters]
    );

    const handleCategoryChange = (category: number) => {
        setFileCategory(category);
    };

    const handleFilterChange = (field: keyof SearchFilters, value: string) => {
        setLocalFilters((prev) => ({
            ...prev,
            [field]: value,
        }));
    };

    // This function is now handled by parent component

    const handleDropdownToggle = (imageId: number) => {
        setOpenDropdown(openDropdown === imageId ? null : imageId);
    };

    const handleDownload = async (image: FileData) => {
        if (image.file_url) {
            try {
                const response = await fetch(image.file_url);
                const blob = await response.blob();

                const blobUrl = window.URL.createObjectURL(blob);

                const link = document.createElement('a');
                link.href = blobUrl;
                link.download = image.file_name || 'image';
                document.body.appendChild(link);
                link.click();

                document.body.removeChild(link);
                window.URL.revokeObjectURL(blobUrl);
            } catch (error) {
                const link = document.createElement('a');
                link.href = image.file_url;
                link.download = image.file_name || 'image';
                link.click();
            }
        }
        setOpenDropdown(null);
    };

    const handleDelete = (imageId: number) => {
        setOpenDropdown(null);
        setImgDeleteId(imageId);
        setShowDeleteModal(true);
    };

    const handleSubmitDelete = () => {
        if (imgDeleteId) {
            onDeleteImage(imgDeleteId);
        }
        setShowDeleteModal(false);
    };

    return (
        <>
            <div className="space-y-4">
                {/* Filter Section */}
                <div className="space-y-4">
                    {/* Category Radio Buttons */}
                    <div className="flex gap-6">
                        {Object.entries(listFileCategories).map(([key, value]) => (
                            <label key={key} className="flex items-center gap-2 cursor-pointer">
                                <input
                                    type="radio"
                                    name="category"
                                    value={key}
                                    checked={fileCategory === parseInt(key)}
                                    onChange={() => handleCategoryChange(parseInt(key))}
                                    className="w-4 h-4 form-check-input"
                                />
                                <span className="text-sm text-gray-700">{value}</span>
                            </label>
                        ))}
                    </div>

                    {/* Filter Inputs */}
                    <div className="grid grid-cols-4 gap-2">
                        {/* Date Range */}
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">Thời gian đăng ảnh</label>
                            <input
                                type="date"
                                value={localFilters.startDate}
                                onChange={(e) => handleFilterChange('startDate', e.target.value)}
                                className="form-control"
                            />
                        </div>
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">&nbsp;</label>
                            <input
                                type="date"
                                value={localFilters.endDate}
                                onChange={(e) => handleFilterChange('endDate', e.target.value)}
                                className="form-control"
                            />
                        </div>

                        {/* Caption */}
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">Chú thích ảnh</label>
                            <input
                                type="text"
                                value={localFilters.caption}
                                onChange={(e) => handleFilterChange('caption', e.target.value)}
                                className="form-control"
                                placeholder="Nhập chú thích"
                            />
                        </div>

                        {/* Hashtag */}
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">Hashtag</label>
                            <input
                                type="text"
                                value={localFilters.hashtag}
                                onChange={(e) => handleFilterChange('hashtag', e.target.value)}
                                className="form-control"
                                placeholder="hashtag1, hashtag2"
                            />
                        </div>
                    </div>
                </div>

                {/* Image Grid */}
                <div className="max-h-[calc(100vh-400px)] h-[calc(100vh-400px)] overflow-auto mb-1">
                    {fileGenerated.length === 0 && <div className="text-center text-gray-500 py-8">Không có ảnh</div>}
                    <div className="grid grid-cols-3 gap-2 content-start mb-1">
                        {fileGenerated.map((item: FileData, index) => (
                            <div key={index} className="relative group">
                                {/* Image Container */}
                                <div className="relative">
                                    <img
                                        src={item.file_url || ''}
                                        alt={item.file_name || ''}
                                        className={'w-full h-auto object-cover border-gray-200 border-2 rounded-lg'}
                                        style={{
                                            aspectRatio: '16/9',
                                        }}
                                    />

                                    {/* Checkbox with order number */}
                                    <div className="absolute top-2 left-2 flex items-center gap-1">
                                        <input
                                            type="checkbox"
                                            checked={selectedImages.some((img) => img.id === (item.id ?? 0))}
                                            onChange={() => onImageSelect(item)}
                                            className="w-4 h-4 form-check-input"
                                            onClick={(e) => e.stopPropagation()}
                                        />
                                        {getImageOrder(item.id ?? 0) > 0 && (
                                            <span className="bg-blue-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center font-bold">
                                                {getImageOrder(item.id ?? 0)}
                                            </span>
                                        )}
                                    </div>

                                    {/* Dropdown Menu */}
                                    <div className="absolute top-2 right-2">
                                        <button
                                            onClick={(e) => {
                                                e.stopPropagation();
                                                handleDropdownToggle(item.id ?? 0);
                                            }}
                                            className="px-[4px] py-[.5px] bg-white rounded-full shadow-md hover:bg-gray-50"
                                            type="button"
                                        >
                                            <MoreHorizontal size={12} className="text-gray-600" />
                                        </button>

                                        {/* Dropdown Content */}
                                        {openDropdown === item.id && (
                                            <div className="absolute right-0 top-8 bg-white border border-gray-200 rounded-md shadow-lg z-10 min-w-[120px]">
                                                <button
                                                    type="button"
                                                    onClick={() => handleDownload(item)}
                                                    className="flex items-center gap-2 w-full p-[4px] text-sm text-gray-700 hover:bg-gray-50"
                                                >
                                                    <Download size={14} />
                                                    Tải xuống
                                                </button>
                                                {/* <button
                                                    type="button"
                                                    onClick={() => handleDelete(item.id ?? 0)}
                                                    className="flex items-center gap-2 w-full p-[4px] text-sm text-red-600 hover:bg-red-50"
                                                >
                                                    <Trash2 size={14} />
                                                    Xóa
                                                </button> */}
                                            </div>
                                        )}
                                    </div>
                                </div>

                                {/* Image Title */}
                                <div className="mt-1">
                                    <h3 className="text-lg font-medium text-gray-900 truncate">
                                        {item.file_title || 'Untitled'}
                                    </h3>
                                    <div className="flex flex-wrap gap-1 mt-1">
                                        {/* Display actual tags from API */}
                                        {item.tags?.map((tag, tagIndex) => (
                                            <span
                                                key={tagIndex}
                                                className="inline-block badge rounded-pill badge-light-danger me-50 mb-50"
                                            >
                                                {tag.name}
                                            </span>
                                        ))}
                                    </div>
                                </div>
                            </div>
                        ))}
                    </div>
                    {fileCategory !== FileCategory.IN_ARTICLE && (
                        <PaginationTable
                            countItem={listFile?.totalCount}
                            totalPage={listFile?.totalPages}
                            currentPage={listFile?.currentPage}
                            handlePageChange={handlePageChange}
                        />
                    )}
                </div>
            </div>
            <ModalConfirm
                show={showDeleteModal}
                text={`Bạn có chắc chắn muốn xóa ảnh này?`}
                btnDisabled={deletePending}
                changeShow={setShowDeleteModal}
                submitAction={handleSubmitDelete}
            />
        </>
    );
}
