import { InputHTMLAttributes, useId } from 'react';
import { RegisterOptions, UseFormRegister } from 'react-hook-form';

interface InputProps extends InputHTMLAttributes<HTMLInputElement> {
    classNameWrap?: string;
    labelSwitchName: string;
    labelFieldName?: string;
    errorMessage?: string;
    // tslint:disable-next-line:no-any
    register?: UseFormRegister<any>;
    rules?: RegisterOptions;
    classNameInput?: string;
}

export default function InputSwitch({
    classNameWrap = 'mb-1',
    labelSwitchName,
    labelFieldName,
    name,
    className,
    errorMessage,
    register,
    rules,
    classNameInput = 'form-check-input',
    ...rest
}: Readonly<InputProps>) {
    const registerResult = register && name ? register(name, rules) : {};
    const id = useId();
    return (
        <div className={classNameWrap}>
            <div className={className}>
                {labelFieldName && <label className="form-label">{labelFieldName}</label>}
                <div className="form-check form-check-danger form-switch">
                    <input {...registerResult} {...rest} type="checkbox" className={classNameInput} id={id} />
                    <label className="form-check-label mb-50" htmlFor={id}>
                        {labelSwitchName}
                    </label>
                </div>
            </div>
        </div>
    );
}
