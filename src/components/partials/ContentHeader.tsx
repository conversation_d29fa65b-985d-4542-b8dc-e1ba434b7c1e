import { isEmpty } from 'lodash';
import { ItemLink } from 'types/common/Item';
import Breadcrumbs from './Breadcrumbs';
import MenuContextRight from './MenuContextRight';
import cn from '../../utils/cn';
import { Plus } from 'react-feather';

interface IProps {
    title: string;
    breadcrumbs?: ItemLink[];
    contextMenu?: ItemLink[];
    addAction?: () => void;
    rightOptions?: React.ReactNode;
    className?: string;
}

export default function ContentHeader({
    title,
    breadcrumbs = [],
    contextMenu = [],
    addAction,
    rightOptions,
    className,
}: Readonly<IProps>) {
    return (
        <div
            className={cn('content-header row !items-center !mb-[14px] w-full', className, {
                'flex !flex-row': rightOptions,
            })}
        >
            <div
                className={cn('content-header-left col-md-9 col-9', {
                    '!w-auto flex-1': rightOptions,
                })}
            >
                <div className="row breadcrumbs-top">
                    <div className="col-12">
                        <h2 className="mb-0 content-header-title float-start !border-none !text-[32px] !font-bold !text-[#323743FF]">
                            {title}
                        </h2>
                        {!isEmpty(breadcrumbs) && <Breadcrumbs breadcrumbs={breadcrumbs} />}
                    </div>
                </div>
            </div>
            {!isEmpty(contextMenu) && <MenuContextRight contextMenu={contextMenu} />}
            {addAction && (
                <div className="content-header-right text-end col-md-3 col-3 d-block">
                    <div className="breadcrumb-right flex justify-end">
                        <button
                            className="btn-icon btn btn-primary btn-round btn-sm dropdown-toggle waves-effect waves-float waves-light flex items-center gap-x-[6px] !py-[8px]"
                            type="button"
                            onClick={addAction}
                        >
                            <Plus size={16} />
                            <span className="text-sm">Thêm mới</span>
                        </button>
                    </div>
                </div>
            )}
            {rightOptions}
        </div>
    );
}
