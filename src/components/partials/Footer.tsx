import { ArrowUp } from 'react-feather';
import { useTranslation } from 'react-i18next';
import { Link } from 'react-router-dom';

export default function Footer() {
    const { t } = useTranslation();

    return (
        <>
            <div className="sidenav-overlay" />
            <div className="drag-target" />
            <footer className="footer footer-static footer-light">
                <p className="clearfix mb-0">
                    <span className="float-md-start d-block d-md-inline-block mt-25">
                        COPYRIGHT © 2024
                        <Link className="ms-25" to="/dashboard">
                            Mediasoft
                        </Link>
                        <span className="d-none d-sm-inline-block">, All rights Reserved</span>
                    </span>
                    <span className="float-md-end d-none d-md-block">
                        Công ty cổ phần giải pháp dịch vụ số Mediasoft
                    </span>
                </p>
            </footer>
            <button className="btn btn-primary btn-icon scroll-top" type="button">
                <ArrowUp />
            </button>
        </>
    );
}
