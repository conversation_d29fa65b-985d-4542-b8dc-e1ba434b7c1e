import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import { HelmetProvider } from 'react-helmet-async';
import { ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import RouterView from 'routes';
import { useAuthStore } from './stores/authStore';
import { ResetStorageEvent, resetType } from './utils/localStorage';
import { useEffect } from 'react';

const queryClient = new QueryClient({
    defaultOptions: {
        queries: {
            refetchOnWindowFocus: false,
            retry: 2, // Limit retry attempts to 2
        },
    },
});

function App() {
    const unauthorized = useAuthStore((state) => state.unauthorized);

    useEffect(() => {
        ResetStorageEvent.addEventListener(resetType, unauthorized);
        return () => {
            ResetStorageEvent.removeEventListener(resetType, unauthorized);
        };
    }, [unauthorized]);

    return (
        <HelmetProvider>
            <QueryClientProvider client={queryClient}>
                <RouterView />
                <ToastContainer autoClose={2000} />
                <ReactQueryDevtools />
            </QueryClientProvider>
        </HelmetProvider>
    );
}

export default App;
