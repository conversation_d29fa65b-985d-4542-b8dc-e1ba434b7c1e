import React, { Suspense } from 'react';
import ReactDOM from 'react-dom/client';
import 'utils/i18n';
import './index.css'; // Import Tailwind CSS
import './custom.css';
import App from './App';
import reportWebVitals from './reportWebVitals';
import ErrorBoundary from 'components/partials/ErrorBoundary';

const root = ReactDOM.createRoot(document.getElementById('root') as HTMLElement);
root.render(
    <React.StrictMode>
        <Suspense fallback={null}>
            <ErrorBoundary>
                <App />
            </ErrorBoundary>
        </Suspense>
    </React.StrictMode>
);

// If you want to start measuring performance in your app, pass a function
// to log results (for example: reportWebVitals(console.log))
// or send to an analytics endpoint. Learn more: https://bit.ly/CRA-vitals
reportWebVitals();
