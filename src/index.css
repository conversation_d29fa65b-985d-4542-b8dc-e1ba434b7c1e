@tailwind base;
@tailwind components;
@tailwind utilities;

@import url('https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,100..900;1,100..900&display=swap');

* {
  font-family: "Roboto", sans-serif;
}

/* Các CSS tùy chỉnh của bạn có thể được thêm vào đây */
@layer base {
  html {
    font-family: system-ui, sans-serif;
  }
}

@layer components {
  /* Các component classes tùy chỉnh */
  .btn {
    @apply px-4 py-2 rounded font-semibold transition-colors duration-200;
  }
  .btn-primary {
    @apply bg-blue-500 text-white hover:bg-blue-600;
  }
  .btn-secondary {
    @apply bg-gray-200 text-gray-800 hover:bg-gray-300;
  }
}

@layer utilities {
  /* Các utility classes tùy chỉnh */
}

svg {
  display: inline !important;
}

.single-thumb .range-slider__thumb[data-lower] {
  width: 0;
}
.single-thumb .range-slider__range {
  border-radius: 6px;
}
