import { FILTER_CONDITIONS } from 'constants/common';
import { baseFilterConfig, BaseModel, DataList, FilterConfig } from './common';
import { FileResponse, ItemParam, ItemStatus } from './common/Item';

export enum IssueType {
    DAILY = 1,
    WEEKLY = 2,
    MONTHLY = 3,
    YEARLY = 4,
}

export enum PageSize {
    A3 = 1,
    A4 = 2,
}

export interface PressPublication extends BaseModel {
    name: string;
    status_id: ItemStatus | boolean;
    display_order: number;
    avatar_id?: number | null;
    avatar?: FileResponse;
    issue_title?: string | null;
    issue_status_id: ItemStatus | boolean;
    issue_type_id: IssueType;
    issue_days: number[];
    issue_offset: number;
    department_id: number;
    page_count: number;
    issue_pre_created: number;
    page_size_id: number;
}

export interface PressPublicationQuery {
    press_publications_list: DataList<PressPublication>;
}

export const pressPublicationFilterConfig: FilterConfig = {
    ...baseFilterConfig,
    press_publication_id: { key: 'press_publication_id', operator: FILTER_CONDITIONS.IN },
};

export const IssueTypeNames: ItemParam[] = [
    { id: IssueType.DAILY, name: 'daily' },
    { id: IssueType.WEEKLY, name: 'weekly' },
    { id: IssueType.MONTHLY, name: 'monthly' },
    { id: IssueType.YEARLY, name: 'yearly' },
];

export const PageSizeType: ItemParam[] = [
    { id: PageSize.A3, name: 'a3' },
    { id: PageSize.A4, name: 'a4' },
];
