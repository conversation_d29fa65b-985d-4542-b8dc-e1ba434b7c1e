import { baseFilterConfig, BaseModel, <PERSON>S<PERSON>ch, DataList, FilterConfig } from './common';
import { FileResponse, ItemParam, ItemStatus } from './common/Item';
import { FILTER_CONDITIONS } from '../constants/common';

export default interface AdvertiseItem extends BaseModel {
    name: string;
    desc?: string;
    content: string;
    type_id: number;
    file_id?: number;
    width: number;
    height: number;
    status_id: ItemStatus | boolean;
    start_date: string;
    end_date?: string;
    department_id: number;
    file?: FileResponse;
}

export interface AdvertiseItemQuery {
    adv_items_list: DataList<AdvertiseItem>;
}

export interface SearchAdvertiseItem extends BaseSearch {
    type_id?: string;
}

export type SearchAdvertiseItemParam = {
    [key in keyof SearchAdvertiseItem]: string;
};

export const AdvertiseItemFilterConfig: FilterConfig = {
    ...baseFilterConfig,
    type_id: { key: 'type_id', operator: FILTER_CONDITIONS.IN },
};

export enum AdvertiseItemType {
    IMAGE = 1,
    EMBED = 2,
}

export const AdvertiseItemTypeNames: ItemParam[] = [
    { id: AdvertiseItemType.IMAGE, name: 'advertiseItemType1', className: 'badge badge-glow bg-success' },
    { id: AdvertiseItemType.EMBED, name: 'advertiseItemType2', className: 'badge badge-glow bg-info' },
];
