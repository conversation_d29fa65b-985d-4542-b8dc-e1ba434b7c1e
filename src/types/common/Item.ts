import { BaseModel } from './index';

export interface ItemParam {
    id: number;
    name: string;
    desc?: string;
    className?: string;
}

export interface ItemLink {
    id?: number;
    text: string;
    to?: string;
    icon?: 'PLUS' | 'DOWNLOAD' | 'SEND';
    fnCallBack?: {
        actionMenu: (id: number) => void;
    };
}

export interface ItemId {
    id: number;
}

export interface ItemFile {
    id: number;
    name: string;
    url: string;
    mineType?: string;
    fileSize?: number;
    file_title?: string;
}

export interface SelectOption {
    value: number;
    label: string;
}

export enum TrueFalse {
    FALSE = 0,
    TRUE = 1,
}

// export enum Language {
//     EN = 'en',
// }

export enum ItemStatus {
    PENDING = 1,
    ACTIVE = 2,
}

export enum ItemParamType {
    GROUP = 'group',
}

export enum ItemLanguage {
    VI = 1,
    EN = 2,
    CN = 3,
}

export enum ArticleType {
    ELECTRONIC = 1,
    PAPER = 2,
    TELEVISION = 3,
    RADIO = 5,
    VIDEO = 4,
}

export const ItemStatusNames: ItemParam[] = [
    { id: ItemStatus.PENDING, name: 'pending', className: 'badge badge-glow bg-warning' },
    { id: ItemStatus.ACTIVE, name: 'active', className: 'badge badge-glow bg-success' },
];

export const PublishStatusNames: ItemParam[] = [
    { id: ItemStatus.ACTIVE, name: 'published', className: 'badge badge-glow bg-success' },
    { id: ItemStatus.PENDING, name: 'unpublished', className: 'badge badge-glow bg-danger' },
];

export const ApproveStatusNames: ItemParam[] = [
    { id: ItemStatus.ACTIVE, name: 'approved', className: 'badge badge-glow bg-success' },
    { id: ItemStatus.PENDING, name: 'pendingApprove', className: 'badge badge-glow bg-danger' },
];

export const ItemLanguageNames: ItemParam[] = [
    { id: ItemLanguage.VI, name: 'vi', className: 'badge badge-glow bg-success' },
    { id: ItemLanguage.EN, name: 'en', className: 'badge badge-glow bg-info' },
    { id: ItemLanguage.CN, name: 'cn', className: 'badge badge-glow bg-warning' },
];

export const ArticleTypeNames: ItemParam[] = [
    { id: ArticleType.ELECTRONIC, name: 'electronic', className: 'electronic' },
    { id: ArticleType.PAPER, name: 'paper', className: 'paper' },
    { id: ArticleType.TELEVISION, name: 'television' },
    { id: ArticleType.RADIO, name: 'radio' },
    { id: ArticleType.VIDEO, name: 'video' },
];

export type TypeMedia = 'image' | 'video' | 'audio' | 'avatar1' | 'avatar2';

export interface FileResponse extends BaseModel {
    file_name: string;
    file_url: string;
    file_size: number;
    mime_type: string;
    folder_id: string;
    department_id: string;
    is_newsroom: boolean;
    parent_id?: number;
    file_title?: string;
    parent?: FileResponse;
}

export interface SelectOptionModel {
    value: string;
    label: string;
}

export interface ItemParamModel {
    id: string;
    name: string;
    className?: string;
}
