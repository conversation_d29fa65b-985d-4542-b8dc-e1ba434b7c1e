import { BaseModel } from './common';
import { ItemStatus } from './common/Item';

export default interface Department extends BaseModel {
    name: string;
    status_id: ItemStatus | boolean;
    parent_id?: number;
    desc?: string;
    display_order: number;
    children?: Department[];
    language_id?: number | null;
}

export interface DepartmentQuery {
    departments_list: {
        data: Department[];
    };
}
