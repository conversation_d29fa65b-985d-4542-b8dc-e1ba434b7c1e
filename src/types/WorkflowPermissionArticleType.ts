import { baseFilterConfig, <PERSON><PERSON><PERSON>l, <PERSON><PERSON>ist, FilterConfig } from './common';
import { ArticleType } from './common/Item';
import { FILTER_CONDITIONS } from '../constants/common';

export default interface WorkflowPermissionArticleType extends BaseModel {
    workflow_permission_id: number;
    article_type_id: ArticleType;
    user_department_id: number;
}

export interface WorkflowPermissionArticleTypeQuery {
    workflow_permission_article_types_list: WorkflowPermissionArticleType[];
}

export const workflowPermissionArticleTypeFilterConfig: FilterConfig = {
    ...baseFilterConfig,
    workflow_permission_id: { key: 'workflow_permission_id', operator: FILTER_CONDITIONS.IN },
    article_type_id: { key: 'article_type_id', operator: FILTER_CONDITIONS.IN },
    user_department_id: { key: 'user_department_id', operator: FILTER_CONDITIONS.EQUAL },
};
