import { FILTER_CONDITIONS } from '../constants/common';
import { BaseModel, DataList, FilterConfig } from './common';
import Tag from './Tag';

export interface DataUploadFile {
    id: number;
    name: string;
    url: string | null;
}
export interface UploadFile extends BaseModel {
    data: DataUploadFile[];
}

export interface FileData extends BaseModel {
    file_name: string;
    file_url: string;
    file_size: number;
    mime_type: string;
    folder_id: number;
    department_id: number;
    is_newsroom: boolean;
    parent_id?: number;
    tags?: Tag[];
    file_title?: string;
}

export interface FileQueryRes {
    files_list: DataList<FileData>;
}

export const ImageTypes = ['image/png', 'image/jpg', 'image/jpeg', 'image/gif'];

export const VideoTypes = [
    'video/mp4',
    'video/webm',
    'video/ogg',
    'video/avi',
    'video/mov',
    'video/wmv',
    'video/flv',
    'video/mkv',
    'video/m4v',
    'video/3gp',
];
