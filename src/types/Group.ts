import Action from './Action';
import { baseFilterConfig, BaseModel, BaseSearch, DataList, FilterConfig } from './common';
import { FILTER_CONDITIONS } from '../constants/common';
import { ItemParam, ItemStatus } from './common/Item';
import User from './User';

export enum GroupType {
    ROLE = 1,
    DEPARTMENT = 2,
    FUNCTIONAL = 3,
}

export default interface Group extends BaseModel {
    name: string;
    department_id: number;
    parent_id?: number | null;
    desc?: string | null;
    type_id: GroupType;
    status_id: ItemStatus | boolean;
    children?: Group[];
    actions?: Action[];
    users: User[];
}

export interface GroupQuery {
    groups_list: DataList<Group>;
}

export interface SearchGroup extends BaseSearch {
    type_id?: string;
    parent_id?: string;
}

export type SearchGroupParam = {
    [key in keyof SearchGroup]: string;
};

export const groupFilterConfig: FilterConfig = {
    ...baseFilterConfig,
    type_id: { key: 'type_id', operator: FILTER_CONDITIONS.EQUAL },
    parent_id: { key: 'parent_id', operator: FILTER_CONDITIONS.EQUAL },
};

export const GroupTypeNames: ItemParam[] = [
    { id: GroupType.ROLE, name: 'role' },
    { id: GroupType.DEPARTMENT, name: 'department' },
    { id: GroupType.FUNCTIONAL, name: 'functional' },
];
