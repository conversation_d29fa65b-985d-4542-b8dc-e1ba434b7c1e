export interface ScaledPosition {
    boundingRect: {
        x1: number;
        y1: number;
        x2: number;
        y2: number;
        width: number;
        height: number;
    };
    rects: Array<{
        x1: number;
        y1: number;
        x2: number;
        y2: number;
        width: number;
        height: number;
    }>;
    pageNumber: number;
}

export interface Content {
    text?: string;
    image?: string;
}

export interface Comment {
    text: string;
    emoji?: string; // Optional để tương thích với react-pdf-highlighter
}

export interface IHighlight {
    id: string;
    position: ScaledPosition;
    content: Content;
    comment: Comment;
}

export interface NewHighlight {
    position: ScaledPosition;
    content: Content;
    comment: Comment;
}

export enum HighlightType {
    TEXT = 'text',
    AREA = 'area',
}

export interface PdfHighlighterState {
    highlights: IHighlight[];
    selectedHighlight?: IHighlight;
    isEditingComment: boolean;
    editingCommentId?: string;
}

export interface HighlightAction {
    type:
        | 'ADD_HIGHLIGHT'
        | 'UPDATE_HIGHLIGHT'
        | 'DELETE_HIGHLIGHT'
        | 'SELECT_HIGHLIGHT'
        | 'CLEAR_SELECTION'
        | 'START_EDIT_COMMENT'
        | 'STOP_EDIT_COMMENT';
    //tslint:disable-next-line: no-any
    payload?: any;
}
