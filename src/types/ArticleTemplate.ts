import { BaseModel, BaseSearch } from './common';

export interface SearchArticleTemplate extends BaseSearch {
    name?: string;
    status?: number | null;
}

export type SearchArticleTemplateParam = {
    [key in keyof SearchArticleTemplate]: string;
};

export default interface ArticleTemplate extends BaseModel {
    name: string;
    description: string | null;
    template: string;
    image: string | null;
    status: number | boolean;
}
