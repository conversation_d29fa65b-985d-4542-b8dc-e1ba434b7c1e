import Article from './Article';
import Category from './Category';
import { baseFilterConfig, BaseModel, BaseSearch, DataList, FilterConfig } from './common';
import { FILTER_CONDITIONS } from '../constants/common';

export default interface ArticleCategory extends BaseModel {
    article_id: number;
    category_id: number;
    display_order: number;
    article: Article;
    category: Category;
    is_major: boolean;
}

export interface ArticleCategoryListQuery {
    article_categories_list: DataList<ArticleCategory>;
}

export interface SearchArticleCategory extends BaseSearch {
    category_id?: string;
    article_id?: string;
    'article.article_type_id'?: string;
}

export type SearchArticleCategoryParam = {
    [key in keyof SearchArticleCategory]: string;
};

export const articleCategoryFilterConfig: FilterConfig = {
    ...baseFilterConfig,
    category_id: { key: 'category_id', operator: FILTER_CONDITIONS.EQUAL },
    article_id: { key: 'article_id', operator: FILTER_CONDITIONS.EQUAL },
    'article.article_type_id': { key: 'article.article_type_id', operator: FILTER_CONDITIONS.EQUAL },
};
