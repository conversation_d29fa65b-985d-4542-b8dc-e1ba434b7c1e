// ImageEditor Types và Interfaces

export enum ActionType {
    ZOOM = 'zoom',
    CROP = 'crop',
    FLIP = 'flip',
    ROTATE = 'rotate',
    WATERMARK = 'watermark',
    TEXT_OVERLAY = 'text_overlay',
    FRAME = 'frame',
    RESTORE = 'restore',
}

export interface CropArea {
    x: number;
    y: number;
    width: number;
    height: number;
    unit?: 'px' | '%';
}

export interface WatermarkConfig {
    id: number;
    url: string;
    name: string;
    opacity: number; // 0-100
    position: {
        x: number; // 0-100 (%)
        y: number; // 0-100 (%)
    };
    size: {
        width: number; // pixels
        height: number; // pixels
    };
}

export interface TextOverlay {
    id: string;
    text: string;
    position: {
        x: number; // 0-100 (%)
        y: number; // 0-100 (%)
    };
    style: {
        fontSize: number; // px
        fontFamily: string;
        fontWeight: 'normal' | 'bold' | '100' | '200' | '300' | '400' | '500' | '600' | '700' | '800' | '900';
        color: string; // hex color
        backgroundColor?: string; // hex color, optional
        opacity: number; // 0-100
    };
    rotation?: number; // degrees
}

export interface FrameConfig {
    id: string;
    type: 'border' | 'shadow' | 'rounded';
    style: {
        borderWidth?: number; // px
        borderColor?: string; // hex color
        borderStyle?: 'solid' | 'dashed' | 'dotted';
        shadowBlur?: number; // px
        shadowColor?: string; // hex color
        shadowOffsetX?: number; // px
        shadowOffsetY?: number; // px
        borderRadius?: number; // px
    };
}

export interface ImageState {
    id: string;
    timestamp: number;
    actionType: ActionType;

    // Image properties
    imageUrl: string;
    originalImageUrl: string;
    imageWidth?: number; // Custom width for resize
    imageHeight?: number; // Custom height for resize

    // Transform properties
    zoomLevel: number; // 1-100
    cropArea?: CropArea;
    flipHorizontal: boolean;
    flipVertical: boolean;
    rotation: number; // degrees: 0, 90, 180, 270

    // Effects
    watermark?: WatermarkConfig; // Single watermark only
    textOverlays: TextOverlay[];
    frame?: FrameConfig;

    // Metadata
    description: string;
    tags: string[];
}

export interface ImageHistoryState {
    currentIndex: number;
    history: ImageState[];
    maxHistorySize: number; // Default: 5
}

// Action interfaces for type safety
export interface ZoomAction {
    type: ActionType.ZOOM;
    payload: {
        zoomLevel: number;
    };
}

export interface CropAction {
    type: ActionType.CROP;
    payload: {
        cropArea: CropArea;
    };
}

export interface FlipAction {
    type: ActionType.FLIP;
    payload: {
        horizontal?: boolean;
        vertical?: boolean;
    };
}

export interface RotateAction {
    type: ActionType.ROTATE;
    payload: {
        rotation: number; // degrees
    };
}

export interface WatermarkAction {
    type: ActionType.WATERMARK;
    payload: {
        watermark?: WatermarkConfig;
    };
}

export interface TextOverlayAction {
    type: ActionType.TEXT_OVERLAY;
    payload: {
        textOverlays: TextOverlay[];
    };
}

export interface FrameAction {
    type: ActionType.FRAME;
    payload: {
        frame?: FrameConfig;
    };
}

export interface RestoreAction {
    type: ActionType.RESTORE;
    payload: {
        imageUrl: string;
    };
}

export type ImageEditorAction =
    | ZoomAction
    | CropAction
    | FlipAction
    | RotateAction
    | WatermarkAction
    | TextOverlayAction
    | FrameAction
    | RestoreAction;

// Utility types
export interface ImageDimensions {
    width: number;
    height: number;
}

export interface ProcessingOptions {
    quality?: number; // 0-1
    format?: 'jpeg' | 'png' | 'webp';
}

// Default values
export const DEFAULT_IMAGE_STATE: Partial<ImageState> = {
    zoomLevel: 100,
    flipHorizontal: false,
    flipVertical: false,
    rotation: 0,
    textOverlays: [],
    description: '',
    tags: [],
};

export const DEFAULT_WATERMARK_CONFIG: Partial<WatermarkConfig> = {
    opacity: 50,
    position: { x: 50, y: 50 },
    size: { width: 100, height: 100 },
};

export const DEFAULT_TEXT_STYLE: TextOverlay['style'] = {
    fontSize: 16,
    fontFamily: 'Arial',
    fontWeight: 'normal',
    color: '#000000',
    opacity: 100,
};

export const HISTORY_LIMIT = 5;
