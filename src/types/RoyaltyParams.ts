import { BaseModel, DataList } from './common';
import { ItemStatus } from './common/Item';

export interface RoyaltyParams extends BaseModel {
    name: string;
    desc: string;
    values: {
        value: string;
        weightedScore: string;
        display_order: string;
        name: string;
    }[];
    status_id: ItemStatus;
    display_order: number;
    department_id: number;
}

export interface RoyaltyParamsQuery {
    royalty_params_list: DataList<RoyaltyParams>;
}

export interface RoyaltyParamsForm {
    name: string;
    desc: string;
    values: {
        value: string;
        weightedScore: string;
        display_order: string;
        name: string;
    }[];
    status_id: ItemStatus;
    display_order: string;
}
