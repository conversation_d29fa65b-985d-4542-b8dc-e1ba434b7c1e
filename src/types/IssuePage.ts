import { baseFilterConfig, BaseModel, BaseSearch, DataList, FilterConfig } from './common';
import { FileResponse, ItemStatus } from './common/Item';
import { FILTER_CONDITIONS } from '../constants/common';
import Issue from './Issue';
import { ArticleIssuePage } from './Article';
import { PressPublication } from './PressPublication';

export default interface IssuePage extends BaseModel {
    name: string;
    status_id: ItemStatus | boolean;
    approve_status_id: ItemStatus | boolean;
    issue_id: number;
    department_id: number;
    page_number: number;
    press_publication_id: number;
    issue?: Issue;
    articleIssuePages?: ArticleIssuePage[];
    pressPublication?: PressPublication;
}

export interface IssuePageQuery {
    issue_pages_list: DataList<IssuePage>;
}

export interface SearchIssuePage extends BaseSearch {
    press_publication_id?: string;
    issue_id?: string;
    approve_status_id?: string;
}

export type SearchIssuePageParam = {
    [key in keyof SearchIssuePage]: string;
};

export const issuePageFilterConfig: FilterConfig = {
    ...baseFilterConfig,
    press_publication_id: {
        key: 'press_publication_id',
        operator: FILTER_CONDITIONS.IN,
    },
    issue_id: {
        key: 'issue_id',
        operator: FILTER_CONDITIONS.IN,
    },
    approve_status_id: {
        key: 'approve_status_id',
        operator: FILTER_CONDITIONS.IN,
    },
};

export interface IssuePageDetailQuery {
    issue_pages_detail: IssuePage;
}
