import { BaseModel, DataList } from './common';
import { ItemParam, ItemStatus } from './common/Item';

export enum PortletType {
    STATIC = 1,
    DYNAMIC = 2,
}

export default interface Portlet extends BaseModel {
    name: string;
    code: string;
    sql?: string | null;
    desc?: string | null;
    content: string;
    status_id: ItemStatus | boolean;
    layout_type_id: PortletType;
    is_client: boolean;
    department_id: number;
}

export interface PortletQuery {
    portlets_list: DataList<Portlet>;
}

export interface PortletDetail {
    portlet_detail: Portlet;
}

export const PortletTypeNames: ItemParam[] = [
    { id: PortletType.DYNAMIC, name: 'dynamic' },
    { id: PortletType.STATIC, name: 'static' },
];
