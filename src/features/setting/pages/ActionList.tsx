import ContentHeader from 'components/partials/ContentHeader';
import ModalConfirm from 'components/partials/ModalConfirm';
import Spinner from 'components/partials/Spinner';
import { LIMIT_MAX, PAGE_NUMBER_DEFAULT, QUERY_KEY } from 'constants/common';
import { find } from 'lodash';
import { useState } from 'react';
import { Helmet } from 'react-helmet-async';
import { useTranslation } from 'react-i18next';
import { ACTION_CREATE, ACTION_DELETE, ACTION_LIST, ACTION_UPDATE, getFlatActions } from 'services/ActionService';
import Action, { ActionQuery } from 'types/Action';
import { showToast } from 'utils/common';
import ListAction from '../components/ListAction';
import ModalActionUpdate from '../components/ModalActionUpdate';
import { useGraphQLQuery } from '../../../hooks/useGraphQLQuery';
import { useGraphQLMutation } from '../../../hooks/useGraphQLMutation';
import { BaseSearch } from '../../../types/common';
import { keepPreviousData } from '@tanstack/react-query';

export default function ActionList() {
    const [itemId, setItemId] = useState(0);
    const [showDelete, setShowDelete] = useState(false);
    const [showUpdate, setShowUpdate] = useState(false);
    const { t } = useTranslation();

    const {
        data: actionData,
        isLoading,
        isRefetching,
        refetch,
    } = useGraphQLQuery<ActionQuery, BaseSearch>(
        [QUERY_KEY.ACTIONS],
        ACTION_LIST,
        {
            limit: LIMIT_MAX,
            page: PAGE_NUMBER_DEFAULT,
            sorts: ['display_order:ASC'],
        },
        '',
        {
            placeholderData: keepPreviousData,
        }
    );
    const actions = getFlatActions(actionData?.actions_list.data ?? []);

    const updateMutation = useGraphQLMutation<{}, Partial<Action>>(itemId > 0 ? ACTION_UPDATE : ACTION_CREATE, '', {
        onSuccess: () => {
            showToast(true, [t('success.update')]);
            setShowUpdate(false);
            setItemId(0);
            refetch();
        },
        onError: () => showToast(false, [t('error.common')]),
    });

    const deleteMutation = useGraphQLMutation(ACTION_DELETE, '', {
        onSuccess: () => {
            showToast(true, [t('success.delete')]);
            setShowDelete(false);
            setItemId(0);
            refetch();
        },
        onError: () => showToast(false, [t('error.common')]),
    });

    const updateItem = async (body: Action) => {
        if (body.parent_id === 0) delete body.parent_id;
        updateMutation.mutate(body);
    };

    const deleteItem = () => {
        if (itemId) {
            deleteMutation.mutate({ id: itemId });
        }
    };

    const handleEdit = (id: number) => {
        updateMutation.reset();
        setItemId(id);
        setShowUpdate(true);
    };

    const handleDelete = (id: number) => {
        deleteMutation.reset();
        setItemId(id);
        setShowDelete(true);
    };

    const actionMenu = (_id: number) => {
        updateMutation.reset();
        setShowUpdate(true);
        setItemId(0);
    };

    return (
        <>
            <Helmet>
                <title>Quản lý chức năng</title>
            </Helmet>
            <ContentHeader
                title="Quản lý chức năng"
                contextMenu={[
                    {
                        text: 'Thêm chức năng',
                        to: '',
                        icon: 'PLUS',
                        fnCallBack: { actionMenu },
                    },
                ]}
            />
            <div className="content-body">
                <div className="col-12">
                    {(isLoading || isRefetching) && <Spinner />}
                    {!isLoading && !isRefetching && (
                        <>
                            <div className="card">
                                <ListAction items={actions} handleEdit={handleEdit} handleDelete={handleDelete} />
                            </div>
                            <ModalActionUpdate
                                show={showUpdate}
                                action={find(actions, { id: itemId })}
                                listActions={actions}
                                isLoading={updateMutation.isPending}
                                changeShow={(s: boolean) => setShowUpdate(s)}
                                submitAction={updateItem}
                            />
                        </>
                    )}
                    <ModalConfirm
                        show={showDelete}
                        text={t('confirm.delete')}
                        btnDisabled={deleteMutation.isPending}
                        changeShow={(s: boolean) => setShowDelete(s)}
                        submitAction={deleteItem}
                    />
                </div>
            </div>
        </>
    );
}
