import { useState } from 'react';
import { Helmet } from 'react-helmet-async';
import { find } from 'lodash';
import { useTranslation } from 'react-i18next';
import ContentHeader from '../../../components/partials/ContentHeader';
import Spinner from '../../../components/partials/Spinner';
import ModalConfirm from '../../../components/partials/ModalConfirm';
import { useGraphQLQuery } from '../../../hooks/useGraphQLQuery';
import { useGraphQLMutation } from '../../../hooks/useGraphQLMutation';
import useQueryParams from '../../../hooks/useQueryParams';
import omitBy from 'lodash/omitBy';
import isUndefined from 'lodash/isUndefined';
import { convertPaging, generateFilters, showToast } from '../../../utils/common';
import { CONFIGS_CREATE, CONFIGS_DELETE, CONFIGS_LIST, CONFIGS_UPDATE } from '../../../services/ConfigService';
import Config, { configFilterConfig, ConfigQuery } from '../../../types/Config';
import { OPERATION_NAME, PAGINATION, QUERY_KEY } from '../../../constants/common';
import PaginationTable from '../../../components/partials/PaginationTable';
import ListConfig from '../components/ListConfig';
import ModalConfigUpdate from '../components/ModalConfigUpdate';
import { useAppStore } from '../../../stores/appStore';
import { BaseSearchParam } from '../../../types/common';
import slugify from 'slugify';
import { keepPreviousData } from '@tanstack/react-query';

export default function PortletList() {
    const { t } = useTranslation();
    const [showUpdate, setShowUpdate] = useState<boolean>(false);
    const [itemId, setItemId] = useState<number>(0);
    const [showDelete, setShowDelete] = useState<boolean>(false);
    const departmentId = useAppStore((state) => state.departmentId);

    const { queryParams, setQueryParams } = useQueryParams<BaseSearchParam>();
    const paramConfig: BaseSearchParam = omitBy(
        {
            limit: queryParams.limit ?? PAGINATION.limit,
            page: queryParams.page ?? '1',
            search: queryParams.search,
            department_id: departmentId.toString(),
        },
        isUndefined
    );

    const { search, limit, page, ...dataParamConfig } = paramConfig;
    const filters = generateFilters(dataParamConfig, configFilterConfig);

    const { data, isLoading, isRefetching, refetch } = useGraphQLQuery<ConfigQuery>(
        [QUERY_KEY.CONFIGS, paramConfig, filters],
        CONFIGS_LIST,
        {
            page: Number(page),
            limit: Number(limit),
            search,
            filters: filters.length > 0 ? filters : undefined,
        },
        OPERATION_NAME.CALL_STATIC_TOKEN,
        {
            enabled: !!paramConfig.department_id,
            placeholderData: keepPreviousData,
        }
    );

    const configs = data?.configs_list.data ?? [];

    const saveMutation = useGraphQLMutation<{}, { id?: number; body: Partial<Config> }>(
        itemId > 0 ? CONFIGS_UPDATE : CONFIGS_CREATE,
        '',
        {
            onSuccess: () => {
                showToast(true, [t('success.update')]);
                setShowUpdate(false);
                setItemId(0);
                refetch();
            },
            onError: () => showToast(false, [t('error.common')]),
        }
    );

    const deleteMutation = useGraphQLMutation(CONFIGS_DELETE, '', {
        onSuccess: () => {
            showToast(true, [t('success.delete')]);
            setShowDelete(false);
            setItemId(0);
            refetch();
        },
        onError: () => showToast(false, [t('error.common')]),
    });

    const updateItem = async (body: Config) => {
        delete body.id;
        body.code = slugify(body.code);
        if (itemId === 0) {
            body.department_id = departmentId;
            saveMutation.mutate({ body });
        } else {
            saveMutation.mutate({ id: itemId, body });
        }
    };

    const handlePageChange = (_event: React.ChangeEvent<unknown>, page: number) => {
        setQueryParams({ ...queryParams, page: page.toString() });
    };

    const deleteItem = async () => {
        if (itemId) {
            deleteMutation.mutate({ id: itemId });
        }
    };

    const handleEdit = (id: number) => {
        saveMutation.reset();
        setItemId(id);
        setShowUpdate(true);
    };

    const handleDelete = (id: number) => {
        deleteMutation.reset();
        setItemId(id);
        setShowDelete(true);
    };

    const actionMenu = () => {
        saveMutation.reset();
        setShowUpdate(true);
        setItemId(0);
    };

    return (
        <>
            <Helmet>
                <title>Cấu hình website</title>
            </Helmet>
            <ContentHeader
                title="Cấu hình website"
                contextMenu={[
                    {
                        text: 'Thêm cấu hình',
                        to: '',
                        icon: 'PLUS',
                        fnCallBack: { actionMenu },
                    },
                ]}
            />
            <div className="content-body">
                {(isLoading || isRefetching) && <Spinner />}
                {!isLoading && !isRefetching && (
                    <>
                        <div className="card">
                            <ListConfig
                                items={configs}
                                paging={convertPaging<Config, BaseSearchParam>(
                                    data?.configs_list,
                                    Number(paramConfig.limit)
                                )}
                                handleEdit={handleEdit}
                                handleDelete={handleDelete}
                            />
                            <PaginationTable
                                countItem={data?.configs_list.totalCount}
                                totalPage={data?.configs_list.totalPages}
                                currentPage={data?.configs_list.currentPage}
                                handlePageChange={handlePageChange}
                            />
                        </div>
                        <ModalConfigUpdate
                            show={showUpdate}
                            config={find(configs, { id: itemId })}
                            departmentId={departmentId}
                            isLoading={saveMutation.isPending}
                            changeShow={(s: boolean) => setShowUpdate(s)}
                            submitAction={updateItem}
                        />
                    </>
                )}
                <ModalConfirm
                    show={showDelete}
                    text={t('confirm.delete')}
                    btnDisabled={deleteMutation.isPending}
                    changeShow={(s: boolean) => setShowDelete(s)}
                    submitAction={deleteItem}
                />
            </div>
        </>
    );
}
