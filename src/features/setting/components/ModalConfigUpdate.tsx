import { yupResolver } from '@hookform/resolvers/yup';
import classNames from 'classnames';
import UpdateButton from 'components/partials/UpdateButton';
import { useEffect, useLayoutEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import Config, { ConfigType, ConfigTypeNames } from 'types/Config';
import { isValidImageFile, showToast, toggleModalOpen } from 'utils/common';
import * as yup from 'yup';
import { Editor } from '@tinymce/tinymce-react';
import UploadImage from 'components/partials/UploadImage';
import { includes } from 'lodash';
import { DEFAULT_IMAGE, TINY_MCE_API_KEY } from '../../../constants/common';
import FileService from '../../../services/FileService';

interface IProps {
    show: boolean;
    config: Config | undefined;
    departmentId: number;
    isLoading?: boolean;
    changeShow: (s: boolean) => void;
    submitAction: (data: Config) => void;
}

export default function ModalConfigUpdate({
    show,
    config,
    departmentId,
    isLoading,
    changeShow,
    submitAction,
}: Readonly<IProps>) {
    const { t } = useTranslation();
    const [image, setImage] = useState(DEFAULT_IMAGE);
    const [file, setFile] = useState<File>();
    useLayoutEffect(() => toggleModalOpen(show), [show]);

    const schema = yup
        .object({
            code: yup.string().required(t('error.required')).trim(),
            name: yup.string().required(t('error.required')).trim(),
            //content: yup.string().required(t('error.required')).trim(),
        })
        .required();

    const {
        register,
        handleSubmit,
        setValue,
        clearErrors,
        reset,
        watch,
        formState: { errors },
    } = useForm<Config>({
        resolver: yupResolver(schema),
    });

    const typeId = +(watch('config_type_id') ?? ConfigType.TEXT);

    useEffect(() => {
        if (config && show) {
            reset(config);
            if (config.config_type_id === ConfigType.IMAGE) setImage(config.content);
        } else {
            reset({
                code: '',
                name: '',
                content: '',
                config_type_id: ConfigType.TEXT,
            });
            setImage(DEFAULT_IMAGE);
        }
        setFile(undefined);
    }, [config, show, reset]);

    const onChangeFile = (file: File) => {
        if (!isValidImageFile(file)) {
            showToast(false, [t('error.chooseImage')]);
            return;
        }
        setImage(URL.createObjectURL(file));
        setFile(file);
    };

    const onSubmit = async (data: Config) => {
        if (typeId === ConfigType.IMAGE && file) {
            const results = await FileService.upload(file, '', departmentId);
            if (results.upload) {
                data.content = results.upload.url;
            } else {
                showToast(false, [t('error.upload')]);
                return;
            }
        }
        if (includes([ConfigType.TEXT, ConfigType.NUMBER], typeId) && !data.content) {
            showToast(false, ['Vui lòng nhập nội dung']);
            return;
        }
        data.config_type_id = typeId;
        submitAction(data);
    };

    return (
        <>
            <div
                className={classNames('modal fade text-start modal-primary', { show })}
                style={{ display: show ? 'block' : 'none' }}
            >
                <div className="modal-dialog modal-dialog-centered modal-lg">
                    <div className="modal-content">
                        <div className="modal-header">
                            <h5 className="modal-title">{config ? 'Cập nhật cấu hình' : 'Thêm mới cấu hình'}</h5>
                            <button type="button" className="btn-close" onClick={() => changeShow(false)} />
                        </div>
                        <form className="validate-form pt-50" onSubmit={handleSubmit(onSubmit)}>
                            <div className="modal-body">
                                <div className="row">
                                    <div className="col-12 col-sm-6 mb-1">
                                        <label className="form-label">
                                            Mã cấu hình <span className="error">*</span>
                                        </label>
                                        <input
                                            {...register('code')}
                                            type="text"
                                            className={classNames('form-control', {
                                                'is-invalid': Boolean(errors.code?.message),
                                            })}
                                        />
                                        <span className="error">{errors.code?.message}</span>
                                    </div>
                                    <div className="col-12 col-sm-6 mb-1">
                                        <label className="form-label">
                                            Tên cấu hình <span className="error">*</span>
                                        </label>
                                        <input
                                            {...register('name')}
                                            type="text"
                                            className={classNames('form-control', {
                                                'is-invalid': Boolean(errors.name?.message),
                                            })}
                                        />
                                        <span className="error">{errors.name?.message}</span>
                                    </div>
                                    {!config && (
                                        <div className="col-12 col-sm-12 mb-1">
                                            <label className="form-label">
                                                Loại cấu hình <span className="error">*</span>
                                            </label>
                                            <div className="d-flex flex-wrap gap-2">
                                                {ConfigTypeNames.map(({ id, name }) => (
                                                    <div key={id} className="form-check form-check-inline">
                                                        <input
                                                            {...register('config_type_id')}
                                                            type="radio"
                                                            id={`type-${id}`}
                                                            value={String(id)}
                                                            className="form-check-input"
                                                        />
                                                        <label htmlFor={`type-${id}`} className="form-check-label">
                                                            {name}
                                                        </label>
                                                    </div>
                                                ))}
                                            </div>
                                        </div>
                                    )}
                                    <div className="col-12 mb-1">
                                        <label className="form-label">
                                            Nội dung <span className="error">*</span>
                                        </label>
                                        {includes([ConfigType.TEXT, ConfigType.NUMBER], typeId) && (
                                            <input
                                                {...register('content')}
                                                type={typeId === ConfigType.NUMBER ? 'number' : 'text'}
                                                className="form-control"
                                            />
                                        )}
                                        {typeId === ConfigType.IMAGE && (
                                            <UploadImage
                                                id={1}
                                                image={image}
                                                label="Tải lên ảnh"
                                                onChangeFile={onChangeFile}
                                            />
                                        )}
                                        {typeId === ConfigType.EDITOR && (
                                            <Editor
                                                apiKey={TINY_MCE_API_KEY}
                                                value={watch('content') ?? ''}
                                                onEditorChange={(value) => {
                                                    setValue('content', value);
                                                    if (value) clearErrors('content');
                                                }}
                                                init={{
                                                    height: 500,
                                                    menubar: true,
                                                    plugins: ['link', 'table', 'lists', 'code', 'media'],
                                                    toolbar:
                                                        'undo redo | formatselect | bold italic | alignleft aligncenter alignright | bullist numlist outdent indent | code',
                                                }}
                                            />
                                        )}
                                    </div>
                                </div>
                            </div>
                            <div className="modal-footer">
                                <UpdateButton btnText="Cập nhật" isLoading={isLoading} hasDivWrap={false} />
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            {show && <div className="modal-backdrop fade show" />}
        </>
    );
}
