import { yupResolver } from '@hookform/resolvers/yup';
import classNames from 'classnames';
import UpdateButton from 'components/partials/UpdateButton';
import { useEffect, useLayoutEffect } from 'react';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import Department from 'types/Department';
import { selectItem, toggleModalOpen } from 'utils/common';
import * as yup from 'yup';
import { ItemLanguageNames, ItemStatus } from '../../../types/common/Item';
import InputSwitch from '../../../components/partials/InputSwitch';

interface IProps {
    show: boolean;
    department: Department | undefined;
    listDepartments: Department[];
    isLoading?: boolean;
    changeShow: (s: boolean) => void;
    submitAction: (data: Department) => void;
}

export default function ModalDepartmentUpdate({
    show,
    department,
    listDepartments,
    isLoading,
    changeShow,
    submitAction,
}: Readonly<IProps>) {
    const { t } = useTranslation();
    useLayoutEffect(() => toggleModalOpen(show), [show]);

    const schema = yup
        .object({
            name: yup.string().required(t('error.required')).trim(),
            display_order: yup
                .number()
                .typeError(t('error.number'))
                .required(t('error.required'))
                .min(1, t('error.min_1'))
                .max(99, t('error.max_99')),
        })
        .required();

    const {
        register,
        handleSubmit,
        reset,
        formState: { errors },
    } = useForm<Department>({
        resolver: yupResolver(schema),
    });

    useEffect(() => {
        if (department && show) {
            reset({ ...department, status_id: department.status_id === ItemStatus.ACTIVE });
        } else {
            reset({
                name: '',
                desc: '',
                parent_id: 0,
                display_order: 1,
                status_id: true,
                language_id: 0,
            });
        }
    }, [department, show, reset]);

    return (
        <>
            <div
                className={classNames('modal fade text-start modal-primary', { show })}
                style={{ display: show ? 'block' : 'none' }}
            >
                <div className="modal-dialog modal-dialog-centered modal-lg">
                    <div className="modal-content">
                        <div className="modal-header">
                            <h5 className="modal-title">Cập nhật Đơn vị</h5>
                            <button type="button" className="btn-close" onClick={() => changeShow(false)} />
                        </div>
                        <form className="validate-form pt-50" onSubmit={handleSubmit(submitAction)}>
                            <div className="modal-body">
                                <div className="row">
                                    <div className="col-12 col-sm-6 mb-1">
                                        <label className="form-label">
                                            Tên đơn vị <span className="error">*</span>
                                        </label>
                                        <input
                                            {...register('name')}
                                            type="text"
                                            className={classNames('form-control', {
                                                'is-invalid': Boolean(errors.name?.message),
                                            })}
                                        />
                                        <span className="error">{errors.name?.message}</span>
                                    </div>
                                    <div className="col-12 col-sm-6 mb-1">
                                        <label className="form-label">Đơn vị cha</label>
                                        <select
                                            {...register('parent_id', { valueAsNumber: true })}
                                            className="form-select"
                                        >
                                            <option value="0">-- Chọn Đơn vị --</option>
                                            {listDepartments
                                                .filter((item) => !item.parent_id && item.id !== department?.id)
                                                .map((item) => (
                                                    <option key={item.id} value={item.id}>
                                                        {item.name}
                                                    </option>
                                                ))}
                                        </select>
                                    </div>
                                    <div className="col-12 col-sm-4 mb-1">
                                        <label className="form-label">
                                            Thứ tự <span className="error">*</span>
                                        </label>
                                        <input
                                            {...register('display_order')}
                                            type="text"
                                            className={classNames('form-control', {
                                                'is-invalid': Boolean(errors.display_order?.message),
                                            })}
                                        />
                                        <span className="error">{errors.display_order?.message}</span>
                                    </div>
                                    <div className="col-12 col-sm-4 mb-1">
                                        <label className="form-label">Ngôn Ngữ</label>
                                        <select
                                            {...register('language_id', { valueAsNumber: true })}
                                            className="form-select"
                                        >
                                            {selectItem(ItemLanguageNames, t)}
                                        </select>
                                    </div>
                                    <InputSwitch
                                        classNameWrap="col-12 col-sm-4 mb-1"
                                        className="d-flex flex-column"
                                        labelSwitchName="Hoạt động"
                                        labelFieldName="Trạng thái"
                                        name="status_id"
                                        register={register}
                                    />
                                    <div className="col-12 mb-1">
                                        <label className="form-label">Mô tả</label>
                                        <input type="text" {...register('desc')} className="form-control" />
                                    </div>
                                </div>
                            </div>
                            <div className="modal-footer">
                                <UpdateButton btnText="Cập nhật" isLoading={isLoading} hasDivWrap={false} />
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            {show && <div className="modal-backdrop fade show" />}
        </>
    );
}
