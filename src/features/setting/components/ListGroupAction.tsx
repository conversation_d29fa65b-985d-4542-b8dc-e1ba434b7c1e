import { includes, isEqual } from 'lodash';
import { useEffect, useState } from 'react';
import { Disc } from 'react-feather';
import { useTranslation } from 'react-i18next';
import Action from 'types/Action';

interface IProps {
    actions: Action[];
    actionIds: string[];
    loading: boolean;
    handleSubmit: (ids: string[]) => void;
}

export default function ListGroupAction({ actions, actionIds, loading, handleSubmit }: Readonly<IProps>) {
    const [ids, setIds] = useState<string[]>(actionIds);
    const { t } = useTranslation();

    useEffect(() => setIds(actionIds), [actionIds]);

    const toggleItem = (id: string, checked: boolean) => {
        const items = [...ids];
        if (checked) {
            if (!includes(items, id)) {
                items.push(id);
                setIds(items);
            }
        } else {
            const index = items.indexOf(id);
            if (index > -1) {
                items.splice(index, 1);
                setIds(items);
            }
        }
    };

    const grantActions = () => handleSubmit(ids);

    return (
        <div className="card">
            <div className="card-body">
                <div className="table-responsive">
                    <table className="table">
                        <thead>
                            <tr>
                                <th className="text-center thAction1">STT</th>
                                <th className="text-center thAction3">Lựa chọn</th>
                                <th>Chức năng</th>
                            </tr>
                        </thead>
                        <tbody>
                            {actions.map((action: Action, index: number) => (
                                <tr key={action.id}>
                                    <td className="text-center">{index + 1}</td>
                                    <td className="text-center">
                                        <input
                                            className="form-check-input"
                                            type="checkbox"
                                            id={`cb_${action.id}`}
                                            value={action?.id?.toString() ?? ''}
                                            checked={includes(ids, action?.id?.toString() ?? '')}
                                            onChange={(e) => toggleItem(e.target.value, e.target.checked)}
                                        />
                                    </td>
                                    <td>
                                        <label className="w-100" htmlFor={`cb_${action.id}`}>
                                            {action.parent_id && <Disc size={14} />} {action.name}
                                        </label>
                                    </td>
                                </tr>
                            ))}
                        </tbody>
                    </table>
                </div>
            </div>
            <div className="card-footer">
                <button
                    className="btn btn-primary waves-effect waves-float waves-light"
                    disabled={loading || isEqual(ids.sort(), actionIds.sort())}
                    onClick={grantActions}
                >
                    Cập nhật
                </button>
            </div>
        </div>
    );
}
