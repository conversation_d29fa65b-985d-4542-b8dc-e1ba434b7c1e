import { yupResolver } from '@hookform/resolvers/yup';
import classNames from 'classnames';
import UpdateButton from 'components/partials/UpdateButton';
import { useEffect, useLayoutEffect } from 'react';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import Group from 'types/Group';
import { toggleModalOpen } from 'utils/common';
import * as yup from 'yup';
import { ItemStatus } from '../../../types/common/Item';
import InputSwitch from '../../../components/partials/InputSwitch';

interface IProps {
    show: boolean;
    group: Group | undefined;
    groups: Group[];
    type: string | undefined;
    isLoading?: boolean;
    changeShow: (s: boolean) => void;
    submitAction: (data: Group) => void;
}

export default function ModalCategoryUpdate({
    show,
    group,
    groups,
    type,
    isLoading,
    changeShow,
    submitAction,
}: Readonly<IProps>) {
    const { t } = useTranslation();
    useLayoutEffect(() => toggleModalOpen(show), [show]);

    const schema = yup
        .object({
            name: yup.string().required(t('error.required')).trim(),
        })
        .required();

    const {
        register,
        handleSubmit,
        reset,
        formState: { errors },
    } = useForm<Group>({
        resolver: yupResolver(schema),
    });

    useEffect(() => {
        if (group && show) {
            reset({ ...group, parent_id: group.parent_id ?? 0, status_id: group.status_id === ItemStatus.ACTIVE });
        } else {
            reset({
                name: '',
                desc: '',
                parent_id: 0,
                status_id: true,
            });
        }
    }, [group, show, reset]);

    return (
        <>
            <div
                className={classNames('modal fade text-start modal-primary', { show })}
                style={{ display: show ? 'block' : 'none' }}
            >
                <div className="modal-dialog modal-dialog-centered modal-lg">
                    <div className="modal-content">
                        <div className="modal-header">
                            <h5 className="modal-title">{group ? t(`${type}.edit`) : t(`${type}.add`)}</h5>
                            <button type="button" className="btn-close" onClick={() => changeShow(false)} />
                        </div>
                        <form className="validate-form pt-50" onSubmit={handleSubmit(submitAction)}>
                            <div className="modal-body">
                                <div className="row">
                                    <div className="col-12 mb-1">
                                        <label className="form-label">
                                            Tên <span className="error">*</span>
                                        </label>
                                        <input
                                            {...register('name')}
                                            type="text"
                                            className={classNames('form-control', {
                                                'is-invalid': Boolean(errors.name?.message),
                                            })}
                                        />
                                        <span className="error">{errors.name?.message}</span>
                                    </div>
                                    <div className="col-12 mb-1">
                                        <label className="form-label">Mô tả</label>
                                        <input type="text" {...register('desc')} className="form-control" />
                                    </div>
                                    {type === 'department' && (
                                        <div className="col-12 col-sm-6 mb-1">
                                            <label className="form-label">Phòng ban cha</label>
                                            <select
                                                {...register('parent_id', { valueAsNumber: true })}
                                                className="form-select"
                                            >
                                                <option value="0">-- Chọn phòng ban --</option>
                                                {groups
                                                    .filter((item) => item.id !== group?.id)
                                                    .map((item) => (
                                                        <option key={item.id} value={item.id}>
                                                            {item.name}
                                                        </option>
                                                    ))}
                                            </select>
                                        </div>
                                    )}
                                    <InputSwitch
                                        classNameWrap="col-12 col-sm-4 mb-1"
                                        className="d-flex flex-column"
                                        labelSwitchName="Hoạt động"
                                        labelFieldName="Trạng thái"
                                        name="status_id"
                                        register={register}
                                    />
                                </div>
                            </div>
                            <div className="modal-footer">
                                <UpdateButton
                                    btnText={group ? 'Cập nhật' : 'Thêm mới'}
                                    isLoading={isLoading}
                                    hasDivWrap={false}
                                />
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            {show && <div className="modal-backdrop fade show" />}
        </>
    );
}
