import { keepPreviousData } from '@tanstack/react-query';
import { find, isEmpty } from 'lodash';
import isUndefined from 'lodash/isUndefined';
import omitBy from 'lodash/omitBy';
import { useEffect, useMemo, useState } from 'react';
import { Plus } from 'react-feather';
import { Helmet } from 'react-helmet-async';
import { useTranslation } from 'react-i18next';
import { ISSUE_PAGE_LIST } from 'services/IssuePageService';
import { ISSUE_LIST } from 'services/IssueService';
import { issuePageFilterConfig, IssuePageQuery } from 'types/IssuePage';
import ContentHeader from '../../../components/partials/ContentHeader';
import ModalConfirm from '../../../components/partials/ModalConfirm';
import PaginationTable from '../../../components/partials/PaginationTable';
import SearchForm from '../../../components/partials/SearchForm';
import Spinner from '../../../components/partials/Spinner';
import { LIMIT_MAX, PAGE_NUMBER_DEFAULT, PAGINATION, QUERY_KEY } from '../../../constants/common';
import { useGraphQLMutation } from '../../../hooks/useGraphQLMutation';
import { useGraphQLQuery } from '../../../hooks/useGraphQLQuery';
import useQueryParams from '../../../hooks/useQueryParams';
import {
    ISSUE_PAGE_FILE_CREATE,
    ISSUE_PAGE_FILE_DETELE,
    ISSUE_PAGE_FILE_LIST,
    ISSUE_PAGE_FILE_UPDATE,
} from '../../../services/IssuePageFileService';
import { PRESS_PUBLICATIONS_LIST } from '../../../services/PressPublicationSevice';
import { useAppStore } from '../../../stores/appStore';
import { baseFilterConfig, BaseSearch, BaseSearchParam } from '../../../types/common';
import { ItemStatus, PublishStatusNames } from '../../../types/common/Item';
import { issueFilterConfig, IssueQuery, SearchIssueParam } from '../../../types/Issue';
import IssuePageFile, {
    issueApproveFilterConfig,
    IssuePageFileQuery,
    SearchIssuePageFileParam,
} from '../../../types/IssuePageFile';
import { PressPublicationQuery } from '../../../types/PressPublication';
import { convertConstantToSelectOptions, convertPaging, generateFilters, showToast } from '../../../utils/common';
import IssueTabFilter from '../components/IssueTabFilter';
import ListIssuePageFile from '../components/ListIssuePageFile';
import ModalIssuePageFileUpdate from '../components/ModalIssuePageFileUpdate';
import PressPublicationsTabFilter from '../components/PressPublicationsTabFilter';

export default function IssuePageFileList() {
    const { t } = useTranslation();
    const [showUpdate, setShowUpdate] = useState(false);
    const [showDelete, setShowDelete] = useState(false);
    const [itemId, setItemId] = useState<number>(0);
    const departmentId = useAppStore((state) => state.departmentId);
    const [activeTab, setActiveTab] = useState('');
    const [activeIssueTab, setActiveIssueTab] = useState('');

    const { queryParams, setQueryParams } = useQueryParams<SearchIssuePageFileParam>();
    const paramConfig: SearchIssuePageFileParam = omitBy(
        {
            limit: queryParams.limit ?? PAGINATION.limit,
            page: queryParams.page ?? '1',
            search: queryParams.search,
            status_id: queryParams.status_id,
            issue_id: queryParams.issue_id,
            'issue.press_publication_id': activeTab === '' ? undefined : queryParams['issue.press_publication_id'],
        },
        isUndefined
    );

    const { search, limit, page, ...dataParamConfig } = paramConfig;
    const filters = generateFilters(dataParamConfig, issueApproveFilterConfig);

    const paramConfigPressPublication: BaseSearchParam = {
        status_id: ItemStatus.ACTIVE.toString(),
        department_id: departmentId.toString(),
    };
    const filtersPressPublication = generateFilters(paramConfigPressPublication, baseFilterConfig);
    const { data: pressPublicationData } = useGraphQLQuery<PressPublicationQuery, BaseSearch>(
        [QUERY_KEY.PRESS_PUBLICATIONS, paramConfigPressPublication, filtersPressPublication],
        PRESS_PUBLICATIONS_LIST,
        {
            page: PAGE_NUMBER_DEFAULT,
            limit: LIMIT_MAX,
            filters: filtersPressPublication.length > 0 ? filtersPressPublication : undefined,
            sorts: ['display_order:ASC'],
        },
        '',
        {
            enabled: !!paramConfigPressPublication.department_id,
            placeholderData: keepPreviousData,
        }
    );
    // eslint-disable-next-line react-hooks/exhaustive-deps
    const pressPublications = pressPublicationData?.press_publications_list.data ?? [];

    const paramConfigIssue: SearchIssueParam = {
        ...paramConfigPressPublication,
        press_publication_id: paramConfig['issue.press_publication_id'],
    };
    const filtersIssue = generateFilters(paramConfigIssue, issueFilterConfig);

    const { data: issueData } = useGraphQLQuery<IssueQuery, BaseSearch>(
        [QUERY_KEY.ISSUES, paramConfigIssue, filtersIssue],
        ISSUE_LIST,
        {
            page: PAGE_NUMBER_DEFAULT,
            limit: LIMIT_MAX,
            filters: filtersIssue.length > 0 ? filtersIssue : undefined,
        },
        '',
        {
            enabled: !!paramConfigIssue.department_id && !!paramConfigIssue.press_publication_id,
            placeholderData: keepPreviousData,
        }
    );

    // eslint-disable-next-line react-hooks/exhaustive-deps
    const issues = issueData?.issues_list.data ?? [];

    const { data, isLoading, isRefetching, refetch } = useGraphQLQuery<IssuePageFileQuery>(
        [QUERY_KEY.ISSUE_PAGE_FILES, paramConfig, filters],
        ISSUE_PAGE_FILE_LIST,
        {
            page: Number(page),
            limit: Number(limit),
            search,
            filters: filters.length > 0 ? filters : undefined,
        },
        '',
        {
            placeholderData: keepPreviousData,
        }
    );

    const issuePageFiles = data?.issue_page_files_list.data ?? [];

    useEffect(() => {
        if (!isEmpty(pressPublications) && !activeTab) {
            const firstTabId = pressPublications[0].id!.toString();

            const currentTabId = queryParams['issue.press_publication_id'] || firstTabId;

            setActiveTab(currentTabId);
            setQueryParams({
                ...queryParams,
                'issue.press_publication_id': currentTabId,
                page: '1',
            });
        }
    }, [pressPublications, activeTab, setQueryParams, queryParams]);

    const paramIssueConfig = omitBy(
        {
            press_publication_id: activeTab,
            issue_id: activeIssueTab,
            department_id: departmentId,
            status_id: ItemStatus.ACTIVE,
        },
        isUndefined
    );

    const issueFilters = generateFilters(paramIssueConfig, issuePageFilterConfig);

    const { data: issuePageResponse } = useGraphQLQuery<IssuePageQuery>(
        [QUERY_KEY.ISSUE_PAGES, paramIssueConfig, filters],
        ISSUE_PAGE_LIST,
        {
            page: 1,
            limit: LIMIT_MAX,
            filters: issueFilters,
        },
        '',
        {
            enabled: !!(paramIssueConfig.press_publication_id && paramIssueConfig.issue_id),
            placeholderData: keepPreviousData,
        }
    );
    const issuePages = useMemo(() => issuePageResponse?.issue_pages_list.data ?? [], [issuePageResponse]);

    useEffect(() => {
        if (!isEmpty(issues) && !activeIssueTab) {
            const firstTabId = issues[0].id!.toString();

            const currentTabId = queryParams.issue_id || firstTabId;
            setActiveIssueTab(currentTabId);
            setQueryParams({
                ...queryParams,
                issue_id: currentTabId,
                page: '1',
            });
        }
    }, [issues, activeIssueTab, setQueryParams, queryParams]);

    const saveMutation = useGraphQLMutation<{}, { id?: number; body: Partial<IssuePageFile> }>(
        itemId > 0 ? ISSUE_PAGE_FILE_UPDATE : ISSUE_PAGE_FILE_CREATE,
        '',
        {
            onSuccess: () => {
                showToast(true, [t('success.update')]);
                setShowUpdate(false);
                setItemId(0);
                refetch();
            },
            onError: () => showToast(false, [t('error.common')]),
        }
    );

    const deleteMutation = useGraphQLMutation(ISSUE_PAGE_FILE_DETELE, '', {
        onSuccess: () => {
            showToast(true, [t('success.delete')]);
            setShowDelete(false);
            setItemId(0);
            refetch();
        },
        onError: () => showToast(false, [t('error.common')]),
    });

    const updateItem = async (body: IssuePageFile) => {
        delete body.id;
        if (itemId === 0) {
            saveMutation.mutate({ body });
        } else {
            saveMutation.mutate({ id: itemId, body });
        }
    };

    const deleteItem = async () => {
        if (itemId) {
            deleteMutation.mutate({ id: itemId });
        }
    };

    const handleEdit = (id: number) => {
        saveMutation.reset();
        setItemId(id);
        setShowUpdate(true);
    };

    const handleDelete = (id: number) => {
        deleteMutation.reset();
        setItemId(id);
        setShowDelete(true);
    };

    const handlePageChange = (_event: React.ChangeEvent<unknown>, page: number) => {
        setQueryParams({ ...queryParams, page: page.toString() });
    };

    const actionMenu = () => {
        saveMutation.reset();
        setShowUpdate(true);
        setItemId(0);
    };

    const handleTabChange = (tabId: string) => {
        setActiveTab(tabId);
        setQueryParams({
            ...queryParams,
            'issue.press_publication_id': tabId,
            page: '1',
        });
    };
    const handleIssueTabChange = (tabId: string) => {
        setActiveIssueTab(tabId);
        setQueryParams({
            ...queryParams,
            issue_id: tabId,
            page: '1',
        });
    };

    return (
        <>
            <Helmet>
                <title>Duyệt trang báo</title>
            </Helmet>
            <ContentHeader title="Duyệt trang báo" />
            <div className="content-body">
                <div className="col-12">
                    <div className="mt-2">
                        {!isEmpty(pressPublications) && (
                            <PressPublicationsTabFilter
                                activeTab={activeTab}
                                onTabChange={handleTabChange}
                                pressPublications={pressPublications}
                            />
                        )}
                        {!isEmpty(issues) && (
                            <IssueTabFilter
                                activeTab={activeIssueTab}
                                onTabChange={handleIssueTabChange}
                                issues={issues}
                            />
                        )}
                    </div>

                    <SearchForm
                        fields={[
                            { name: 'search', type: 'text', label: 'Từ khóa', wrapClassName: 'col-md-4 col-12' },
                            {
                                name: 'status_id',
                                type: 'select',
                                label: 'Trạng thái',
                                wrapClassName: 'col-md-4 col-12',
                                options: {
                                    multiple: true,
                                    choices: convertConstantToSelectOptions(PublishStatusNames, t, true),
                                },
                            },
                        ]}
                        isLoading={isLoading || isRefetching}
                    />

                    <div className="d-flex justify-content-end mb-2">
                        <button
                            className="d-flex gap-25 align-items-center btn-icon btn btn-primary btn-round btn-sm waves-effect waves-float waves-light"
                            type="button"
                            onClick={actionMenu}
                        >
                            <Plus size={16} />
                            <span className="text-sm">Thêm duyệt trang báo</span>
                        </button>
                    </div>

                    {(isLoading || isRefetching) && <Spinner />}
                    {!isLoading && !isRefetching && data && (
                        <>
                            <div className="card">
                                <ListIssuePageFile
                                    items={issuePageFiles}
                                    issues={issues}
                                    paging={convertPaging<IssuePageFile, BaseSearchParam>(
                                        data.issue_page_files_list,
                                        Number(paramConfig.limit)
                                    )}
                                    handleEdit={handleEdit}
                                    handleDelete={handleDelete}
                                    issuePages={issuePages}
                                />
                                <PaginationTable
                                    countItem={data.issue_page_files_list.totalCount}
                                    totalPage={data.issue_page_files_list.totalPages}
                                    currentPage={data.issue_page_files_list.currentPage}
                                    handlePageChange={handlePageChange}
                                />
                            </div>
                            <ModalIssuePageFileUpdate
                                show={showUpdate}
                                issuePageFile={find(issuePageFiles, { id: itemId })}
                                pressPublications={pressPublications}
                                issues={issues}
                                departmentId={departmentId}
                                isLoading={saveMutation.isPending}
                                changeShow={(s: boolean) => setShowUpdate(s)}
                                submitAction={updateItem}
                                selectedIssueId={activeIssueTab}
                                issuePages={issuePages}
                            />
                        </>
                    )}
                    <ModalConfirm
                        show={showDelete}
                        text={t('confirm.delete')}
                        btnDisabled={deleteMutation.isPending}
                        changeShow={(s: boolean) => setShowDelete(s)}
                        submitAction={deleteItem}
                    />
                </div>
            </div>
        </>
    );
}
