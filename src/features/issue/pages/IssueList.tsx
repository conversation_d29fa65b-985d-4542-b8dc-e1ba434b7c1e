import { keepPreviousData } from '@tanstack/react-query';
import { find, isEmpty } from 'lodash';
import isUndefined from 'lodash/isUndefined';
import omitBy from 'lodash/omitBy';
import { useEffect, useState } from 'react';
import { Helmet } from 'react-helmet-async';
import { useTranslation } from 'react-i18next';
import ContentHeader from '../../../components/partials/ContentHeader';
import ModalConfirm from '../../../components/partials/ModalConfirm';
import PaginationTable from '../../../components/partials/PaginationTable';
import SearchForm from '../../../components/partials/SearchForm';
import Spinner from '../../../components/partials/Spinner';
import { LIMIT_MAX, PAGE_NUMBER_DEFAULT, PAGINATION, QUERY_KEY } from '../../../constants/common';
import { useGraphQLMutation } from '../../../hooks/useGraphQLMutation';
import { useGraphQLQuery } from '../../../hooks/useGraphQLQuery';
import useQueryParams from '../../../hooks/useQueryParams';
import { ISSUE_CREATE, ISSUE_DELETE, ISSUE_LIST, ISSUE_UPDATE } from '../../../services/IssueService';
import { PRESS_PUBLICATIONS_LIST } from '../../../services/PressPublicationSevice';
import { useAppStore } from '../../../stores/appStore';
import { baseFilterConfig, BaseSearch, BaseSearchParam } from '../../../types/common';
import { ItemStatus, PublishStatusNames } from '../../../types/common/Item';
import Issue, { issueFilterConfig, IssueQuery, SearchIssueParam } from '../../../types/Issue';
import { PressPublicationQuery } from '../../../types/PressPublication';
import { convertConstantToSelectOptions, convertPaging, generateFilters, showToast } from '../../../utils/common';
import ListIssue from '../components/ListIssue';
import ModalIssueUpdate from '../components/ModalIssueUpdate';
import PressPublicationsTabFilter from '../components/PressPublicationsTabFilter';

export default function IssueList() {
    const { t } = useTranslation();
    const [showUpdate, setShowUpdate] = useState(false);
    const [showDelete, setShowDelete] = useState(false);
    const [itemId, setItemId] = useState<number>(0);
    const [activeTab, setActiveTab] = useState('');

    const departmentId = useAppStore((state) => state.departmentId);

    const { queryParams, setQueryParams } = useQueryParams<SearchIssueParam>();
    const paramConfig: SearchIssueParam = omitBy(
        {
            limit: queryParams.limit ?? PAGINATION.limit,
            page: queryParams.page ?? '1',
            search: queryParams.search,
            status_id: queryParams.status_id,
            department_id: departmentId.toString(),
            press_publication_id: activeTab === '' ? undefined : queryParams.press_publication_id,
        },
        isUndefined
    );

    const { search, limit, page, ...dataParamConfig } = paramConfig;
    const filters = generateFilters(dataParamConfig, issueFilterConfig);

    const paramConfigPressPublication: BaseSearchParam = {
        status_id: ItemStatus.ACTIVE.toString(),
        department_id: departmentId.toString(),
    };
    const filtersPressPublication = generateFilters(paramConfigPressPublication, baseFilterConfig);
    const { data: pressPublicationData } = useGraphQLQuery<PressPublicationQuery, BaseSearch>(
        [QUERY_KEY.PRESS_PUBLICATIONS, paramConfigPressPublication, filtersPressPublication],
        PRESS_PUBLICATIONS_LIST,
        {
            page: PAGE_NUMBER_DEFAULT,
            limit: LIMIT_MAX,
            filters: filtersPressPublication.length > 0 ? filtersPressPublication : undefined,
            sorts: ['display_order:ASC'],
        },
        '',
        {
            enabled: !!paramConfigPressPublication.department_id,
            placeholderData: keepPreviousData,
        }
    );
    // eslint-disable-next-line react-hooks/exhaustive-deps
    const pressPublications = pressPublicationData?.press_publications_list.data ?? [];

    const { data, isLoading, isRefetching, refetch } = useGraphQLQuery<IssueQuery>(
        [QUERY_KEY.ISSUES, paramConfig, filters],
        ISSUE_LIST,
        {
            page: Number(page),
            limit: Number(limit),
            search,
            filters: filters.length > 0 ? filters : undefined,
        },
        '',
        {
            enabled: !!paramConfig.department_id && !!paramConfig.press_publication_id,
            placeholderData: keepPreviousData,
        }
    );
    const issues = data?.issues_list.data ?? [];

    const saveMutation = useGraphQLMutation<{}, { id?: number; body: Partial<Issue> }>(
        itemId > 0 ? ISSUE_UPDATE : ISSUE_CREATE,
        '',
        {
            onSuccess: () => {
                showToast(true, [t('success.update')]);
                setShowUpdate(false);
                setItemId(0);
                refetch();
            },
            onError: () => showToast(false, [t('error.common')]),
        }
    );

    const deleteMutation = useGraphQLMutation(ISSUE_DELETE, '', {
        onSuccess: () => {
            showToast(true, [t('success.delete')]);
            setShowDelete(false);
            setItemId(0);
            refetch();
        },
        onError: () => showToast(false, [t('error.common')]),
    });

    const updateItem = async (body: Issue) => {
        body.department_id = departmentId;
        delete body.id;
        if (itemId === 0) {
            saveMutation.mutate({ body });
        } else {
            saveMutation.mutate({ id: itemId, body });
        }
    };

    const deleteItem = async () => {
        if (itemId) {
            deleteMutation.mutate({ id: itemId });
        }
    };

    const handleEdit = (id: number) => {
        saveMutation.reset();
        setItemId(id);
        setShowUpdate(true);
    };

    const handleDelete = (id: number) => {
        deleteMutation.reset();
        setItemId(id);
        setShowDelete(true);
    };

    const handlePageChange = (_event: React.ChangeEvent<unknown>, page: number) => {
        setQueryParams({ ...queryParams, page: page.toString() });
    };

    const actionMenu = () => {
        saveMutation.reset();
        setShowUpdate(true);
        setItemId(0);
    };

    useEffect(() => {
        if (!isEmpty(pressPublications) && !activeTab) {
            const firstTabId = pressPublications[0].id!.toString();
            setActiveTab(firstTabId);
            setQueryParams({
                ...queryParams,
                press_publication_id: firstTabId,
                page: '1',
            });
        }
    }, [pressPublications, activeTab, setQueryParams, queryParams]);

    const handleTabChange = (tabId: string) => {
        setActiveTab(tabId);
        setQueryParams({
            ...queryParams,
            press_publication_id: tabId,
            page: '1',
        });
    };

    return (
        <>
            <Helmet>
                <title>Quản trị số báo</title>
            </Helmet>
            <ContentHeader
                title="Quản trị số báo"
                contextMenu={[
                    {
                        text: 'Thêm số báo',
                        to: '',
                        icon: 'PLUS',
                        fnCallBack: { actionMenu },
                    },
                ]}
            />
            <div className="content-body">
                <div className="col-12">
                    <SearchForm
                        fields={[
                            { name: 'search', type: 'text', label: 'Từ khóa', wrapClassName: 'col-md-4 col-12' },
                            {
                                name: 'status_id',
                                type: 'select',
                                label: 'Trạng thái',
                                wrapClassName: 'col-md-4 col-12',
                                options: {
                                    multiple: true,
                                    choices: convertConstantToSelectOptions(PublishStatusNames, t, true),
                                },
                            },
                        ]}
                        isLoading={isLoading || isRefetching}
                        searchClass="col-md-4 pt-2"
                    />
                    <div className="mt-2">
                        <PressPublicationsTabFilter
                            activeTab={activeTab}
                            onTabChange={handleTabChange}
                            pressPublications={pressPublications}
                        />
                    </div>
                    {(isLoading || isRefetching) && <Spinner />}
                    {!isLoading && !isRefetching && data && (
                        <>
                            <div className="card">
                                <ListIssue
                                    items={issues}
                                    paging={convertPaging<Issue, BaseSearchParam>(
                                        data.issues_list,
                                        Number(paramConfig.limit)
                                    )}
                                    handleEdit={handleEdit}
                                    handleDelete={handleDelete}
                                />
                                <PaginationTable
                                    countItem={data.issues_list.totalCount}
                                    totalPage={data.issues_list.totalPages}
                                    currentPage={data.issues_list.currentPage}
                                    handlePageChange={handlePageChange}
                                />
                            </div>
                            <ModalIssueUpdate
                                show={showUpdate}
                                issue={find(issues, { id: itemId })}
                                departmentId={departmentId}
                                pressPublications={pressPublications}
                                isLoading={saveMutation.isPending}
                                changeShow={(s: boolean) => setShowUpdate(s)}
                                submitAction={updateItem}
                            />
                        </>
                    )}
                    <ModalConfirm
                        show={showDelete}
                        text={t('confirm.delete')}
                        btnDisabled={deleteMutation.isPending}
                        changeShow={(s: boolean) => setShowDelete(s)}
                        submitAction={deleteItem}
                    />
                </div>
            </div>
        </>
    );
}
