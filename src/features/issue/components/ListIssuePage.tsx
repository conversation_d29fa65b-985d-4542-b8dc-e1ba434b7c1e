import IssuePage from 'types/IssuePage';
import { Trash2 } from 'react-feather';
import { Paging } from '../../../types/common';
import { genTableIndex, getFieldHtml, getFieldInArrayObject } from '../../../utils/common';
import { useTranslation } from 'react-i18next';
import { PressPublication } from 'types/PressPublication';
import { ApproveStatusNames, PublishStatusNames } from '../../../types/common/Item';

interface IProps {
    items: IssuePage[];
    pressPublications: PressPublication[];
    paging: Paging;
    handleEdit: (id: number) => void;
    handleDelete: (id: number) => void;
}

export default function ListIssuePage({
    items,
    pressPublications,
    paging,
    handleEdit,
    handleDelete,
}: Readonly<IProps>) {
    const { t } = useTranslation();

    return (
        <div className="table-responsive">
            <table className="table">
                <thead>
                    <tr>
                        <th className="text-center">STT</th>
                        <th>Tên trang</th>
                        <th>Ấn phẩm</th>
                        <th>Số báo</th>
                        <th className="text-center">Trạng thái</th>
                        <th className="text-center">Trạng thái duyệt</th>
                        <th className="thAction1"></th>
                    </tr>
                </thead>
                <tbody>
                    {items.map((item: IssuePage, index: number) => (
                        <tr key={item.id}>
                            <td className="text-center">{genTableIndex(index, paging.limit, paging.current_page)}</td>
                            <td>
                                <span className="text-primary cursor-pointer" onClick={() => handleEdit(item.id!)}>
                                    {item.name}
                                </span>
                            </td>
                            <td>{getFieldInArrayObject(pressPublications, item.press_publication_id)}</td>
                            <td>{item.issue?.name}</td>
                            <td className="text-center">
                                {getFieldHtml(PublishStatusNames, item.status_id as number, t)}
                            </td>
                            <td className="text-center">
                                {getFieldHtml(ApproveStatusNames, item.approve_status_id as number, t)}
                            </td>
                            <td className="text-center">
                                <button
                                    type="button"
                                    title="Xoá"
                                    className="btn btn-icon btn-sm btn-flat-danger waves-effect"
                                    onClick={() => handleDelete(item.id!)}
                                >
                                    <Trash2 size={14} />
                                </button>
                            </td>
                        </tr>
                    ))}
                </tbody>
            </table>
        </div>
    );
}
