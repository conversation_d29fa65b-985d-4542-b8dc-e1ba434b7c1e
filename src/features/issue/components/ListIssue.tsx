import Issue from 'types/Issue';
import { Trash2 } from 'react-feather';
import { Paging } from '../../../types/common';
import { genTableIndex, getFieldHtml } from '../../../utils/common';
import { useTranslation } from 'react-i18next';
import { PublishStatusNames } from '../../../types/common/Item';
import { FORMAT_DATE, formatDateTime } from '../../../utils/date';

interface IProps {
    items: Issue[];
    paging: Paging;
    handleEdit: (id: number) => void;
    handleDelete: (id: number) => void;
}

export default function ListIssue({ items, paging, handleEdit, handleDelete }: Readonly<IProps>) {
    const { t } = useTranslation();

    return (
        <div className="table-responsive">
            <table className="table">
                <thead>
                    <tr>
                        <th className="text-center">STT</th>
                        <th>Tiêu đề</th>
                        <th className="text-center">Số báo trong năm</th>
                        <th className="text-center">Số báo tất cả</th>
                        <th className="text-center">Ngày xuất bản</th>
                        <th className="text-center">Trạng thái</th>
                        <th className="thAction1"></th>
                    </tr>
                </thead>
                <tbody>
                    {items.map((item: Issue, index: number) => (
                        <tr key={item.id}>
                            <td className="text-center">{genTableIndex(index, paging.limit, paging.current_page)}</td>
                            <td>
                                <span className="text-primary cursor-pointer" onClick={() => handleEdit(item.id!)}>
                                    {item.name}
                                </span>
                            </td>
                            <td className="text-center">{item.year_count}</td>
                            <td className="text-center">{item.all_count}</td>
                            <td className="text-center">
                                {item.publish_date && formatDateTime(item.publish_date, FORMAT_DATE.SHOW_ONLY_DATE)}
                            </td>
                            <td className="text-center">
                                {getFieldHtml(PublishStatusNames, item.status_id as number, t)}
                            </td>
                            <td className="text-center">
                                <button
                                    type="button"
                                    title="Xoá"
                                    className="btn btn-icon btn-sm btn-flat-danger waves-effect"
                                    onClick={() => handleDelete(item.id!)}
                                >
                                    <Trash2 size={14} />
                                </button>
                            </td>
                        </tr>
                    ))}
                </tbody>
            </table>
        </div>
    );
}
