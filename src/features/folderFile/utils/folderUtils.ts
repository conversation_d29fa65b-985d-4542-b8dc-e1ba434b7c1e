import FolderType from '../../../types/Folder';

/**
 * Lọc danh sách folder để loại trừ folder hiện tại và tất cả folder con của nó
 * @param folders - Danh sách tất cả folder
 * @param excludeFolderId - ID của folder cần loại trừ
 * @param folderChildren - Object chứa children của các folder
 * @returns Danh sách folder đã được lọc
 */
export const filterFoldersForMove = (
    folders: FolderType[],
    excludeFolderId: number,
    folderChildren: Record<number, FolderType[]>
): FolderType[] => {
    // Tạo Set chứa tất cả ID của folder cần loại trừ (bao gồm cả children)
    const excludeIds = new Set<number>();

    // Thêm ID của folder hiện tại
    excludeIds.add(excludeFolderId);

    // Hàm đệ quy để tìm tất cả children
    const addChildrenIds = (folderId: number) => {
        const children = folderChildren[folderId];
        if (children && children.length > 0) {
            children.forEach((child) => {
                if (child.id) {
                    excludeIds.add(child.id);
                    addChildrenIds(child.id);
                }
            });
        }
    };

    // Tìm tất cả children của folder cần loại trừ
    addChildrenIds(excludeFolderId);

    // Lọc danh sách folder
    return folders.filter((folder) => folder.id && !excludeIds.has(folder.id));
};

/**
 * Tạo cấu trúc folder tree từ danh sách folder phẳng
 * @param folders - Danh sách folder đã được lọc
 * @returns Danh sách folder root
 */
export const buildFolderTree = (folders: FolderType[]): FolderType[] => {
    const folderMap = new Map<number, FolderType>();
    const rootFolders: FolderType[] = [];

    folders.forEach((folder) => {
        if (folder.id) {
            folderMap.set(folder.id, { ...folder, children: [] });
        }
    });

    folders.forEach((folder) => {
        if (folder.id) {
            const folderNode = folderMap.get(folder.id);
            if (folderNode) {
                if (folder.parent_id) {
                    const parent = folderMap.get(folder.parent_id);
                    if (parent) {
                        parent.children.push(folderNode);
                    } else {
                        rootFolders.push(folderNode);
                    }
                } else {
                    rootFolders.push(folderNode);
                }
            }
        }
    });

    return rootFolders;
};
