import React, { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import ModalContent from '../../../components/partials/ModalContent';
import UpdateButton from '../../../components/partials/UpdateButton';
import { useGraphQLMutation } from '../../../hooks/useGraphQLMutation';
import { FOLDERS_CREATE, FOLDERS_UPDATE } from '../../../services/FolderService';
import { FolderCreateRes, FolderUpdateRes } from '../../../types/Folder';
import { useAppStore } from '../../../stores/appStore';
import { showToast } from '../../../utils/common';
import Groups from 'types/Group';
import User from 'types/User';
import Select from 'react-select';

interface ModalFolderFormProps {
    show: boolean;
    onClose: () => void;
    groups: Groups[];
    editingFolder: { id?: number; name?: string; parent_id?: number; user_ids?: number[]; users?: User[] } | null;
    onSuccess: () => void;
    isNewsroom?: boolean;
}

interface FolderFormData {
    name: string;
    user_ids?: number[];
    users: User[];
}

const ModalFolderForm = ({
    show,
    onClose,
    groups,
    editingFolder,
    onSuccess,
    isNewsroom = false,
}: Readonly<ModalFolderFormProps>) => {
    const [selectedGroup, setSelectedGroup] = useState<number | null>(null);
    const [selectedUserIds, setSelectedUserIds] = useState<number[]>([]);
    const departmentId = useAppStore((state) => state.departmentId);
    const isEditing = editingFolder?.id !== undefined;

    const schema = yup.object({
        name: yup.string().required('Tên folder là bắt buộc').trim(),
    });

    const {
        register,
        handleSubmit,
        formState: { errors },
        reset,
    } = useForm<FolderFormData>({
        resolver: yupResolver(schema),
    });

    const createMutation = useGraphQLMutation<
        FolderCreateRes,
        { body: { name: string; department_id: number; parent_id?: number; is_newsroom: boolean; user_ids: number[] } }
    >(FOLDERS_CREATE, '', {
        onSuccess: () => {
            showToast(true, ['Tạo folder thành công']);
            onSuccess();
            reset();
        },
    });

    const updateMutation = useGraphQLMutation<
        FolderUpdateRes,
        { id: number; body: { name: string; department_id: number; parent_id?: number; user_ids: number[] } }
    >(FOLDERS_UPDATE, '', {
        onSuccess: () => {
            showToast(true, ['Cập nhật folder thành công']);
            onSuccess();
            reset();
        },
    });

    const onSubmit = (data: FolderFormData) => {
        if (selectedGroup === null) {
            showToast(false, ['Vui lòng chọn phòng ban']);
            return;
        }
        if (selectedUserIds.length === 0) {
            showToast(false, ['Vui lòng chọn người dùng']);
            return;
        }
        const folderData = {
            name: data.name.trim(),
            department_id: departmentId,
            parent_id: editingFolder?.parent_id,
            is_newsroom: isNewsroom,
            user_ids: selectedUserIds,
        };

        if (isEditing && editingFolder?.id) {
            updateMutation.mutate({
                id: editingFolder.id,
                body: folderData,
            });
        } else {
            createMutation.mutate({
                body: folderData,
            });
        }
    };

    const handleClose = () => {
        reset();
        onClose();
        setSelectedUserIds([]);
    };

    useEffect(() => {
        if (editingFolder && show) {
            reset({
                name: editingFolder.name ?? '',
            });

            if (editingFolder.users && editingFolder.users.length > 0) {
                const selectedOptions = editingFolder.users.map((user) => ({
                    value: user.id!,
                    label: user.full_name || user.user_name,
                }));

                const selectedIds = selectedOptions.map((opt) => opt.value);

                setSelectedUserIds(selectedIds);
            }
        } else if (show && !editingFolder) {
            reset({ name: '' });
            setSelectedUserIds([]);
        }
    }, [editingFolder, show, reset]);

    const isLoading = createMutation.isPending || updateMutation.isPending;

    const modalContent = (
        <form onSubmit={handleSubmit(onSubmit)}>
            <div className="modal-body">
                <div className="row">
                    <div className="col-12">
                        <div className="mb-1">
                            <label className="form-label" htmlFor="folder-name">
                                Tên folder <span className="text-danger">*</span>
                            </label>
                            <input
                                type="text"
                                id="folder-name"
                                className={`form-control ${errors.name ? 'is-invalid' : ''}`}
                                placeholder="Nhập tên folder"
                                {...register('name')}
                                disabled={isLoading}
                            />
                            {errors.name && <div className="invalid-feedback">{errors.name.message}</div>}
                        </div>

                        <div className="mb-1">
                            <label className="form-label" htmlFor="folder-name">
                                Chọn phòng ban <span className="text-danger">*</span>
                            </label>
                            <select
                                className="form-select"
                                onChange={(e) => {
                                    const value = Number(e.target.value);
                                    setSelectedGroup(value !== 0 ? value : null);
                                }}
                            >
                                <option value="0">-- Chọn phòng ban --</option>
                                {groups.map((item) => (
                                    <option key={item.id} value={item.id}>
                                        {item.name}
                                    </option>
                                ))}
                            </select>
                        </div>

                        {selectedGroup && (
                            <div className="col-12 mb-1">
                                <label className="form-label">
                                    Chọn người dùng <span className="text-danger">*</span>
                                </label>

                                <Select
                                    options={
                                        groups
                                            .find((group) => group.id === selectedGroup)
                                            ?.users?.map((user) => ({
                                                value: user.id,
                                                label: user.full_name || user.user_name,
                                            })) || []
                                    }
                                    isMulti
                                    onChange={(selectedOptions) => {
                                        const selectedIds = selectedOptions
                                            .map((option) => option.value)
                                            .filter((id): id is number => typeof id === 'number');

                                        setSelectedUserIds(selectedIds);
                                    }}
                                    value={
                                        groups
                                            .find((group) => group.id === selectedGroup)
                                            ?.users?.filter((user) => selectedUserIds.includes(user.id!))
                                            .map((user) => ({
                                                value: user.id,
                                                label: user.full_name || user.user_name,
                                            })) || []
                                    }
                                    isClearable
                                    placeholder="Chọn..."
                                />
                            </div>
                        )}

                        {editingFolder?.parent_id && (
                            <div className="mb-1">
                                <small className="text-muted">Folder này sẽ được tạo trong folder cha</small>
                            </div>
                        )}
                    </div>
                </div>
            </div>

            <div className="modal-footer">
                <button type="button" className="btn btn-outline-secondary" onClick={handleClose} disabled={isLoading}>
                    Hủy
                </button>
                <UpdateButton btnText={isEditing ? 'Cập nhật' : 'Tạo mới'} isLoading={isLoading} hasDivWrap={false} />
            </div>
        </form>
    );

    return (
        <ModalContent
            show={show}
            changeShow={handleClose}
            title={isEditing ? 'Sửa folder' : 'Thêm mới folder'}
            content={modalContent}
            modalSize="md"
        />
    );
};

export default ModalFolderForm;
