import { useEffect, useRef } from 'react';
import { Edit2, FolderPlus, Move, Trash2 } from 'react-feather';
import FolderType from '../../../types/Folder';

interface FolderActionMenuProps {
    folder: FolderType;
    onClose: () => void;
    onAddFolder: (parentId?: number) => void;
    onEditFolder: (folder: { id: number; name: string; parent_id?: number }) => void;
    onDeleteFolder: (folder: FolderType) => void;
    onMoveFolder: (folder: FolderType) => void;
    isDeletingFolder: boolean;
}

const FolderActionMenu = ({
    folder,
    onClose,
    onAddFolder,
    onEditFolder,
    onDeleteFolder,
    onMoveFolder,
    isDeletingFolder,
}: Readonly<FolderActionMenuProps>) => {
    const menuRef = useRef<HTMLDivElement>(null);

    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
                onClose();
            }
        };

        document.addEventListener('mousedown', handleClickOutside);
        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, [onClose]);

    const handleAddSubFolder = () => {
        onAddFolder(folder.id);
        onClose();
    };

    const handleEditFolder = () => {
        onEditFolder({
            id: folder.id!,
            name: folder.name,
            parent_id: folder.parent_id,
        });
        onClose();
    };

    // const handleDeleteFolder = () => {
    //     onDeleteFolder(folder);
    //     onClose();
    // };

    const handleMoveFolder = () => {
        onMoveFolder(folder);
        onClose();
    };

    return (
        <div ref={menuRef} className="dropdown-menu dropdown-menu-end show" style={{ minWidth: '180px' }}>
            <button
                type="button"
                className="dropdown-item d-flex align-items-center w-100 border-0 bg-transparent"
                onClick={handleAddSubFolder}
                style={{
                    padding: '8px 16px',
                    fontSize: '14px',
                    transition: 'background-color 0.15s ease-in-out',
                }}
                onMouseEnter={(e) => {
                    e.currentTarget.style.backgroundColor = '#f8f9fa';
                }}
                onMouseLeave={(e) => {
                    e.currentTarget.style.backgroundColor = 'transparent';
                }}
            >
                <FolderPlus size={14} className="me-2" />
                Thêm folder con
            </button>
            <button
                type="button"
                className="dropdown-item d-flex align-items-center w-100 border-0 bg-transparent"
                onClick={handleEditFolder}
                style={{
                    padding: '8px 16px',
                    fontSize: '14px',
                    transition: 'background-color 0.15s ease-in-out',
                }}
                onMouseEnter={(e) => {
                    e.currentTarget.style.backgroundColor = '#f8f9fa';
                }}
                onMouseLeave={(e) => {
                    e.currentTarget.style.backgroundColor = 'transparent';
                }}
            >
                <Edit2 size={14} className="me-2" />
                Sửa
            </button>
            <button
                type="button"
                className="dropdown-item d-flex align-items-center w-100 border-0 bg-transparent"
                onClick={handleMoveFolder}
                style={{
                    padding: '8px 16px',
                    fontSize: '14px',
                    transition: 'background-color 0.15s ease-in-out',
                }}
                onMouseEnter={(e) => {
                    e.currentTarget.style.backgroundColor = '#f8f9fa';
                }}
                onMouseLeave={(e) => {
                    e.currentTarget.style.backgroundColor = 'transparent';
                }}
            >
                <Move size={14} className="me-2" />
                Di chuyển
            </button>
            {/* <div className="dropdown-divider" style={{ margin: '4px 0' }}></div> */}
            {/* <button
                type="button"
                className="dropdown-item d-flex align-items-center w-100 border-0 bg-transparent text-danger"
                onClick={handleDeleteFolder}
                disabled={isDeletingFolder}
                style={{
                    padding: '8px 16px',
                    fontSize: '14px',
                    transition: 'background-color 0.15s ease-in-out',
                    opacity: isDeletingFolder ? 0.6 : 1,
                }}
                onMouseEnter={(e) => {
                    if (!isDeletingFolder) {
                        e.currentTarget.style.backgroundColor = '#f8d7da';
                    }
                }}
                onMouseLeave={(e) => {
                    if (!isDeletingFolder) {
                        e.currentTarget.style.backgroundColor = 'transparent';
                    }
                }}
            >
                <Trash2 size={14} className="me-2" />
                {isDeletingFolder ? 'Đang xóa...' : 'Xóa'}
            </button> */}
        </div>
    );
};

export default FolderActionMenu;
