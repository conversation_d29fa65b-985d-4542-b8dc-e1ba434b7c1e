import React, { useState, useEffect } from 'react';
import ModalContent from '../../../components/partials/ModalContent';
import FolderType, { FolderDetailRes } from '../../../types/Folder';
import FolderTree from './FolderTree';
import { axiosGraphQLClient } from '../../../services/AxiosGraphQLClient';
import { FOLDERS_DETAIL } from '../../../services/FolderService';
import { filterFoldersForMove } from '../utils/folderUtils';
import { useAuthStore } from '../../../stores/authStore';
import { FolderFilterType } from '../../../types/common';

interface ModalMoveFolderProps {
    show: boolean;
    onClose: () => void;
    movingFolder: FolderType | null;
    allFolders: FolderType[];
    filterType: FolderFilterType;
    onMoveFolder: (targetFolderId: number | null) => void;
    isLoading: boolean;
}

const ModalMoveFolder: React.FC<ModalMoveFolderProps> = ({
    show,
    onClose,
    movingFolder,
    allFolders,
    filterType,
    onMoveFolder,
    isLoading,
}) => {
    const [selectedFolderId, setSelectedFolderId] = useState<number | null>(null);
    const [filteredFolders, setFilteredFolders] = useState<FolderType[]>([]);
    const [expandedFolders, setExpandedFolders] = useState<Set<number>>(new Set());
    const [folderChildren, setFolderChildren] = useState<Record<number, FolderType[]>>({});
    const currentUser = useAuthStore((state) => state.user);

    useEffect(() => {
        if (movingFolder?.id && allFolders.length > 0) {
            let filteredByType = allFolders;

            // Filter by type first
            if (filterType === FolderFilterType.PERSONAL) {
                filteredByType = allFolders.filter((folder) => folder.created_by === currentUser?.id);
            }

            // Lọc folder để loại trừ folder hiện tại và children của nó
            const filtered = filterFoldersForMove(filteredByType, movingFolder.id, folderChildren);
            // Chỉ lấy root folders (không có parent_id)
            const rootFolders = filtered.filter((folder) => !folder.parent_id);
            setFilteredFolders(rootFolders);
        }
    }, [movingFolder, allFolders, folderChildren, filterType, currentUser?.id]);

    const loadFolderChildren = async (folderId: number) => {
        try {
            const result = await axiosGraphQLClient.request<FolderDetailRes>(
                FOLDERS_DETAIL,
                { id: folderId },
                'Folders_detail'
            );

            if (result.folders_detail?.children) {
                let children = result.folders_detail.children;

                // Filter by type first
                if (filterType === FolderFilterType.PERSONAL) {
                    children = children.filter((child) => child.created_by === currentUser?.id);
                }

                // Lọc children để loại trừ folder đang di chuyển và children của nó
                const filteredChildren = movingFolder?.id
                    ? filterFoldersForMove(children, movingFolder.id, {})
                    : children;

                setFolderChildren((prev) => ({
                    ...prev,
                    [folderId]: filteredChildren,
                }));
            }
            //tslint:disable-next-line: no-any
        } catch (error: any) {
            throw new Error(`Lỗi khi tải folder children: ${error.message}`);
        }
    };

    const handleToggleExpand = async (folderId: number) => {
        const newExpandedFolders = new Set(expandedFolders);

        if (expandedFolders.has(folderId)) {
            newExpandedFolders.delete(folderId);
        } else {
            newExpandedFolders.add(folderId);

            if (!folderChildren[folderId]) {
                await loadFolderChildren(folderId);
            }
        }

        setExpandedFolders(newExpandedFolders);
    };

    const handleFolderSelect = (folderId: number) => {
        setSelectedFolderId(folderId);
    };

    const handleMoveFolder = () => {
        onMoveFolder(selectedFolderId);
        handleClose();
    };

    const handleClose = () => {
        setSelectedFolderId(null);
        setExpandedFolders(new Set());
        setFolderChildren({});
        onClose();
    };

    const modalContent = (
        <div>
            <div className="modal-body">
                <div className="mb-3">
                    <p className="text-muted mb-2">Chọn folder đích để di chuyển folder "{movingFolder?.name}"</p>
                </div>
                <div className="border rounded p-3" style={{ maxHeight: '400px', overflow: 'auto' }}>
                    {/* Option để chọn thư mục gốc */}
                    <div
                        className={`folder-row d-flex align-items-center py-2 px-2 cursor-pointer position-relative ${
                            selectedFolderId === null ? 'bg-light border-start border-primary border-3' : ''
                        }`}
                        style={{
                            backgroundColor: selectedFolderId === null ? '#f8f9fa' : 'transparent',
                        }}
                        onClick={() => setSelectedFolderId(null)}
                        onMouseEnter={(e) => {
                            if (selectedFolderId !== null) {
                                e.currentTarget.style.backgroundColor = '#f8f9fa';
                            }
                        }}
                        onMouseLeave={(e) => {
                            if (selectedFolderId !== null) {
                                e.currentTarget.style.backgroundColor = 'transparent';
                            }
                        }}
                    >
                        <div style={{ width: 16 }} className="me-1" />
                        <i className="fas fa-home text-muted me-2" style={{ fontSize: 16 }} />
                        <span className="folder-name flex-grow-1">Thư mục gốc</span>
                    </div>
                    <FolderTree
                        folders={filteredFolders}
                        selectedFolderId={selectedFolderId}
                        onFolderSelect={handleFolderSelect}
                        onAddFolder={() => {}}
                        onEditFolder={() => {}}
                        onDeleteFolder={() => {}}
                        onMoveFolder={() => {}}
                        isDeletingFolder={false}
                        isLoading={false}
                        expandedFolders={expandedFolders}
                        folderChildren={folderChildren}
                        onToggleExpand={handleToggleExpand}
                        showActions={false}
                    />
                </div>
            </div>

            <div className="modal-footer">
                <button type="button" className="btn btn-outline-secondary" onClick={handleClose} disabled={isLoading}>
                    Hủy
                </button>
                <button type="button" className="btn btn-primary" onClick={handleMoveFolder} disabled={isLoading}>
                    {isLoading ? (
                        <>
                            <span
                                className="spinner-border spinner-border-sm me-1"
                                role="status"
                                aria-hidden="true"
                            ></span>
                            Đang di chuyển...
                        </>
                    ) : (
                        'Di chuyển'
                    )}
                </button>
            </div>
        </div>
    );

    return (
        <ModalContent
            show={show}
            changeShow={handleClose}
            title="Di chuyển folder"
            content={modalContent}
            modalSize="md"
        />
    );
};

export default ModalMoveFolder;
