import { Trash2 } from 'react-feather';
import AdvertiseItem, { AdvertiseItemType, AdvertiseItemTypeNames } from 'types/AdvertiseItem';
import { getFieldHtml } from 'utils/common';
import { ItemStatusNames } from '../../../types/common/Item';
import { useTranslation } from 'react-i18next';
import { genTableIndex } from '../../../utils/common';
import { Paging } from '../../../types/common';

interface IProps {
    items: AdvertiseItem[];
    paging: Paging;
    handleEdit: (id: number) => void;
    handleDelete: (id: number) => void;
}

export default function ListAdvertise({ items, paging, handleEdit, handleDelete }: Readonly<IProps>) {
    const { t } = useTranslation();

    return (
        <div className="table-responsive">
            <table className="table">
                <thead>
                    <tr>
                        <th className="text-center">STT</th>
                        <th>Ti<PERSON><PERSON> đ<PERSON></th>
                        <th className="text-center">Ảnh</th>
                        <th className="text-center">Loại</th>
                        <th className="text-center"><PERSON><PERSON>ch thước</th>
                        <th className="text-center">Lượt xem</th>
                        <th className="text-center">Trạng thái</th>
                        <th className="thAction1"></th>
                    </tr>
                </thead>
                <tbody>
                    {items.map((item: AdvertiseItem, index: number) => (
                        <tr key={item.id}>
                            <td className="text-center">{genTableIndex(index, paging.limit, paging.current_page)}</td>
                            <td>
                                <span className="text-primary cursor-pointer" onClick={() => handleEdit(item.id!)}>
                                    {item.name}
                                </span>
                            </td>
                            <td className="text-center">
                                {item.file && (
                                    <img src={item.file?.file_url} alt="" className="h100px mx-auto object-contain" />
                                )}
                            </td>
                            <td className="text-center">{getFieldHtml(AdvertiseItemTypeNames, item.type_id, t)}</td>
                            {item.type_id === AdvertiseItemType.IMAGE ? (
                                <td className="text-center">
                                    {item.width}px x {item.height}px
                                </td>
                            ) : (
                                <td></td>
                            )}
                            <td className="text-center">0</td>
                            <td className="text-center">
                                {getFieldHtml(ItemStatusNames, item.status_id as number, t)}
                            </td>
                            <td className="text-center">
                                <button
                                    type="button"
                                    title="Xoá"
                                    className="btn btn-icon btn-sm btn-flat-danger waves-effect"
                                    onClick={() => handleDelete(item.id!)}
                                >
                                    <Trash2 size={14} />
                                </button>
                            </td>
                        </tr>
                    ))}
                </tbody>
            </table>
        </div>
    );
}
