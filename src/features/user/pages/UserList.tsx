import { keepPreviousData } from '@tanstack/react-query';
import ContentHeader from 'components/partials/ContentHeader';
import ModalConfirm from 'components/partials/ModalConfirm';
import PaginationTable from 'components/partials/PaginationTable';
import Spinner from 'components/partials/Spinner';
import { LIMIT_MAX, PAGE_NUMBER_DEFAULT, PAGINATION, QUERY_KEY } from 'constants/common';
import useQueryParams from 'hooks/useQueryParams';
import isUndefined from 'lodash/isUndefined';
import omitBy from 'lodash/omitBy';
import { useMemo, useState } from 'react';
import { Helmet } from 'react-helmet-async';
import { useTranslation } from 'react-i18next';
import { useNavigate, useParams } from 'react-router-dom';
import { useAuthStore } from 'stores/authStore';
import User, { SearchUserParam, userFilterConfig, UserListQuery, UserRoleNames } from 'types/User';
import Group, { groupFilterConfig, GroupQuery, GroupType, SearchGroup } from 'types/Group';
import {
    convertConstantToSelectOptions,
    convertDataToSelectOptions,
    convertObjectToSelectOptions,
    convertPaging,
    generateFilters,
    getFieldInArrayObject,
    showToast,
} from 'utils/common';
import ListUser from '../components/ListUser';
import { useGraphQLQuery } from '../../../hooks/useGraphQLQuery';
import { USER_DELETE, USER_LIST } from '../../../services/UserService';
import { useGraphQLMutation } from '../../../hooks/useGraphQLMutation';
import SearchForm from '../../../components/partials/SearchForm';
import { ItemParam, ItemStatus, ItemStatusNames, SelectOption } from '../../../types/common/Item';
import { BaseSearch } from '../../../types/common';
import { GROUP_LIST } from 'services/GroupService';
import { useAppStore } from '../../../stores/appStore';

export default function UserList() {
    const user = useAuthStore((state) => state.user);
    const departmentId = useAppStore((state) => state.departmentId);
    const { type } = useParams();
    const [showDelete, setShowDelete] = useState(false);
    const [itemId, setItemId] = useState(0);
    const navigate = useNavigate();
    const { t } = useTranslation();
    const roleId = useMemo(() => +getFieldInArrayObject(UserRoleNames, type ?? '', 'id', '', 'name'), [type]);
    if (!roleId) {
        navigate('/not-found');
    }

    const { queryParams, setQueryParams } = useQueryParams<SearchUserParam>();
    const paramConfig: SearchUserParam = omitBy(
        {
            limit: queryParams.limit ?? PAGINATION.limit,
            page: queryParams.page ?? '1',
            search: queryParams.search,
            group_id: queryParams.group_id,
            status_id: queryParams.status_id,
            role_id: roleId.toString(),
            //department_id: departmentId.toString(),
        },
        isUndefined
    );

    const { search, limit, page, ...dataParamConfig } = paramConfig;
    const filters = generateFilters(dataParamConfig, userFilterConfig);

    const { data, isLoading, isRefetching, refetch } = useGraphQLQuery<UserListQuery, BaseSearch>(
        [QUERY_KEY.USERS, paramConfig, filters],
        USER_LIST,
        {
            page: Number(page),
            limit: Number(limit),
            search,
            filters: filters.length > 0 ? filters : undefined,
            // sorts: paramConfig.sort,
        },
        '',
        {
            enabled: !!roleId && !!user, // && !!departmentId
            placeholderData: keepPreviousData,
        }
    );

    const deleteMutation = useGraphQLMutation(USER_DELETE, '', {
        onSuccess: () => {
            showToast(true, [t('success.delete')]);
            setShowDelete(false);
            setItemId(0);
            refetch();
        },
        onError: () => showToast(false, [t('error.common')]),
    });

    const handlePageChange = (_event: React.ChangeEvent<unknown>, page: number) => {
        setQueryParams({ ...queryParams, page: page.toString() });
    };

    const handleDelete = (id: number) => {
        deleteMutation.reset();
        setItemId(id);
        setShowDelete(true);
    };

    const deleteItem = async () => {
        if (itemId) {
            deleteMutation.mutate({ id: itemId });
        }
    };

    const paramConfigGroup: SearchGroup = {
        department_id: departmentId.toString(),
        type_id: GroupType.DEPARTMENT.toString(),
        status_id: ItemStatus.ACTIVE.toString(),
    };
    const filtersGroup = generateFilters(paramConfigGroup, groupFilterConfig);
    const { data: groupData } = useGraphQLQuery<GroupQuery, SearchGroup>(
        [QUERY_KEY.GROUPS, paramConfigGroup, filtersGroup],
        GROUP_LIST,
        {
            limit: LIMIT_MAX,
            page: PAGE_NUMBER_DEFAULT,
            filters: filtersGroup.length > 0 ? filtersGroup : undefined,
        },
        '',
        {
            enabled: !!paramConfigGroup.department_id,
            placeholderData: keepPreviousData,
        }
    );

    return (
        <>
            <Helmet>
                <title>{t(`${type}.multiple`)}</title>
            </Helmet>
            <ContentHeader
                title={t(`${type}.multiple`)}
                contextMenu={[
                    {
                        text: t(`${type}.add`),
                        to: `/user/add/${type}`,
                        icon: 'PLUS',
                    },
                ]}
            />
            <div className="content-body">
                <div className="col-12">
                    <SearchForm
                        fields={[
                            { name: 'search', type: 'text', label: 'Từ khóa', wrapClassName: 'col-md-4 col-12' },
                            {
                                name: 'group_id',
                                type: 'select',
                                label: 'Phòng ban',
                                wrapClassName: 'col-md-4 col-12',
                                options: {
                                    multiple: true,
                                    choices: convertDataToSelectOptions(
                                        groupData?.groups_list.data ?? [],
                                        'id',
                                        'name',
                                        undefined,
                                        null
                                    ),
                                },
                            },
                            {
                                name: 'status_id',
                                type: 'select',
                                label: 'Trạng thái',
                                wrapClassName: 'col-md-4 col-12',
                                options: {
                                    multiple: true,
                                    choices: convertConstantToSelectOptions(ItemStatusNames, t, true),
                                },
                            },
                        ]}
                        isLoading={isLoading || isRefetching}
                        searchClass="col-md-4 pt-2"
                    />
                    {(isLoading || isRefetching) && <Spinner />}
                    {!isLoading && !isRefetching && (
                        <div className="card">
                            <ListUser
                                items={data?.users_list.data ?? []}
                                paging={convertPaging<User, SearchUserParam>(
                                    data?.users_list,
                                    Number(paramConfig.limit)
                                )}
                                handleDelete={handleDelete}
                                currenUser={user}
                            />
                            <PaginationTable
                                countItem={data?.users_list.totalCount}
                                totalPage={data?.users_list.totalPages}
                                currentPage={data?.users_list.currentPage}
                                handlePageChange={handlePageChange}
                            />
                        </div>
                    )}
                    <ModalConfirm
                        show={showDelete}
                        text={t('confirm.delete')}
                        btnDisabled={deleteMutation.isPending}
                        changeShow={(s: boolean) => setShowDelete(s)}
                        submitAction={deleteItem}
                    />
                </div>
            </div>
        </>
    );
}
