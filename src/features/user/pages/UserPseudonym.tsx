import { keepPreviousData } from '@tanstack/react-query';
import ContentHeader from 'components/partials/ContentHeader';
import ModalConfirm from 'components/partials/ModalConfirm';
import PaginationTable from 'components/partials/PaginationTable';
import Spinner from 'components/partials/Spinner';
import { PAGINATION, QUERY_KEY } from 'constants/common';
import useQueryParams from 'hooks/useQueryParams';
import isUndefined from 'lodash/isUndefined';
import omitBy from 'lodash/omitBy';
import { useState } from 'react';
import { Helmet } from 'react-helmet-async';
import { useTranslation } from 'react-i18next';
import { useNavigate, useParams } from 'react-router-dom';
import { convertConstantToSelectOptions, convertPaging, generateFilters, showToast } from 'utils/common';
import { useGraphQLQuery } from '../../../hooks/useGraphQLQuery';
import { useGraphQLMutation } from '../../../hooks/useGraphQLMutation';
import SearchForm from '../../../components/partials/SearchForm';
import { ItemStatus, ItemStatusNames } from '../../../types/common/Item';
import { BaseSearch } from '../../../types/common';
import Pseudonym, { pseudonymFilterConfig, PseudonymQuery, SearchPseudonymParam } from '../../../types/Pseudonym';
import {
    PSEUDONYM_CREATE,
    PSEUDONYM_DELETE,
    PSEUDONYM_LIST,
    PSEUDONYM_UPDATE,
} from '../../../services/PseudonymService';
import ListPseudonym from '../components/ListPseudonym';
import ModalPseudonymUpdate from '../components/ModalPseudonymUpdate';
import { USER_DETAIL } from '../../../services/UserService';
import User from 'types/User';
import { find } from 'lodash';

export default function UserPseudonym() {
    const { userId } = useParams();
    //const navigate = useNavigate();
    const { t } = useTranslation();

    // if (!userId) {
    //     navigate('/not-found');
    // }

    const { queryParams, setQueryParams } = useQueryParams<SearchPseudonymParam>();
    const paramConfig: SearchPseudonymParam = omitBy(
        {
            limit: queryParams.limit ?? PAGINATION.limit,
            page: queryParams.page ?? '1',
            search: queryParams.search,
            status_id: queryParams.status_id,
            user_id: userId,
        },
        isUndefined
    );

    const { search, limit, page, ...dataParamConfig } = paramConfig;
    const filters = generateFilters(dataParamConfig, pseudonymFilterConfig);

    const [showDelete, setShowDelete] = useState(false);
    const [showUpdate, setShowUpdate] = useState(false);
    const [itemId, setItemId] = useState(0);

    const { data, isLoading, isRefetching, refetch } = useGraphQLQuery<PseudonymQuery, BaseSearch>(
        [QUERY_KEY.PSEUDONYMS, paramConfig, filters],
        PSEUDONYM_LIST,
        {
            page: Number(page),
            limit: Number(limit),
            search,
            filters: filters.length > 0 ? filters : undefined,
        },
        '',
        {
            enabled: !!userId,
            placeholderData: keepPreviousData,
        }
    );

    const pseudonyms = data?.pseudonyms_list.data ?? [];

    const { data: userData, isLoading: isLoadingUsers } = useGraphQLQuery<{ users_detail: User }, { id: number }>(
        [QUERY_KEY.USER, Number(userId)],
        USER_DETAIL,
        {
            id: Number(userId),
        },
        '',
        {
            enabled: !!userId,
        }
    );

    const saveMutation = useGraphQLMutation<{}, { id?: number; body: Partial<Pseudonym> }>(
        itemId > 0 ? PSEUDONYM_UPDATE : PSEUDONYM_CREATE,
        '',
        {
            onSuccess: () => {
                showToast(true, [t('success.update')]);
                setShowUpdate(false);
                setItemId(0);
                refetch();
            },
            onError: () => showToast(false, [t('error.common')]),
        }
    );

    const deleteMutation = useGraphQLMutation(PSEUDONYM_DELETE, '', {
        onSuccess: () => {
            showToast(true, [t('success.delete')]);
            setShowDelete(false);
            setItemId(0);
            refetch();
        },
        onError: () => showToast(false, [t('error.common')]),
    });

    const updateItem = async (body: Pseudonym) => {
        body.status_id = body.status_id ? ItemStatus.ACTIVE : ItemStatus.PENDING;
        body.user_id = Number(userId);
        delete body.id;
        if (itemId === 0) {
            saveMutation.mutate({ body });
        } else {
            saveMutation.mutate({ id: itemId, body });
        }
    };

    const deleteItem = async () => {
        if (itemId) {
            deleteMutation.mutate({ id: itemId });
        }
    };

    const handleEdit = (id: number) => {
        saveMutation.reset();
        setItemId(id);
        setShowUpdate(true);
    };

    const handleDelete = (id: number) => {
        deleteMutation.reset();
        setItemId(id);
        setShowDelete(true);
    };

    const handlePageChange = (_event: React.ChangeEvent<unknown>, page: number) => {
        setQueryParams({ ...queryParams, page: page.toString() });
    };

    const actionMenu = (_id: number) => {
        saveMutation.reset();
        setShowUpdate(true);
        setItemId(0);
    };

    return (
        <>
            <Helmet>
                <title>Quản lý bút danh</title>
            </Helmet>
            <ContentHeader
                title={`Quản lý bút danh | ${userData?.users_detail?.full_name}`}
                contextMenu={[
                    {
                        text: 'Thêm bút danh',
                        icon: 'PLUS',
                        fnCallBack: { actionMenu },
                    },
                ]}
            />
            <div className="content-body">
                <div className="col-12">
                    <SearchForm
                        fields={[
                            { name: 'search', type: 'text', label: 'Từ khóa', wrapClassName: 'col-md-4 col-12' },
                            {
                                name: 'status_id',
                                type: 'select',
                                label: 'Trạng thái',
                                wrapClassName: 'col-md-4 col-12',
                                options: {
                                    multiple: true,
                                    choices: convertConstantToSelectOptions(ItemStatusNames, t, true),
                                },
                            },
                        ]}
                        isLoading={isLoading || isRefetching}
                        searchClass="col-md-4 pt-2"
                    />
                    {(isLoading || isRefetching || isLoadingUsers) && <Spinner />}
                    {!isLoading && !isRefetching && !isLoadingUsers && userData && (
                        <>
                            <div className="card">
                                <ListPseudonym
                                    items={pseudonyms}
                                    paging={convertPaging<Pseudonym, SearchPseudonymParam>(
                                        data?.pseudonyms_list,
                                        Number(paramConfig.limit)
                                    )}
                                    handleDelete={handleDelete}
                                    handleEdit={handleEdit}
                                />
                                <PaginationTable
                                    countItem={data?.pseudonyms_list.totalCount}
                                    totalPage={data?.pseudonyms_list.totalPages}
                                    currentPage={data?.pseudonyms_list.currentPage}
                                    handlePageChange={handlePageChange}
                                />
                            </div>
                            <ModalPseudonymUpdate
                                show={showUpdate}
                                pseudonym={find(pseudonyms, { id: itemId })}
                                isLoading={saveMutation.isPending}
                                changeShow={(s: boolean) => setShowUpdate(s)}
                                submitAction={updateItem}
                            />
                        </>
                    )}
                    <ModalConfirm
                        show={showDelete}
                        text={t('confirm.delete')}
                        btnDisabled={deleteMutation.isPending}
                        changeShow={(s: boolean) => setShowDelete(s)}
                        submitAction={deleteItem}
                    />
                </div>
            </div>
        </>
    );
}
