import ContentHeader from 'components/partials/ContentHeader';
import Spinner from 'components/partials/Spinner';
import { QUERY_KEY } from 'constants/common';
import { Helmet } from 'react-helmet-async';
import { LoginDeviceQuery } from 'types/LoginDevice';
import ListDevice from '../components/ListDevice';
import { useGraphQLQuery } from '../../../hooks/useGraphQLQuery';
import { LOGIN_DEVICE_LIST } from '../../../services/LoginDeviceService';

export default function LoginDeviceList() {
    const { data, isLoading, isRefetching } = useGraphQLQuery<LoginDeviceQuery>(
        [QUERY_KEY.LOGIN_DEVICES],
        LOGIN_DEVICE_LIST
    );

    return (
        <>
            <Helmet>
                <title>Quản lý thiết bị đăng nhập</title>
            </Helmet>
            <ContentHeader title="Quản lý thiết bị đăng nhập" />
            <div className="content-body">
                <div className="col-12">
                    {(isLoading || isRefetching) && <Spinner />}
                    {!isLoading && !isRefetching && (
                        <div className="card">
                            <ListDevice items={data?.login_devices_listing ?? []} />
                        </div>
                    )}
                </div>
            </div>
        </>
    );
}
