import { Star, Trash2 } from 'react-feather';
import { useTranslation } from 'react-i18next';
import Pseudonym from 'types/Pseudonym';
import { Paging } from 'types/common';
import { ItemStatusNames } from 'types/common/Item';
import { genTableIndex, getFieldHtml } from 'utils/common';

interface IProps {
    items: Pseudonym[];
    paging: Paging;
    handleDelete: (id: number) => void;
    handleEdit: (id: number) => void;
}

export default function ListPseudonym({ items, paging, handleDelete, handleEdit }: Readonly<IProps>) {
    const { t } = useTranslation();

    return (
        <div className="table-responsive">
            <table className="table">
                <thead>
                    <tr>
                        <th className="text-center">STT</th>
                        <th>Bút danh</th>
                        <th className="text-center">Mặc định</th>
                        <th className="text-center">Trạng thái</th>
                        <th className="thAction1"></th>
                    </tr>
                </thead>
                <tbody>
                    {items.map((item, index) => (
                        <tr key={item.id}>
                            <td className="text-center">{genTableIndex(index, paging.limit, paging.current_page)}</td>
                            <td>
                                <span className="text-primary cursor-pointer" onClick={() => handleEdit(item.id!)}>
                                    {item.name}
                                </span>
                            </td>
                            <td className="text-center">
                                {item.is_default && <Star size={16} className="text-warning" />}
                            </td>
                            <td className="text-center">
                                {getFieldHtml(ItemStatusNames, item.status_id as number, t)}
                            </td>
                            <td className="text-center">
                                {!item.is_default && (
                                    <button
                                        type="button"
                                        title="Xoá"
                                        className="btn btn-icon btn-sm btn-flat-danger waves-effect"
                                        onClick={() => handleDelete(item.id!)}
                                    >
                                        <Trash2 size={14} />
                                    </button>
                                )}
                            </td>
                        </tr>
                    ))}
                </tbody>
            </table>
        </div>
    );
}
