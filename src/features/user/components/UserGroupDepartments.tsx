import { includes } from 'lodash';
import { useEffect, useState } from 'react';
import { Disc } from 'react-feather';
import User from '../../../types/User';
import Group from 'types/Group';
import { useGraphQLMutation } from '../../../hooks/useGraphQLMutation';
import { USERS_CHANGE_GROUPS } from '../../../services/UserService';
import { showToast } from '../../../utils/common';
import { useTranslation } from 'react-i18next';

interface IProps {
    user: User;
    type: string;
    groups: Group[];
}

export default function UserGroupDepartments({ user, type, groups }: Readonly<IProps>) {
    const { t } = useTranslation();
    const [groupIds, setGroupIds] = useState<number[]>([]);

    useEffect(() => {
        setGroupIds((user.groups ?? []).map((item) => item.id!));
    }, [user]);

    const toggleItem = (itemId: number, checked: boolean) => {
        const items = [...groupIds];
        if (checked) {
            if (!includes(items, itemId)) {
                items.push(itemId);
                setGroupIds(items);
            }
        } else {
            const index = items.indexOf(itemId);
            if (index > -1) {
                items.splice(index, 1);
                setGroupIds(items);
            }
        }
    };

    const mutation = useGraphQLMutation<
        {},
        {
            id: number;
            body: { group_ids: number[] };
        }
    >(USERS_CHANGE_GROUPS, '', {
        onSuccess: () => showToast(true, [t('success.update')]),
        onError: () => showToast(false, [t('error.common')]),
    });

    const grantDepartments = () => mutation.mutate({ id: user.id!, body: { group_ids: groupIds } });

    return (
        <div className="card">
            <div className="card-header">
                <h4 className="card-title">{t(`${type}.single`)}</h4>
            </div>
            <div className="card-body">
                <div className="table-responsive">
                    <table className="table">
                        <thead>
                            <tr>
                                <th className="thAction1">STT</th>
                                <th className="thAction1">Chọn</th>
                                <th>{t(`${type}.single`)}</th>
                            </tr>
                        </thead>
                        <tbody>
                            {groups.map((item, index) => (
                                <tr key={item.id}>
                                    <td className="text-center">{index + 1}</td>
                                    <td className="text-center">
                                        <input
                                            className="form-check-input"
                                            type="checkbox"
                                            id={`cb_${item.id}`}
                                            value={item.id}
                                            checked={includes(groupIds, item.id)}
                                            onChange={(e) => {
                                                toggleItem(item.id!, e.target.checked);
                                            }}
                                        />
                                    </td>
                                    <td>
                                        <label className="w-100" htmlFor={`cb_${item.id}`}>
                                            {item.parent_id && <Disc size={14} />} {item.name}
                                        </label>
                                    </td>
                                </tr>
                            ))}
                        </tbody>
                    </table>
                </div>
            </div>
            <div className="card-footer modal-footer">
                <button
                    className="btn btn-primary waves-effect waves-float waves-light"
                    //disabled={isEqual(groupIds.sort(), (user.groups ?? []).map((item) => item.id!).sort())}
                    onClick={grantDepartments}
                >
                    Cập nhật
                </button>
            </div>
        </div>
    );
}
