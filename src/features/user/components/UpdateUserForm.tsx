import { yupResolver } from '@hookform/resolvers/yup';
import classNames from 'classnames';
import UpdateButton from 'components/partials/UpdateButton';
import UploadImage from 'components/partials/UploadImage';
import { useEffect, useState } from 'react';
import { Eye, EyeOff, RefreshCw } from 'react-feather';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import FileService from 'services/FileService';
import User, { AuthUpdateRes, Gender, GenderNames, UserCreate, UserRole, UserUpdate } from 'types/User';
import { ItemStatus } from 'types/common/Item';
import { isValidImageFile, selectItem, showToast } from 'utils/common';
import { formatInputDateTime } from 'utils/date';
import * as yup from 'yup';
import { useGraphQLMutation } from '../../../hooks/useGraphQLMutation';
import { AUTH_UPDATE, generateSecurePassword, USER_CREATE, USER_UPDATE } from '../../../services/UserService';
import omitBy from 'lodash/omitBy';
import omit from 'lodash/omit';
import isNull from 'lodash/isNull';
import InputSwitch from '../../../components/partials/InputSwitch';
import { DEFAULT_IMAGE } from '../../../constants/common';
import { isEmpty } from 'lodash';
import { useAppStore } from '../../../stores/appStore';

interface IProps {
    id: number;
    roleId: UserRole | number;
    user?: User;
    isProfile?: boolean;
    type?: string;
}

export default function UpdateUserForm({ id, roleId, user, isProfile = false, type }: Readonly<IProps>) {
    const [showPass, setShowPass] = useState(false);
    const [showRePass, setShowRePass] = useState(false);
    const [avatar, setAvatar] = useState(user?.avatar?.file_url ?? DEFAULT_IMAGE);
    const [fileAvatar, setFileAvatar] = useState<File>();
    const departmentId = useAppStore((state) => state.departmentId);
    const navigate = useNavigate();
    const { t } = useTranslation();

    const yupObj = {
        user_name: yup
            .string()
            .required(t('error.required'))
            .trim()
            .matches(/^(?![_-])[a-z0-9_-]+(?<![_-])$/, 'Chỉ cho phép nhập chữ thường và số'),
        full_name: yup.string().required(t('error.required')).trim(),
        gender_id: yup.number().min(1, t('error.required')),
        email: yup
            .string()
            .nullable()
            .test('email-validation', t('error.email'), function (value) {
                if (!value || value.length === 0) return true;
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                return emailRegex.test(value);
            }),
    };

    if (id === 0) {
        Object.assign(yupObj, {
            password: yup
                .string()
                .required(t('error.required'))
                .trim()
                .min(12, t('error.passwordLength'))
                .matches(/[A-Z]/, t('error.passwordUpperCase'))
                .matches(/[a-z]/, t('error.passwordLowerCase'))
                .matches(/[0-9]/, t('error.passwordNumber'))
                .matches(/[^A-Za-z0-9]/, t('error.passwordSpecial')),
            confirm_password: yup
                .string()
                .required(t('error.required'))
                .trim()
                .oneOf([yup.ref('password'), null], t('error.passwordNotSame')),
        });
    }

    const schema = yup.object(yupObj).required();
    const {
        register,
        handleSubmit,
        setValue,
        watch,
        reset,
        formState: { isSubmitting, errors },
    } = useForm<User>({
        resolver: yupResolver(schema),
    });

    useEffect(() => {
        if (roleId > 0) {
            if (id > 0 && user) {
                reset({
                    ...user,
                    status_id: user.status_id === ItemStatus.ACTIVE,
                    gender_id: user.gender_id ?? Gender.OTHER,
                    birthday: user.birthday ? formatInputDateTime(new Date(Number(user.birthday)).toISOString()) : '',
                });
                setAvatar(user?.avatar?.file_url ?? DEFAULT_IMAGE);
            } else {
                setValue('status_id', true);
                setValue('gender_id', Gender.OTHER);
            }
            setFileAvatar(undefined);
        }
    }, [id, user, roleId, reset, setValue]);

    const userCreateMutation = useGraphQLMutation<UserCreate, { body: Partial<User> }>(USER_CREATE, '', {
        onSuccess() {
            showToast(true, [t('success.update')]);
            setTimeout(() => {
                navigate(`/user/list/${type}`);
            }, 2000);
        },
        onError: () => showToast(false, [t('error.common')]),
    });

    const userUpdateMutation = useGraphQLMutation<UserUpdate, { id: number; body: Partial<User> }>(USER_UPDATE, '', {
        onSuccess() {
            showToast(true, [t('success.update')]);
            setTimeout(() => {
                navigate(`/user/list/${type}`);
            }, 2000);
        },
        onError: () => showToast(false, [t('error.common')]),
    });

    const authUpdateMutation = useGraphQLMutation<AuthUpdateRes, { body: Partial<User> }>(AUTH_UPDATE, '', {
        onSuccess() {
            showToast(true, [t('success.update')]);
            setTimeout(() => {
                navigate(`/user/profile`);
            }, 2000);
        },
        onError: () => showToast(false, [t('error.common')]),
    });

    const onChangeFile = (file: File) => {
        if (!isValidImageFile(file)) {
            showToast(false, [t('error.chooseImage')]);
            return;
        }
        setFileAvatar(file);
        setAvatar(URL.createObjectURL(file));
    };

    const handleUploadAvatar = async () => {
        let avatarId = user?.avatar_id ?? null;
        if (fileAvatar) {
            const results = await FileService.upload(fileAvatar, '', departmentId);
            if (results.upload) {
                avatarId = results.upload.id;
                setAvatar(results.upload.url);
            } else {
                showToast(false, [t('success.update')]);
            }
            setFileAvatar(undefined);
        } else if (avatar === DEFAULT_IMAGE) {
            avatarId = null;
        }
        return avatarId;
    };

    const onSubmit = async (formData: User) => {
        delete formData.id;
        const data = omit(formData, [
            'created_at',
            'created_by',
            'updated_at',
            'updated_by',
            'email_verified',
            'phone_verified',
            'is_require_change_password',
            'login_at',
            'avatar',
            'groups',
            'userDepartments',
        ]);
        if (data.birthday) {
            data.birthday = new Date(data.birthday).toISOString();
        } else delete data.birthday;
        if (!data.telegram_notify) data.telegram_id = null;
        else if (isEmpty(data.telegram_id)) {
            showToast(false, ['Vui lòng nhập Telegram ID']);
            return;
        }
        data.avatar_id = await handleUploadAvatar();
        let dataUpdate = omitBy(data, isNull);
        dataUpdate.status_id = dataUpdate.status_id === true ? ItemStatus.ACTIVE : ItemStatus.PENDING;

        if (isProfile || id > 0) {
            dataUpdate = omit(dataUpdate, ['role_id', 'user_name']);
        }
        if (isProfile) {
            dataUpdate = omit(dataUpdate, ['status_id']);
            authUpdateMutation.mutate({ body: dataUpdate });
        } else if (id === 0) {
            dataUpdate.role_id = roleId;
            userCreateMutation.mutate({ body: dataUpdate });
        } else {
            userUpdateMutation.mutate({ id, body: dataUpdate });
        }
    };

    return (
        <div className="card">
            <div className="card-header border-bottom">
                <h4 className="card-title">Thông tin chính</h4>
            </div>
            <div className="card-body py-2 my-25">
                <form className="validate-form pt-50" onSubmit={handleSubmit(onSubmit)}>
                    <div className="row">
                        <div className="col-12 col-sm-6">
                            <UploadImage
                                id={1}
                                image={avatar}
                                label="Tải lên ảnh đại diện"
                                onChangeFile={onChangeFile}
                            />
                        </div>
                        <div className="col-12 col-sm-6"></div>
                        <div className="col-12 col-sm-4 mb-1">
                            <label className="form-label">
                                Tài khoản <span className="error">*</span>
                            </label>
                            <input
                                {...register('user_name')}
                                type="text"
                                className={classNames('form-control', {
                                    'is-invalid': Boolean(errors.user_name?.message),
                                })}
                                readOnly={id > 0}
                            />
                            <span className="error">{errors.user_name?.message}</span>
                        </div>
                        <div className="col-12 col-sm-4 mb-1">
                            <label className="form-label">
                                Họ tên <span className="error">*</span>
                            </label>
                            <input
                                {...register('full_name')}
                                type="text"
                                className={classNames('form-control', {
                                    'is-invalid': Boolean(errors.full_name?.message),
                                })}
                            />
                            <span className="error">{errors.full_name?.message}</span>
                        </div>
                        <div className="col-12 col-sm-4 mb-1">
                            <label className="form-label">Email</label>
                            <input
                                {...register('email')}
                                type="text"
                                className={classNames('form-control', {
                                    'is-invalid': Boolean(errors.email?.message),
                                })}
                            />
                            <span className="error">{errors.email?.message}</span>
                        </div>
                        <div className="col-12 col-sm-4 mb-1">
                            <label className="form-label">Số điện thoại</label>
                            <input
                                {...register('phone')}
                                type="text"
                                className={classNames('form-control', {
                                    'is-invalid': Boolean(errors.phone?.message),
                                })}
                            />
                            <span className="error">{errors.phone?.message}</span>
                        </div>
                        <div className="col-12 col-sm-4 mb-1">
                            <label className="form-label">Giới tính</label>
                            <select {...register('gender_id', { valueAsNumber: true })} className="form-select">
                                {selectItem(GenderNames, t, true)}
                            </select>
                        </div>
                        <div className="col-12 col-sm-4 mb-1">
                            <label className="form-label">Ngày sinh</label>
                            <input {...register('birthday')} type="date" className="form-control" />
                        </div>
                        <div className="col-12 col-sm-8 mb-1">
                            <label className="form-label">Địa chỉ</label>
                            <input {...register('address')} type="text" className="form-control" />
                        </div>
                        <div className="col-12 col-sm-4 mb-1">
                            <InputSwitch
                                classNameWrap="mt-50"
                                className="d-flex flex-column"
                                labelSwitchName="Hoạt động"
                                labelFieldName="Trạng thái"
                                name="status_id"
                                disabled={isProfile}
                                register={register}
                            />
                        </div>
                        <div className="col-12 col-sm-8 mb-1">
                            <label className="form-label">Nhận thông báo</label>
                            <div className="demo-inline-spacing">
                                <InputSwitch
                                    classNameWrap="mt-50"
                                    className="d-flex flex-column"
                                    labelSwitchName="Email"
                                    name="email_notify"
                                    register={register}
                                />
                                <InputSwitch
                                    classNameWrap="mt-50"
                                    className="d-flex flex-column"
                                    labelSwitchName="Telegram"
                                    name="telegram_notify"
                                    register={register}
                                />
                                <InputSwitch
                                    classNameWrap="mt-50"
                                    className="d-flex flex-column"
                                    labelSwitchName="Zalo"
                                    name="zalo_notify"
                                    register={register}
                                />
                                {watch('telegram_notify') && (
                                    <>
                                        <div className="mt-0">
                                            <label className="form-check-label">Telegram ID</label>
                                        </div>
                                        <div className="mt-0">
                                            <input {...register('telegram_id')} type="text" className="form-control" />
                                        </div>
                                    </>
                                )}
                            </div>
                        </div>
                        <div className="col-12 col-sm-4 mb-1">
                            <label className="form-label">Yêu cầu đổi mật khẩu sau 1 tháng</label>
                            <InputSwitch
                                classNameWrap="mt-50"
                                className="d-flex flex-column"
                                labelSwitchName="Hoạt động"
                                name="require_change_password"
                                register={register}
                            />
                        </div>
                        {id === 0 && (
                            <>
                                <div className="col-12 col-sm-4 mb-1">
                                    <label className="form-label">
                                        Mật khẩu <span className="error">*</span>
                                    </label>
                                    <RefreshCw
                                        size={14}
                                        className="text-primary cursor-pointer ms-50"
                                        onClick={() => {
                                            const password = generateSecurePassword();
                                            setValue('password', password);
                                            setValue('confirm_password', password);
                                        }}
                                    />
                                    <div className="input-group input-group-merge form-password-toggle">
                                        <input
                                            {...register('password')}
                                            className={classNames('form-control', 'form-control-merge', {
                                                error: Boolean(errors.password?.message),
                                            })}
                                            type={showPass ? 'text' : 'password'}
                                            placeholder="············"
                                        />
                                        <span
                                            className="input-group-text cursor-pointer"
                                            onClick={() => setShowPass((prevShowPass) => !prevShowPass)}
                                            style={{
                                                borderColor: errors.password?.message ? '#ea5455' : '#d8d6de',
                                            }}
                                        >
                                            {showPass ? <EyeOff size={14} /> : <Eye size={14} />}
                                        </span>
                                    </div>
                                    <span className="error">{errors.password?.message}</span>
                                </div>
                                <div className="col-12 col-sm-4 mb-1">
                                    <label className="form-label">
                                        Nhập lại mật khẩu <span className="error">*</span>
                                    </label>
                                    <div className="input-group input-group-merge form-password-toggle">
                                        <input
                                            {...register('confirm_password')}
                                            className={classNames('form-control', 'form-control-merge', {
                                                error: Boolean(errors.confirm_password?.message),
                                            })}
                                            type={showRePass ? 'text' : 'password'}
                                            placeholder="············"
                                        />
                                        <span
                                            className="input-group-text cursor-pointer"
                                            onClick={() => setShowRePass((prevShowRePass) => !prevShowRePass)}
                                            style={{
                                                borderColor: errors.password?.message ? '#ea5455' : '#d8d6de',
                                            }}
                                        >
                                            {showRePass ? <EyeOff size={14} /> : <Eye size={14} />}
                                        </span>
                                    </div>
                                    <span className="error">{errors.confirm_password?.message}</span>
                                </div>
                            </>
                        )}
                        <div className="col-12">
                            <UpdateButton btnText="Cập nhật" isLoading={isSubmitting} btnClass={['mt-1']} />
                        </div>
                    </div>
                </form>
            </div>
        </div>
    );
}
