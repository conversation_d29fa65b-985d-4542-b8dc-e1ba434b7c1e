import Notification, { NotificationsTypeNames } from 'types/Notification';
import { FORMAT_DATE, formatDateTime } from 'utils/date';
import { genTableIndex, getFieldHtml } from '../../../utils/common';
import { Paging } from '../../../types/common';
import { useTranslation } from 'react-i18next';

interface IProps {
    items: Notification[];
    paging: Paging;
}

export default function ListNotification({ items, paging }: Readonly<IProps>) {
    const { t } = useTranslation();
    return (
        <div className="table-responsive">
            <table className="table">
                <thead>
                    <tr>
                        <th className="text-center">STT</th>
                        <th>Tiêu để</th>
                        <th>Người gửi</th>
                        <th className="text-center">Đ<PERSON> đọc</th>
                        <th className="text-center">Thời gian</th>
                    </tr>
                </thead>
                <tbody>
                    {items.map((item: Notification, index: number) => (
                        <tr key={item.id}>
                            <td className="text-center">{genTableIndex(index, paging.limit, paging.current_page)}</td>
                            <td>{item.title} </td>
                            <td>{item.fromUser?.user_name}</td>
                            <td className="text-center">
                                {getFieldHtml(NotificationsTypeNames, item.is_read ? 1 : 0, t)}
                            </td>
                            <td className="text-center">
                                {item.created_at && formatDateTime(item.created_at, FORMAT_DATE.SHOW_ONLY_DATE)}
                            </td>
                        </tr>
                    ))}
                </tbody>
            </table>
        </div>
    );
}
