import { ArticleTypeNames } from '../../../types/common/Item';
import { useTranslation } from 'react-i18next';
import WorkflowPermission, { WorkflowPermissionArticleType } from '../../../types/WorkflowPermission';
import { useEffect, useState } from 'react';
import { find, findIndex, isEmpty } from 'lodash';
import { useGraphQLMutation } from '../../../hooks/useGraphQLMutation';
import {
    WORKFLOW_PERMISSION_ARTICLE_TYPE_CREATE,
    WORKFLOW_PERMISSION_ARTICLE_TYPE_UPDATE,
} from '../../../services/WorkflowPermissionArticleTypeService';
import { refreshPage, showToast } from '../../../utils/common';

interface IProps {
    userDepartmentId: number;
    workflowPermissions: WorkflowPermission[];
    data: WorkflowPermissionArticleType[];
}

export default function UserWorkflowPermissions({ userDepartmentId, workflowPermissions, data }: Readonly<IProps>) {
    const { t } = useTranslation();
    const [items, setItems] = useState<WorkflowPermissionArticleType[]>([]);

    useEffect(() => {
        setItems(data);
    }, [data]);

    const changeArticleType = (workflowPermissionId: number, articleTypeId: number, checked: boolean) => {
        if (checked) {
            const cloneItems = [...items];
            const index = findIndex(cloneItems, { article_type_id: articleTypeId });
            if (index > -1) {
                cloneItems[index].workflow_permission_id = workflowPermissionId;
            } else {
                cloneItems.push({
                    id: 0,
                    workflow_permission_id: workflowPermissionId,
                    article_type_id: articleTypeId,
                    user_department_id: userDepartmentId,
                });
            }
            setItems(cloneItems);
        }
    };

    const createMutation = useGraphQLMutation<{}, { body: Partial<WorkflowPermissionArticleType> }>(
        WORKFLOW_PERMISSION_ARTICLE_TYPE_CREATE
    );

    const updateMutation = useGraphQLMutation<{}, { id: number; body: Partial<WorkflowPermissionArticleType> }>(
        WORKFLOW_PERMISSION_ARTICLE_TYPE_UPDATE
    );

    const grantWorkflows = () => {
        items.forEach((item) => {
            const itemId = item.id ?? 0;
            delete item.id;
            if (itemId > 0) {
                updateMutation.mutate({ id: itemId, body: item });
            } else {
                createMutation.mutate({ body: item });
            }
        });
        showToast(true, [t('success.update')]);
        refreshPage();
    };

    return (
        <div className="card">
            <div className="card-header">
                <h4 className="card-title">Phân quyền toà soạn</h4>
            </div>
            <div className="card-body">
                <div className="table-responsive">
                    <table className="table">
                        <thead>
                            <tr>
                                <th className="thAction1">STT</th>
                                <th>Quyền toà soạn</th>
                                {ArticleTypeNames.map((item) => (
                                    <th key={item.id} className="text-center">
                                        {t(`constants.${item.name}`)}
                                    </th>
                                ))}
                            </tr>
                        </thead>
                        <tbody>
                            {workflowPermissions.map((item, index) => (
                                <tr key={item.id}>
                                    <td className="text-center">{index + 1}</td>
                                    <td>{item.name}</td>
                                    {ArticleTypeNames.map((articleType) => (
                                        <td key={articleType.id} className="text-center">
                                            <input
                                                className="form-check-input"
                                                type="radio"
                                                name={`cb_${articleType.id}`}
                                                value={articleType.id}
                                                checked={
                                                    !!find(items, {
                                                        workflow_permission_id: item.id,
                                                        article_type_id: articleType.id,
                                                    })
                                                }
                                                onChange={(e) =>
                                                    changeArticleType(item.id!, +e.target.value, e.target.checked)
                                                }
                                            />
                                        </td>
                                    ))}
                                </tr>
                            ))}
                        </tbody>
                    </table>
                </div>
            </div>
            <div className="card-footer modal-footer">
                <button
                    className="btn btn-primary waves-effect waves-float waves-light"
                    disabled={isEmpty(workflowPermissions)}
                    onClick={grantWorkflows}
                >
                    Cập nhật
                </button>
            </div>
        </div>
    );
}
