import { includes } from 'lodash';
import { Disc } from 'react-feather';
import { useTranslation } from 'react-i18next';
import Action from 'types/Action';
import { getFieldInArrayObject } from 'utils/common';

interface IProps {
    actions: Action[];
    actionIds: string[];
}

export default function UserActions({ actions, actionIds }: Readonly<IProps>) {
    const { t } = useTranslation();

    return (
        <div className="card">
            <div className="card-body">
                <div className="table-responsive">
                    <table className="table">
                        <thead>
                            <tr>
                                <th className="text-center thAction1">{t('no.')}</th>
                                <th className="text-center thAction1">{t('choose')}</th>
                                <th>{t('actionName')}</th>
                                <th>{t('parent')}</th>
                            </tr>
                        </thead>
                        <tbody>
                            {actions.map((item, index) => (
                                <tr key={item.id}>
                                    <td className="text-center">{index + 1}</td>
                                    <td className="text-center">
                                        <input
                                            className="form-check-input"
                                            type="checkbox"
                                            value={item.id}
                                            checked={includes(actionIds, String(item.id))}
                                            readOnly
                                        />
                                    </td>
                                    <td>
                                        {item.parent_id && <Disc size={14} />} {item.name}
                                    </td>
                                    <td>{item.parent_id && getFieldInArrayObject(actions, item.parent_id)}</td>
                                </tr>
                            ))}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    );
}
