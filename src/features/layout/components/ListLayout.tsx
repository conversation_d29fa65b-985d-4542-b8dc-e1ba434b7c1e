import { useTranslation } from 'react-i18next';
import Layout, { LayoutTypeNames } from '../../../types/Layout';
import { genTableIndex, getFieldHtml } from '../../../utils/common';
import { ItemStatusNames } from '../../../types/common/Item';
import { Smartphone, Star, Trash2 } from 'react-feather';
import { Paging } from '../../../types/common';
import { Link } from 'react-router-dom';

interface IProps {
    items: Layout[];
    paging: Paging;
    handleDelete: (id: number) => void;
}

export default function ListLayout({ items, paging, handleDelete }: Readonly<IProps>) {
    const { t } = useTranslation();

    return (
        <div className="table-responsive">
            <table className="table">
                <thead>
                    <tr>
                        <th className="text-center">STT</th>
                        <th>Tên layout</th>
                        <th className="text-center">Loại layout</th>
                        <th className="text-center">Trạng thái</th>
                        <th className="thAction1"></th>
                    </tr>
                </thead>
                <tbody>
                    {items.map((item: Layout, index: number) => (
                        <tr key={item.id}>
                            <td className="text-center">{genTableIndex(index, paging.limit, paging.current_page)}</td>
                            <td>
                                <Link to={`/layout/edit/${item.id}`} className="text-primary">
                                    {item.name} {item.is_default && <Star size={14} className="text-warning" />}{' '}
                                    {item.is_mobile && <Smartphone size={14} className="text-info" />}
                                </Link>
                            </td>
                            <td className="text-center">{getFieldHtml(LayoutTypeNames, item.layout_type_id, t)}</td>
                            <td className="text-center">
                                {getFieldHtml(ItemStatusNames, item.status_id as number, t)}
                            </td>
                            <td className="text-center">
                                <button
                                    type="button"
                                    title="Xoá"
                                    className="btn btn-icon btn-sm btn-flat-danger waves-effect"
                                    onClick={() => handleDelete(item.id!)}
                                >
                                    <Trash2 size={14} />
                                </button>
                            </td>
                        </tr>
                    ))}
                </tbody>
            </table>
        </div>
    );
}
