import { yupResolver } from '@hookform/resolvers/yup';
import classNames from 'classnames';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import * as yup from 'yup';
import Layout, { LayoutType, LayoutTypeNames } from '../../../types/Layout';
import { selectItem, showToast } from '../../../utils/common';
import UpdateButton from '../../../components/partials/UpdateButton';
import InputSwitch from '../../../components/partials/InputSwitch';
import { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useGraphQLMutation } from '../../../hooks/useGraphQLMutation';
import { LAYOUT_CREATE, LAYOUT_UPDATE } from '../../../services/LayoutService';
import { ItemStatus } from '../../../types/common/Item';
import { useAppStore } from 'stores/appStore';

interface IProps {
    id: number;
    layout?: Layout;
}

export default function UpdateLayoutForm({ id, layout }: Readonly<IProps>) {
    const { t } = useTranslation();
    const navigate = useNavigate();
    const departmentId = useAppStore((state) => state.departmentId);

    const schema = yup
        .object({
            name: yup.string().required(t('error.required')).trim(),
            content: yup.string().required(t('error.required')).trim(),
        })
        .required();

    const {
        register,
        handleSubmit,
        reset,
        formState: { isSubmitting, errors },
    } = useForm<Layout>({
        resolver: yupResolver(schema),
    });

    useEffect(() => {
        if (layout) {
            reset({
                ...layout,
                status_id: layout.status_id === ItemStatus.ACTIVE,
            });
        } else {
            reset({
                name: '',
                desc: '',
                content: '',
                status_id: true,
                layout_type_id: LayoutType.DETAIL,
                is_default: false,
                is_mobile: false,
            });
        }
    }, [layout, reset]);

    const createMutation = useGraphQLMutation<{}, { body: Partial<Layout> }>(LAYOUT_CREATE, '', {
        onSuccess: () => {
            showToast(true, [t('success.update')]);
            navigate('/layout');
        },
        onError: () => {
            showToast(false, [t('error.common')]);
        },
    });

    const updateMutation = useGraphQLMutation<{}, { id: number; body: Partial<Layout> }>(LAYOUT_UPDATE, '', {
        onSuccess: () => {
            showToast(true, [t('success.update')]);
            navigate('/layout');
        },
        onError: () => {
            showToast(false, [t('error.common')]);
        },
    });

    const onSubmit = (data: Layout) => {
        const formData = {
            ...data,
            status_id: data.status_id === true ? ItemStatus.ACTIVE : ItemStatus.PENDING,
            department_id: departmentId,
        };
        if (id > 0) {
            delete formData.id;
            updateMutation.mutate({ id, body: formData });
        } else {
            createMutation.mutate({ body: formData });
        }
    };

    return (
        <div className="card">
            <div className="card-header">
                <h4 className="card-title">{id > 0 ? 'Cập nhật layout' : 'Thêm mới layout'}</h4>
            </div>
            <div className="card-body">
                <form className="validate-form" onSubmit={handleSubmit(onSubmit)}>
                    <div className="row">
                        <div className="col-12 col-md-4 mb-1">
                            <label className="form-label">
                                Tên layout <span className="error">*</span>
                            </label>
                            <input
                                {...register('name')}
                                type="text"
                                className={classNames('form-control', {
                                    'is-invalid': Boolean(errors.name?.message),
                                })}
                            />
                            <span className="error">{errors.name?.message}</span>
                        </div>
                        <div className="col-12 col-md-4 mb-1">
                            <label className="form-label">Loại layout</label>
                            <select {...register('layout_type_id', { valueAsNumber: true })} className="form-select">
                                {selectItem(LayoutTypeNames, t, true)}
                            </select>
                        </div>
                        <div className="col-12 col-md-4 mb-1">
                            <InputSwitch
                                className="d-flex flex-column"
                                labelSwitchName="Hoạt động"
                                labelFieldName="Trạng thái"
                                name="status_id"
                                register={register}
                            />
                        </div>
                        <div className="col-12 col-md-8 mb-1">
                            <label className="form-label">Mô tả</label>
                            <input type="text" {...register('desc')} className="form-control" />
                        </div>
                        <div className="col-12 col-md-4 mb-1">
                            <div className="demo-inline-spacing">
                                <InputSwitch
                                    classNameWrap="mt-50"
                                    className="d-flex flex-column"
                                    labelSwitchName="Có"
                                    labelFieldName="Mặc định"
                                    name="is_default"
                                    register={register}
                                />
                                <InputSwitch
                                    classNameWrap="mt-50"
                                    className="d-flex flex-column"
                                    labelSwitchName="Có"
                                    labelFieldName="Mobile"
                                    name="is_mobile"
                                    register={register}
                                />
                            </div>
                        </div>
                        <div className="col-12 mb-1">
                            <label className="form-label">
                                Nội dung <span className="error">*</span>
                            </label>
                            <textarea {...register('content')} className="form-control" rows={5}></textarea>
                            <span className="error">{errors.content?.message}</span>
                        </div>
                        <div className="col-12">
                            <UpdateButton btnText="Cập nhật" isLoading={isSubmitting} btnClass={['mt-1']} />
                        </div>
                    </div>
                </form>
            </div>
        </div>
    );
}
