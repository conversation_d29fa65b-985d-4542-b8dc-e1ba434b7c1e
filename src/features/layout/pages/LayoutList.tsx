import { useState } from 'react';
import { Helmet } from 'react-helmet-async';
import { useTranslation } from 'react-i18next';
import ContentHeader from '../../../components/partials/ContentHeader';
import Spinner from '../../../components/partials/Spinner';
import ModalConfirm from '../../../components/partials/ModalConfirm';
import { useGraphQLQuery } from '../../../hooks/useGraphQLQuery';
import { useGraphQLMutation } from '../../../hooks/useGraphQLMutation';
import { ItemStatusNames } from '../../../types/common/Item';
import SearchForm from '../../../components/partials/SearchForm';
import useQueryParams from '../../../hooks/useQueryParams';
import omitBy from 'lodash/omitBy';
import isUndefined from 'lodash/isUndefined';
import { convertConstantToSelectOptions, convertPaging, generateFilters, showToast } from '../../../utils/common';
import { LAYOUT_DELETE, LAYOUT_LIST } from '../../../services/LayoutService';
import Layout, {
    layoutFilterConfig,
    LayoutQuery,
    LayoutTypeNames,
    SearchLayout,
    SearchLayoutParam,
} from '../../../types/Layout';
import { OPERATION_NAME, PAGINATION, QUERY_KEY } from '../../../constants/common';
import PaginationTable from '../../../components/partials/PaginationTable';
import ListLayout from '../components/ListLayout';
import { keepPreviousData } from '@tanstack/react-query';
import { useAppStore } from 'stores/appStore';

export default function LayoutList() {
    const { t } = useTranslation();
    const departmentId = useAppStore((state) => state.departmentId);
    const [itemId, setItemId] = useState<number>(0);
    const [showDelete, setShowDelete] = useState<boolean>(false);

    const { queryParams, setQueryParams } = useQueryParams<SearchLayoutParam>();
    const paramConfig: SearchLayoutParam = omitBy(
        {
            limit: queryParams.limit ?? PAGINATION.limit,
            page: queryParams.page ?? '1',
            search: queryParams.search,
            status_id: queryParams.status_id,
            layout_type_id: queryParams.layout_type_id,
            department_id: departmentId.toString(),
        },
        isUndefined
    );

    const { search, limit, page, ...dataParamConfig } = paramConfig;
    const filters = generateFilters(dataParamConfig, layoutFilterConfig);

    const { data, isLoading, isRefetching, refetch } = useGraphQLQuery<LayoutQuery, SearchLayout>(
        [QUERY_KEY.LAYOUTS, paramConfig, filters],
        LAYOUT_LIST,
        {
            page: Number(page),
            limit: Number(limit),
            search,
            filters: filters.length > 0 ? filters : undefined,
        },
        OPERATION_NAME.CALL_STATIC_TOKEN,
        {
            enabled: !!paramConfig.department_id,
            placeholderData: keepPreviousData,
        }
    );

    const deleteMutation = useGraphQLMutation(LAYOUT_DELETE, '', {
        onSuccess: () => {
            showToast(true, [t('success.delete')]);
            setShowDelete(false);
            setItemId(0);
            refetch();
        },
        onError: () => showToast(false, [t('error.common')]),
    });

    const handlePageChange = (_event: React.ChangeEvent<unknown>, page: number) => {
        setQueryParams({ ...queryParams, page: page.toString() });
    };

    const deleteItem = async () => {
        if (itemId) {
            deleteMutation.mutate({ id: itemId });
        }
    };

    const handleDelete = (id: number) => {
        deleteMutation.reset();
        setItemId(id);
        setShowDelete(true);
    };

    return (
        <>
            <Helmet>
                <title>Quản lý Layout</title>
            </Helmet>
            <ContentHeader
                title="Quản lý Layout"
                contextMenu={[
                    {
                        text: 'Thêm layout',
                        to: '/layout/add',
                        icon: 'PLUS',
                    },
                ]}
            />

            <div className="content-body">
                <SearchForm
                    fields={[
                        { name: 'search', type: 'text', label: 'Từ khóa', wrapClassName: 'col-md-4 col-12' },
                        {
                            name: 'status_id',
                            type: 'select',
                            label: 'Trạng thái',
                            wrapClassName: 'col-md-4 col-12',
                            options: {
                                multiple: true,
                                choices: convertConstantToSelectOptions(ItemStatusNames, t, true),
                            },
                        },
                        {
                            name: 'layout_type_id',
                            type: 'select',
                            label: 'Loại layout',
                            wrapClassName: 'col-md-4 col-12',
                            options: {
                                multiple: true,
                                choices: convertConstantToSelectOptions(LayoutTypeNames, t, true),
                            },
                        },
                    ]}
                    isLoading={isLoading || isRefetching}
                />
                {(isLoading || isRefetching) && <Spinner />}
                {!isLoading && !isRefetching && (
                    <div className="card">
                        <ListLayout
                            items={data?.layouts_list.data ?? []}
                            paging={convertPaging<Layout, SearchLayoutParam>(
                                data?.layouts_list,
                                Number(paramConfig.limit)
                            )}
                            handleDelete={handleDelete}
                        />
                        <PaginationTable
                            countItem={data?.layouts_list.totalCount}
                            totalPage={data?.layouts_list.totalPages}
                            currentPage={data?.layouts_list.currentPage}
                            handlePageChange={handlePageChange}
                        />
                    </div>
                )}
                <ModalConfirm
                    show={showDelete}
                    text={t('confirm.delete')}
                    btnDisabled={deleteMutation.isPending}
                    changeShow={(s: boolean) => setShowDelete(s)}
                    submitAction={deleteItem}
                />
            </div>
        </>
    );
}
