import { Trash2 } from 'react-feather';
import Tag from 'types/Tag';
import { genTableIndex, getFieldHtml } from 'utils/common';
import { ItemStatusNames } from '../../../types/common/Item';
import { useTranslation } from 'react-i18next';
import { Paging } from 'types/common';

interface IProps {
    items: Tag[];
    paging: Paging;
    handleEdit: (id: number) => void;
    handleDelete: (id: number) => void;
}

export default function ListTag({ items, paging, handleEdit, handleDelete }: Readonly<IProps>) {
    const { t } = useTranslation();

    return (
        <div className="table-responsive">
            <table className="table">
                <thead>
                    <tr>
                        <th className="text-center">STT</th>
                        <th>Tag</th>
                        <th className="text-center">Trạng thái</th>
                        <th className="thAction1"></th>
                    </tr>
                </thead>
                <tbody>
                    {items.map((item: Tag, index: number) => (
                        <tr key={item.id}>
                            <td className="text-center">{genTableIndex(index, paging.limit, paging.current_page)}</td>
                            <td>
                                <span className="text-primary cursor-pointer" onClick={() => handleEdit(item.id!)}>
                                    {item.name}
                                </span>
                            </td>
                            <td className="text-center">
                                {getFieldHtml(ItemStatusNames, item.status_id as number, t)}
                            </td>
                            <td className="text-center">
                                <button
                                    type="button"
                                    title="Xoá"
                                    className="btn btn-icon btn-sm btn-flat-danger waves-effect"
                                    onClick={() => handleDelete(item.id!)}
                                >
                                    <Trash2 size={14} />
                                </button>
                            </td>
                        </tr>
                    ))}
                </tbody>
            </table>
        </div>
    );
}
