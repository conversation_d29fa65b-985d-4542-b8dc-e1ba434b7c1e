import { yupResolver } from '@hookform/resolvers/yup';
import classNames from 'classnames';
import UpdateButton from 'components/partials/UpdateButton';
import { useEffect, useLayoutEffect } from 'react';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import Tag from 'types/Tag';
import { toggleModalOpen } from 'utils/common';
import * as yup from 'yup';
import { ItemStatus } from '../../../types/common/Item';
import InputSwitch from '../../../components/partials/InputSwitch';
import slugify from 'slugify';
import { RefreshCw } from 'react-feather';
import { lowerCase } from 'lodash';

interface IProps {
    show: boolean;
    tag: Tag | undefined;
    isLoading?: boolean;
    changeShow: (s: boolean) => void;
    submitAction: (data: Tag) => void;
}

export default function ModalTagUpdate({ show, tag, isLoading, changeShow, submitAction }: Readonly<IProps>) {
    const { t } = useTranslation();
    useLayoutEffect(() => toggleModalOpen(show), [show]);

    const schema = yup
        .object({
            name: yup.string().required(t('error.required')).trim(),
        })
        .required();

    const {
        register,
        handleSubmit,
        reset,
        formState: { errors },
        setValue,
        watch,
    } = useForm<Tag>({
        resolver: yupResolver(schema),
    });
    const watchName = watch('name');

    useEffect(() => {
        if (tag && show) {
            reset({
                ...tag,
                status_id: tag.status_id === ItemStatus.ACTIVE,
            });
        } else {
            reset({
                name: '',
                slug: '',
                status_id: true,
            });
        }
    }, [tag, show, reset]);

    const onSubmit = (data: Tag) => {
        data.slug = slugify(lowerCase(data.slug || data.name));
        submitAction(data);
    };

    return (
        <>
            <div
                className={classNames('modal fade text-start modal-primary', { show })}
                style={{ display: show ? 'block' : 'none' }}
            >
                <div className="modal-dialog modal-dialog-centered modal-lg">
                    <div className="modal-content">
                        <div className="modal-header">
                            <h5 className="modal-title">{tag ? 'Cập nhật Tag' : 'Thêm mới Tag'}</h5>
                            <button type="button" className="btn-close" onClick={() => changeShow(false)} />
                        </div>
                        <form className="validate-form pt-50" onSubmit={handleSubmit(onSubmit)}>
                            <div className="modal-body">
                                <div className="row">
                                    <div className="col-12 col-sm-6 mb-1">
                                        <label className="form-label">
                                            Tag <span className="error">*</span>
                                        </label>
                                        <input
                                            {...register('name')}
                                            type="text"
                                            className={classNames('form-control', {
                                                'is-invalid': Boolean(errors.name?.message),
                                            })}
                                        />
                                        <span className="error">{errors.name?.message}</span>
                                    </div>
                                    <div className="col-12 col-sm-6 mb-1">
                                        <label className="form-label">
                                            Slug{' '}
                                            <RefreshCw
                                                size={14}
                                                className="text-primary cursor-pointer ms-50"
                                                onClick={() => setValue('slug', slugify(lowerCase(watchName)))}
                                            />
                                        </label>
                                        <input {...register('slug')} type="text" className="form-control" />
                                    </div>
                                    <InputSwitch
                                        classNameWrap="col-12 col-sm-6 mb-1"
                                        className="d-flex flex-column"
                                        labelSwitchName="Hoạt động"
                                        labelFieldName="Trạng thái"
                                        name="status_id"
                                        register={register}
                                    />
                                </div>
                            </div>
                            <div className="modal-footer">
                                <UpdateButton
                                    btnText={tag ? 'Cập nhật' : 'Thêm mới'}
                                    isLoading={isLoading}
                                    hasDivWrap={false}
                                />
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            {show && <div className="modal-backdrop fade show" />}
        </>
    );
}
