import { yupResolver } from '@hookform/resolvers/yup';
import classNames from 'classnames';
import UpdateButton from 'components/partials/UpdateButton';
import { useEffect, useLayoutEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import Category from 'types/Category';
import { toggleModalOpen } from 'utils/common';
import * as yup from 'yup';
import { ArticleType, ItemStatus, SelectOption } from '../../../types/common/Item';
import InputSwitch from '../../../components/partials/InputSwitch';
import slugify from 'slugify';
import { RefreshCw } from 'react-feather';
import { lowerCase } from 'lodash';
import Layout, { LayoutType } from '../../../types/Layout';
import Select, { SingleValue } from 'react-select';

interface IProps {
    show: boolean;
    category: Category | undefined;
    categories: Category[];
    layouts: Layout[];
    categoryTypeId: string | undefined;
    articleTypeId: number;
    isLoading?: boolean;
    changeShow: (s: boolean) => void;
    submitAction: (data: Category) => void;
}

export default function ModalCategoryUpdate({
    show,
    category,
    categories,
    layouts,
    categoryTypeId,
    articleTypeId,
    isLoading,
    changeShow,
    submitAction,
}: Readonly<IProps>) {
    const { t } = useTranslation();
    useLayoutEffect(() => toggleModalOpen(show), [show]);
    const [webLayoutValue, setWebLayoutValue] = useState<SelectOption>();
    const [mobileLayoutValue, setMobileLayoutValue] = useState<SelectOption>();
    const [articleWebLayoutValue, setArticleWebLayoutValue] = useState<SelectOption>();
    const [articleMobileLayoutValue, setArticleMobileLayoutValue] = useState<SelectOption>();

    const schema = yup
        .object({
            name: yup.string().required(t('error.required')).trim(),
            display_order: yup
                .number()
                .typeError(t('error.number'))
                .required(t('error.required'))
                .min(1, t('error.min_1'))
                .max(99, t('error.max_99')),
        })
        .required();

    const {
        register,
        handleSubmit,
        reset,
        formState: { errors },
        setValue,
        watch,
    } = useForm<Category>({
        resolver: yupResolver(schema),
    });
    const watchName = watch('name');

    useEffect(() => {
        if (category && show) {
            reset({
                ...category,
                status_id: category.status_id === ItemStatus.ACTIVE,
            });
            let item = layouts.find((item) => item.id === category.web_layout_id);
            setWebLayoutValue(item ? { value: item.id!, label: item.name } : undefined);
            item = layouts.find((item) => item.id === category.mobile_layout_id);
            setMobileLayoutValue(item ? { value: item.id!, label: item.name } : undefined);
            item = layouts.find((item) => item.id === category.article_web_layout_id);
            setArticleWebLayoutValue(item ? { value: item.id!, label: item.name } : undefined);
            item = layouts.find((item) => item.id === category.article_mobile_layout_id);
            setArticleMobileLayoutValue(item ? { value: item.id!, label: item.name } : undefined);
        } else {
            const webLayout = layouts.find(
                (item) => item.layout_type_id === LayoutType.CATEGORY && !item.is_mobile && item.is_default
            );
            const articleWebLayout = layouts.find(
                (item) => item.layout_type_id === LayoutType.DETAIL && !item.is_mobile && item.is_default
            );
            const mobileLayout = layouts.find(
                (item) => item.layout_type_id === LayoutType.CATEGORY && item.is_mobile && item.is_default
            );
            const articleMobileLayout = layouts.find(
                (item) => item.layout_type_id === LayoutType.DETAIL && item.is_mobile && item.is_default
            );
            reset({
                name: '',
                slug: '',
                status_id: true,
                display_order: 1,
                is_major: true,
                parent_id: 0,
                web_layout_id: webLayout?.id ?? 0,
                mobile_layout_id: mobileLayout?.id ?? 0,
                article_web_layout_id: articleWebLayout?.id ?? 0,
                article_mobile_layout_id: articleMobileLayout?.id ?? 0,
            });
            setWebLayoutValue(webLayout ? { value: webLayout.id!, label: webLayout.name } : undefined);
            setMobileLayoutValue(mobileLayout ? { value: mobileLayout.id!, label: mobileLayout.name } : undefined);
            setArticleWebLayoutValue(
                articleWebLayout ? { value: articleWebLayout.id!, label: articleWebLayout.name } : undefined
            );
            setArticleMobileLayoutValue(
                articleMobileLayout ? { value: articleMobileLayout.id!, label: articleMobileLayout.name } : undefined
            );
        }
    }, [category, show, reset, layouts]);

    const onChangeWebLayout = (option: SingleValue<SelectOption>) => {
        setWebLayoutValue(option ?? undefined);
        setValue('web_layout_id', option ? option.value : 0);
    };

    const onChangeMobileLayout = (option: SingleValue<SelectOption>) => {
        setMobileLayoutValue(option ?? undefined);
        setValue('mobile_layout_id', option ? option.value : 0);
    };

    const onChangeArticleWebLayout = (option: SingleValue<SelectOption>) => {
        setArticleWebLayoutValue(option ?? undefined);
        setValue('article_web_layout_id', option ? option.value : 0);
    };

    const onChangeArticleMobileLayout = (option: SingleValue<SelectOption>) => {
        setArticleMobileLayoutValue(option ?? undefined);
        setValue('article_mobile_layout_id', option ? option.value : 0);
    };

    const onSubmit = (data: Category) => {
        data.slug = slugify(lowerCase(data.slug || data.name));
        if (data.web_layout_id === 0) data.web_layout_id = null;
        if (data.mobile_layout_id === 0) data.mobile_layout_id = null;
        if (data.article_web_layout_id === 0) data.article_web_layout_id = null;
        if (data.article_mobile_layout_id === 0) data.article_mobile_layout_id = null;
        submitAction(data);
    };

    return (
        <>
            <div
                className={classNames('modal fade text-start modal-primary', { show })}
                style={{ display: show ? 'block' : 'none' }}
            >
                <div className="modal-dialog modal-dialog-centered modal-lg">
                    <div className="modal-content">
                        <div className="modal-header">
                            <h5 className="modal-title">
                                {category
                                    ? `Cập nhật ${t(`${categoryTypeId}.single`)}`
                                    : `Thêm mới ${t(`${categoryTypeId}.single`)}`}
                            </h5>
                            <button type="button" className="btn-close" onClick={() => changeShow(false)} />
                        </div>
                        <form className="validate-form pt-50" onSubmit={handleSubmit(onSubmit)}>
                            <div className="modal-body">
                                <div className="row">
                                    <div className="col-12 col-sm-6 mb-1">
                                        <label className="form-label">
                                            {t(`${categoryTypeId}.single`)} <span className="error">*</span>
                                        </label>
                                        <input
                                            {...register('name')}
                                            type="text"
                                            className={classNames('form-control', {
                                                'is-invalid': Boolean(errors.name?.message),
                                            })}
                                        />
                                        <span className="error">{errors.name?.message}</span>
                                    </div>
                                    <div className="col-12 col-sm-6 mb-1">
                                        <label className="form-label">
                                            Slug{' '}
                                            <RefreshCw
                                                size={14}
                                                className="text-primary cursor-pointer ms-50"
                                                onClick={() => setValue('slug', slugify(lowerCase(watchName)))}
                                            />
                                        </label>
                                        <input {...register('slug')} type="text" className="form-control" />
                                    </div>
                                    {categoryTypeId === 'category' && (
                                        <div className="col-12 col-sm-6 mb-1">
                                            <label className="form-label">Chuyên mục cha</label>
                                            <select
                                                {...register('parent_id', { valueAsNumber: true })}
                                                className="form-select"
                                            >
                                                <option value="0">-- Chọn chuyên mục --</option>
                                                {categories
                                                    .filter((item) => item.id !== category?.id)
                                                    .map((item) => (
                                                        <option key={item.id} value={item.id}>
                                                            {item.name}
                                                        </option>
                                                    ))}
                                            </select>
                                        </div>
                                    )}
                                    <div className="col-12 col-sm-6 mb-1">
                                        <div className="row">
                                            <InputSwitch
                                                classNameWrap="col-6"
                                                className="d-flex flex-column"
                                                labelSwitchName="Hoạt động"
                                                labelFieldName="Trạng thái"
                                                name="status_id"
                                                register={register}
                                            />
                                            {categoryTypeId === 'category' && (
                                                <InputSwitch
                                                    classNameWrap="col-6"
                                                    className="d-flex flex-column"
                                                    labelSwitchName="Có"
                                                    labelFieldName="Danh mục chính"
                                                    name="is_major"
                                                    register={register}
                                                />
                                            )}
                                        </div>
                                    </div>
                                    {categoryTypeId === 'topic' && <div className="col-12 col-sm-6 mb-1" />}
                                    {categoryTypeId === 'category' && articleTypeId === ArticleType.ELECTRONIC && (
                                        <>
                                            <div className="col-12 col-sm-6 mb-1">
                                                <label className="form-label">Layout</label>
                                                <Select
                                                    options={layouts
                                                        .filter(
                                                            (item) =>
                                                                item.layout_type_id === LayoutType.CATEGORY &&
                                                                !item.is_mobile
                                                        )
                                                        .map((item) => ({
                                                            value: item.id!,
                                                            label: item.name,
                                                        }))}
                                                    onChange={onChangeWebLayout}
                                                    value={webLayoutValue}
                                                    isClearable
                                                    placeholder="Chọn..."
                                                />
                                            </div>
                                            <div className="col-12 col-sm-6 mb-1">
                                                <label className="form-label">Mobile Layout</label>
                                                <Select
                                                    options={layouts
                                                        .filter(
                                                            (item) =>
                                                                item.layout_type_id === LayoutType.CATEGORY &&
                                                                item.is_mobile
                                                        )
                                                        .map((item) => ({
                                                            value: item.id!,
                                                            label: item.name,
                                                        }))}
                                                    onChange={onChangeMobileLayout}
                                                    value={mobileLayoutValue}
                                                    isClearable
                                                    placeholder="Chọn..."
                                                />
                                            </div>
                                            <div className="col-12 col-sm-6 mb-1">
                                                <label className="form-label">Article Layout</label>
                                                <Select
                                                    options={layouts
                                                        .filter(
                                                            (item) =>
                                                                item.layout_type_id === LayoutType.DETAIL &&
                                                                !item.is_mobile
                                                        )
                                                        .map((item) => ({
                                                            value: item.id!,
                                                            label: item.name,
                                                        }))}
                                                    onChange={onChangeArticleWebLayout}
                                                    value={articleWebLayoutValue}
                                                    isClearable
                                                    placeholder="Chọn..."
                                                />
                                            </div>
                                            <div className="col-12 col-sm-6 mb-1">
                                                <label className="form-label">Article Mobile Layout</label>
                                                <Select
                                                    options={layouts
                                                        .filter(
                                                            (item) =>
                                                                item.layout_type_id === LayoutType.DETAIL &&
                                                                item.is_mobile
                                                        )
                                                        .map((item) => ({
                                                            value: item.id!,
                                                            label: item.name,
                                                        }))}
                                                    onChange={onChangeArticleMobileLayout}
                                                    value={articleMobileLayoutValue}
                                                    isClearable
                                                    placeholder="Chọn..."
                                                />
                                            </div>
                                        </>
                                    )}
                                    <div className="col-12 col-sm-3 mb-1">
                                        <label className="form-label">
                                            Thứ tự <span className="error">*</span>
                                        </label>
                                        <input
                                            {...register('display_order')}
                                            type="text"
                                            className={classNames('form-control', {
                                                'is-invalid': Boolean(errors.display_order?.message),
                                            })}
                                        />
                                        <span className="error">{errors.display_order?.message}</span>
                                    </div>
                                    <div className="col-12 col-sm-9 mb-1">
                                        <label className="form-label">Mô tả</label>
                                        <input type="text" {...register('desc')} className="form-control" />
                                    </div>
                                </div>
                            </div>
                            <div className="modal-footer">
                                <UpdateButton
                                    btnText={category ? 'Cập nhật' : 'Thêm mới'}
                                    isLoading={isLoading}
                                    hasDivWrap={false}
                                />
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            {show && <div className="modal-backdrop fade show" />}
        </>
    );
}
