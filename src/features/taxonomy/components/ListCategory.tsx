import { <PERSON>, <PERSON>, Star, Trash2 } from 'react-feather';
import Category from 'types/Category';
import { getFieldHtml, getFieldInArrayObject } from 'utils/common';
import { ItemStatusNames } from '../../../types/common/Item';
import { useTranslation } from 'react-i18next';
import { Link } from 'react-router-dom';

interface IProps {
    items: Category[];
    categoryTypeId: string | undefined;
    articleTypeId: string | undefined;
    handleEdit: (id: number) => void;
    handleDelete: (id: number) => void;
}

export default function ListCategory({
    items,
    categoryTypeId,
    articleTypeId,
    handleEdit,
    handleDelete,
}: Readonly<IProps>) {
    const { t } = useTranslation();

    return (
        <div className="table-responsive">
            <table className="table">
                <thead>
                    <tr>
                        <th className="text-center">STT</th>
                        <th>{t(`${categoryTypeId}.single`)}</th>
                        {categoryTypeId === 'category' && <th className="text-center"><PERSON><PERSON><PERSON><PERSON> m<PERSON> cha</th>}
                        <th className="text-center">Trạng thái</th>
                        <th className="thAction1"></th>
                    </tr>
                </thead>
                <tbody>
                    {items.map((item: Category) => (
                        <tr key={item.id}>
                            <td className="text-center">{item.display_order}</td>
                            <td>
                                <span className="text-primary cursor-pointer" onClick={() => handleEdit(item.id!)}>
                                    {item.parent_id && <Disc size={14} />} {item.name}{' '}
                                    {item.is_major && categoryTypeId === 'category' && (
                                        <Star size={14} className="text-warning" />
                                    )}
                                </span>
                            </td>
                            {categoryTypeId === 'category' && (
                                <td className="text-center">
                                    {item.parent_id && getFieldInArrayObject(items, item.parent_id, 'name')}
                                </td>
                            )}
                            <td className="text-center">
                                {getFieldHtml(ItemStatusNames, item.status_id as number, t)}
                            </td>
                            <td className="d-flex text-center">
                                <Link
                                    to={`/taxonomy/${categoryTypeId}/${articleTypeId}/${item.id}`}
                                    type="button"
                                    title="Danh sách bài viết"
                                    className="btn btn-icon btn-sm btn-flat-info waves-effect"
                                >
                                    <Eye size={14} />
                                </Link>
                                <button
                                    type="button"
                                    title="Xoá"
                                    className="btn btn-icon btn-sm btn-flat-danger waves-effect"
                                    onClick={() => handleDelete(item.id!)}
                                >
                                    <Trash2 size={14} />
                                </button>
                            </td>
                        </tr>
                    ))}
                </tbody>
            </table>
        </div>
    );
}
