import { useMemo, useState } from 'react';
import { Helmet } from 'react-helmet-async';
import { useTranslation } from 'react-i18next';
import { find } from 'lodash';
import ContentHeader from '../../../components/partials/ContentHeader';
import Spinner from '../../../components/partials/Spinner';
import ModalConfirm from '../../../components/partials/ModalConfirm';
import { useGraphQLQuery } from '../../../hooks/useGraphQLQuery';
import { useGraphQLMutation } from '../../../hooks/useGraphQLMutation';
import { ArticleType, ArticleTypeNames, ItemStatus, ItemStatusNames } from 'types/common/Item';
import SearchForm from '../../../components/partials/SearchForm';
import useQueryParams from '../../../hooks/useQueryParams';
import omitBy from 'lodash/omitBy';
import isUndefined from 'lodash/isUndefined';
import {
    convertConstantToSelectOptions,
    generateFilters,
    getFieldInArrayObject,
    showToast,
} from '../../../utils/common';
import {
    CATEGORY_CREATE,
    CATEGORY_DELETE,
    CATEGORY_LIST_WITHOUT_CHILDREN,
    CATEGORY_UPDATE,
    getFlatCategories,
} from '../../../services/CategoryService';
import Category, {
    categoryFilterConfig,
    CategoryQuery,
    CategoryType,
    CategoryTypeNames,
    SearchCategory,
    SearchCategoryParam,
} from '../../../types/Category';
import { LIMIT_MAX, OPERATION_NAME, PAGE_NUMBER_DEFAULT, QUERY_KEY } from '../../../constants/common';
import { useAppStore } from '../../../stores/appStore';
import { keepPreviousData } from '@tanstack/react-query';
import PaginationTable from '../../../components/partials/PaginationTable';
import ListCategory from '../components/ListCategory';
import ModalCategoryUpdate from '../components/ModalCategoryUpdate';
import { useNavigate, useParams } from 'react-router-dom';
import { layoutFilterConfig, LayoutQuery, LayoutType, SearchLayout, SearchLayoutParam } from '../../../types/Layout';
import { LAYOUT_LIST } from '../../../services/LayoutService';

export default function CategoryList() {
    const { t } = useTranslation();
    const { type1, type2 } = useParams();
    const [showUpdate, setShowUpdate] = useState<boolean>(false);
    const [showDelete, setShowDelete] = useState<boolean>(false);
    const [itemId, setItemId] = useState<number>(0);
    const navigate = useNavigate();
    const departmentId = useAppStore((state) => state.departmentId);
    const typeId1 = useMemo(() => +getFieldInArrayObject(CategoryTypeNames, type1 ?? '', 'id', '', 'name'), [type1]);
    const typeId2 = useMemo(() => +getFieldInArrayObject(ArticleTypeNames, type2 ?? '', 'id', '', 'name'), [type2]);
    if (!typeId1 || !typeId2) {
        navigate('/not-found');
    }

    const { queryParams, setQueryParams } = useQueryParams<SearchCategoryParam>();
    const paramConfig: SearchCategoryParam = omitBy(
        {
            limit: LIMIT_MAX, // queryParams.limit ?? PAGINATION.limit,
            page: queryParams.page ?? '1',
            search: queryParams.search,
            status_id: queryParams.status_id,
            category_type_id: typeId1.toString(),
            article_type_id: typeId2.toString(),
            department_id: departmentId.toString(),
        },
        isUndefined
    );

    const { search, limit, page, ...dataParamConfig } = paramConfig;
    const filters = generateFilters(dataParamConfig, categoryFilterConfig);

    const { data, isLoading, isRefetching, refetch } = useGraphQLQuery<CategoryQuery, SearchCategory>(
        [QUERY_KEY.CATEGORIES, paramConfig, filters],
        CATEGORY_LIST_WITHOUT_CHILDREN,
        {
            page: Number(page),
            limit: Number(limit),
            search,
            filters: filters.length > 0 ? filters : undefined,
            sorts: ['display_order:ASC'],
        },
        OPERATION_NAME.CALL_STATIC_TOKEN,
        {
            enabled: !!paramConfig.department_id && !!typeId1 && !!typeId2,
            placeholderData: keepPreviousData,
        }
    );

    const categories = getFlatCategories(data?.categories_list.data ?? []);

    const paramConfigLayout: SearchLayoutParam = omitBy(
        {
            status_id: ItemStatus.ACTIVE.toString(),
            layout_type_id: [LayoutType.CATEGORY, LayoutType.DETAIL].join(','),
            department_id: departmentId.toString(),
        },
        isUndefined
    );
    const filterLayouts = generateFilters(paramConfigLayout, layoutFilterConfig);

    const { data: layouts } = useGraphQLQuery<LayoutQuery, SearchLayout>(
        [QUERY_KEY.LAYOUTS, paramConfigLayout, filterLayouts],
        LAYOUT_LIST,
        {
            page: PAGE_NUMBER_DEFAULT,
            limit: LIMIT_MAX,
            filters: filterLayouts.length > 0 ? filterLayouts : undefined,
        },
        OPERATION_NAME.CALL_STATIC_TOKEN,
        {
            enabled:
                !!paramConfigLayout.department_id &&
                typeId1 === CategoryType.CATEGORY &&
                typeId2 === ArticleType.ELECTRONIC,
            placeholderData: keepPreviousData,
        }
    );

    const saveMutation = useGraphQLMutation<{}, { id?: number; body: Partial<Category> }>(
        itemId > 0 ? CATEGORY_UPDATE : CATEGORY_CREATE,
        '',
        {
            onSuccess: () => {
                showToast(true, [t('success.update')]);
                setShowUpdate(false);
                setItemId(0);
                refetch();
            },
            onError: () => showToast(false, [t('error.common')]),
        }
    );

    const deleteMutation = useGraphQLMutation(CATEGORY_DELETE, '', {
        onSuccess: () => {
            showToast(true, [t('success.delete')]);
            setShowDelete(false);
            setItemId(0);
            refetch();
        },
        onError: () => showToast(false, [t('error.common')]),
    });

    const updateItem = async (body: Category) => {
        body.status_id = body.status_id ? ItemStatus.ACTIVE : ItemStatus.PENDING;
        body.department_id = departmentId;
        body.category_type_id = typeId1;
        body.article_type_id = typeId2;
        if (body.parent_id === 0) delete body.parent_id;
        delete body.id;
        delete body.children;
        if (itemId === 0) {
            saveMutation.mutate({ body });
        } else {
            saveMutation.mutate({ id: itemId, body });
        }
    };

    const deleteItem = async () => {
        if (itemId) {
            deleteMutation.mutate({ id: itemId });
        }
    };

    const handleEdit = (id: number) => {
        saveMutation.reset();
        setItemId(id);
        setShowUpdate(true);
    };

    const handleDelete = (id: number) => {
        deleteMutation.reset();
        setItemId(id);
        setShowDelete(true);
    };

    const handlePageChange = (_event: React.ChangeEvent<unknown>, page: number) => {
        setQueryParams({ ...queryParams, page: page.toString() });
    };

    const actionMenu = () => {
        saveMutation.reset();
        setShowUpdate(true);
        setItemId(0);
    };

    return (
        <>
            <Helmet>
                <title>{t(`${type1}.multiple`, { data: t(`${type2}.single`) })}</title>
            </Helmet>
            <ContentHeader
                title={t(`${type1}.multiple`, { data: t(`${type2}.single`) })}
                contextMenu={[
                    {
                        text: t(`${type1}.add`),
                        to: '',
                        icon: 'PLUS',
                        fnCallBack: { actionMenu },
                    },
                ]}
            />
            <div className="content-body">
                <div className="col-12">
                    <SearchForm
                        fields={[
                            { name: 'search', type: 'text', label: 'Từ khóa', wrapClassName: 'col-md-4 col-12' },
                            {
                                name: 'status_id',
                                type: 'select',
                                label: 'Trạng thái',
                                wrapClassName: 'col-md-4 col-12',
                                options: {
                                    multiple: true,
                                    choices: convertConstantToSelectOptions(ItemStatusNames, t, true),
                                },
                            },
                        ]}
                        isLoading={isLoading || isRefetching}
                        searchClass="col-md-4 pt-2"
                    />
                    {(isLoading || isRefetching) && <Spinner />}
                    {!isLoading && !isRefetching && (
                        <>
                            <div className="card">
                                <ListCategory
                                    items={categories}
                                    categoryTypeId={type1}
                                    articleTypeId={type2}
                                    handleEdit={handleEdit}
                                    handleDelete={handleDelete}
                                />
                                <PaginationTable
                                    countItem={data?.categories_list.totalCount}
                                    totalPage={data?.categories_list.totalPages}
                                    currentPage={data?.categories_list.currentPage}
                                    handlePageChange={handlePageChange}
                                />
                            </div>
                            <ModalCategoryUpdate
                                show={showUpdate}
                                category={find(categories, { id: itemId })}
                                categories={categories}
                                layouts={layouts?.layouts_list.data ?? []}
                                categoryTypeId={type1}
                                articleTypeId={typeId2}
                                isLoading={saveMutation.isPending}
                                changeShow={(s: boolean) => setShowUpdate(s)}
                                submitAction={updateItem}
                            />
                        </>
                    )}
                    <ModalConfirm
                        show={showDelete}
                        text={t('confirm.delete')}
                        btnDisabled={deleteMutation.isPending}
                        changeShow={(s: boolean) => setShowDelete(s)}
                        submitAction={deleteItem}
                    />
                </div>
            </div>
        </>
    );
}
