import { yupResolver } from '@hookform/resolvers/yup';
import classNames from 'classnames';
import UpdateButton from 'components/partials/UpdateButton';
import { OPERATION_NAME } from 'constants/common';
import { useGraphQLMutation } from 'hooks/useGraphQLMutation';
import { useForm } from 'react-hook-form';
import { useNavigate } from 'react-router-dom';
import { USER_FORGOT_PASSWORD } from 'services/UserService';
import { ForgotPasswordData, UserForgotPass, UserRole } from 'types/User';
import { showToast } from 'utils/common';
import * as yup from 'yup';
import { useTranslation } from 'react-i18next';

export default function ForgotForm() {
    const { t } = useTranslation();
    const navigate = useNavigate();
    const schema = yup
        .object({
            email: yup.string().required(t('error.required')).email(t('error.email')),
        })
        .required();
    const {
        register,
        handleSubmit,
        formState: { isSubmitting, errors },
    } = useForm<ForgotPasswordData>({
        resolver: yupResolver(schema),
    });

    const { mutate } = useGraphQLMutation<UserForgotPass, { body: ForgotPasswordData }>(
        USER_FORGOT_PASSWORD,
        OPERATION_NAME.FORGOT_PASSWORD,
        {
            onSuccess: (data) => {
                const token = data.auth_forgot_pass;
                showToast(true, [t('success.update')]);
                navigate(`/change-password?token=${encodeURIComponent(token)}`);
            },
            onError: () => showToast(false, [t('error.common')]),
        }
    );

    const onSubmit = async (body: ForgotPasswordData) => {
        body.role_id = UserRole.ADMIN;
        mutate({ body });
    };

    return (
        <form className="auth-login-form mt-2" onSubmit={handleSubmit(onSubmit)}>
            <div className="mb-1">
                <label className="form-label">Email</label>
                <input
                    {...register('email')}
                    className={classNames('form-control', { error: Boolean(errors.email?.message) })}
                    type="text"
                />
                <span className="error">{errors.email?.message}</span>
            </div>
            <UpdateButton isLoading={isSubmitting} btnClass={['w-100']} btnText="Gửi yêu cầu" hasDivWrap={false} />
        </form>
    );
}
