import { yupResolver } from '@hookform/resolvers/yup';
import classNames from 'classnames';
import UpdateButton from 'components/partials/UpdateButton';
import { useGraphQLMutation } from 'hooks/useGraphQLMutation';
import { useState } from 'react';
import { Eye, EyeOff } from 'react-feather';
import { useForm } from 'react-hook-form';
import { Link, useNavigate } from 'react-router-dom';
import { LOGIN } from 'services/UserService';
import { showToast } from 'utils/common';
import * as yup from 'yup';
import { LoginData, UserQuery } from '../../../types/User';
import { OPERATION_NAME } from '../../../constants/common';
import { useTranslation } from 'react-i18next';
import { isEmpty } from 'lodash';

interface IProps {
    url: string;
}

export default function LoginForm({ url }: Readonly<IProps>) {
    const { t } = useTranslation();
    const navigate = useNavigate();
    const [showPass, setShowPass] = useState(false);
    const schema = yup
        .object({
            user_name: yup.string().required(t('error.required')).trim(),
            password: yup.string().required(t('error.required')).trim(),
        })
        .required();
    const {
        register,
        handleSubmit,
        formState: { isSubmitting, errors },
    } = useForm<LoginData>({
        resolver: yupResolver(schema),
        defaultValues: {
            //TODO: remove this
            user_name: 'admin',
            password: 'Admin@123456',
        },
    });

    const { mutate } = useGraphQLMutation<UserQuery, { body: LoginData }>(LOGIN, OPERATION_NAME.LOGIN, {
        onSuccess: () => {
            showToast(true, ['Đăng nhập thành công']);
            navigate(isEmpty(url) ? '/dashboard' : url);
        },
    });

    const onSubmit = async (body: LoginData) => mutate({ body });

    return (
        <form
            className="mt-2 bg-white auth-login-form px-6 sm:px-14 pt-20 pb-32 rounded-lg w-full sm:w-[450px]"
            onSubmit={handleSubmit(onSubmit)}
        >
            <h1 className="text-[#171A1FFF] text-2xl font-semibold text-center">Đăng nhập</h1>
            <div className="mt-8 mb-1">
                <label className="form-label text-[#424856]">Tên đăng nhập</label>
                <input
                    {...register('user_name')}
                    className={classNames('form-control', { error: Boolean(errors.user_name?.message) })}
                    type="text"
                />
                <span className="error">{errors.user_name?.message}</span>
            </div>
            <div className="mb-1">
                <div className="d-flex justify-content-between">
                    <label className="form-label">Mật khẩu</label>
                </div>
                <div className="input-group input-group-merge form-password-toggle">
                    <input
                        {...register('password')}
                        className={classNames('form-control', 'form-control-merge', {
                            error: Boolean(errors.password?.message),
                        })}
                        type={showPass ? 'text' : 'password'}
                        placeholder="············"
                    />
                    <span
                        className="cursor-pointer input-group-text"
                        onClick={() => setShowPass((prevShowPass) => !prevShowPass)}
                    >
                        {showPass ? <EyeOff size={14} /> : <Eye size={14} />}
                    </span>
                </div>
                <span className="error">{errors.password?.message}</span>
            </div>
            <UpdateButton
                isLoading={isSubmitting}
                btnClass={['w-100 rounded-full']}
                btnText="Đăng nhập"
                hasDivWrap={false}
            />
            <Link
                to="/forgot"
                className="text-[#A42C48FF] hover:text-[#A42C48FF] w-full block mt-2 text-sm text-center mx-auto"
            >
                Quên mật khẩu
            </Link>
        </form>
    );
}
