import { AUTH_KEYS } from 'constants/auth';
import { useEffect, useLayoutEffect } from 'react';
import { Outlet, useNavigate, useSearchParams } from 'react-router-dom';
import { getLocalStorage } from 'utils/localStorage';
import './authentication.css';

export default function AuthPage() {
    const navigate = useNavigate();
    const [searchParams] = useSearchParams();
    useEffect(() => {
        const isLogin = Boolean(getLocalStorage(AUTH_KEYS.ACCESS_TOKEN));
        if (isLogin) {
            const path = searchParams.get('path');
            if (path) {
                navigate(`/${path}`);
            } else {
                navigate('/dashboard');
            }
        }
    }, [navigate, searchParams]);
    useLayoutEffect(() => {
        document.body.classList.add('blank-page');
    }, []);
    return <Outlet />;
}
