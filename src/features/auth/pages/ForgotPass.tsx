import { ChevronLeft } from 'react-feather';
import { Helmet } from 'react-helmet-async';
import { Link } from 'react-router-dom';
import ForgotForm from '../components/ForgotForm';

export default function ForgotPass() {
    return (
        <>
            <Helmet>
                <title>Quên mật khẩu</title>
            </Helmet>
            <div className="flex flex-col min-h-screen">
                <div className="flex-1 flex flex-col py-10 w-full max-w-[600px] mx-auto items-center px-4 sm:px-0">
                    <img src="/assets/images/icons/forgot.png" className="w-[120px] sm:w-[180px] mx-auto" alt="" />
                    <div className="mx-auto w-full sm:col-12 sm:col-sm-8 sm:col-md-6 sm:col-lg-12 px-2 sm:px-xl-2">
                        <h1 className="text-[#323743FF] text-[28px] sm:text-[40px] font-bold text-center">
                            Bạn quên mật khẩu?
                        </h1>
                        <p className="text-center mt-1 text-xs sm:text-sm text-[#9095A1FF]">
                            Vui lòng nhập Email của tài khoản, chúng tôi sẽ cung cấp mật khẩu mới đến bạn!
                        </p>
                        <ForgotForm />
                        <Link
                            to="/"
                            className="flex items-center gap-x-1 text-[#171A1FFF] hover:text-[#171A1FFF] text-base sm:text-lg justify-center mt-5"
                        >
                            <ChevronLeft size={14} />
                            <span> Quay về Đăng nhập</span>
                        </Link>
                    </div>
                </div>
                <div className="px-4 sm:px-10 py-4 flex gap-x-2 text-xs sm:text-sm relative z-[1] items-center">
                    <a className="text-[#9095A1FF] hover:text-[#9095A1FF]" href="/">
                        Mediasoft©2024
                    </a>
                    <div className="w-[1px] bg-[#DEE1E6FF] h-4 sm:h-5"></div>
                    <a className="text-[#9095A1FF] hover:text-[#9095A1FF]" href="/">
                        Privacy Policy
                    </a>
                </div>
            </div>
        </>
    );
}
