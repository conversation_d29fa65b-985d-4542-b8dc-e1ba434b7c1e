import { yupResolver } from '@hookform/resolvers/yup';
import classNames from 'classnames';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import * as yup from 'yup';
import Portlet, { PortletType, PortletTypeNames } from '../../../types/Portlet';
import { selectItem, showToast } from '../../../utils/common';
import UpdateButton from '../../../components/partials/UpdateButton';
import InputSwitch from '../../../components/partials/InputSwitch';
import { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAppStore } from '../../../stores/appStore';
import { useGraphQLMutation } from '../../../hooks/useGraphQLMutation';
import { ItemStatus } from '../../../types/common/Item';
import { EXECUTE_SQL, PORTLET_CREATE, PORTLET_UPDATE } from '../../../services/PortletService';
import slugify from 'slugify';
import { allExpanded, defaultStyles, JsonView } from 'react-json-view-lite';
import 'react-json-view-lite/dist/index.css';
import { SkipForward } from 'react-feather';
import { axiosGraphQLClient } from '../../../services/AxiosGraphQLClient';
import Spinner from '../../../components/partials/Spinner';

interface IProps {
    id: number;
    portlet?: Portlet;
}

export function UpdatePortletForm({ id, portlet }: Readonly<IProps>) {
    const { t } = useTranslation();
    const navigate = useNavigate();

    const departmentId = useAppStore((state) => state.departmentId);

    const schema = yup
        .object({
            code: yup.string().required(t('error.required')).trim(),
            name: yup.string().required(t('error.required')).trim(),
            content: yup.string().required(t('error.required')).trim(),
        })
        .required();

    const {
        register,
        handleSubmit,
        reset,
        watch,
        formState: { isSubmitting, errors },
    } = useForm<Portlet>({
        resolver: yupResolver(schema),
    });

    const [sqlResult, setSqlResult] = useState<{}>({});
    const [isExecutingSql, setIsExecutingSql] = useState(false);

    useEffect(() => {
        if (portlet) {
            reset({
                ...portlet,
                status_id: portlet.status_id === ItemStatus.ACTIVE,
            });
        } else {
            reset({
                name: '',
                code: '',
                sql: '',
                desc: '',
                content: '',
                status_id: true,
                layout_type_id: PortletType.DYNAMIC,
                is_client: false,
            });
        }
    }, [portlet, reset, departmentId]);

    const createMutation = useGraphQLMutation<{}, { body: Partial<Portlet> }>(PORTLET_CREATE, '', {
        onSuccess: () => {
            showToast(true, [t('success.update')]);
            navigate('/portlet');
        },
        onError: () => {
            showToast(false, [t('error.common')]);
        },
    });

    const updateMutation = useGraphQLMutation<{}, { id: number; body: Partial<Portlet> }>(PORTLET_UPDATE, '', {
        onSuccess: () => {
            showToast(true, [t('success.update')]);
            navigate('/portlet');
        },
        onError: () => {
            showToast(false, [t('error.common')]);
        },
    });

    const executeSql = async () => {
        const sql = watch('sql');
        if (!sql) {
            showToast(false, ['Vui lòng nhập SQL']);
            return;
        }
        setIsExecutingSql(true);
        try {
            const result = await axiosGraphQLClient.request(EXECUTE_SQL, { sql });
            setSqlResult(result.execute_sql || {});
        } catch {
            showToast(false, ['Lỗi khi thực thi SQL']);
        } finally {
            setIsExecutingSql(false);
        }
    };

    const onSubmit = (data: Portlet) => {
        const formData = {
            ...data,
            code: slugify(data.code),
            status_id: data.status_id === true ? ItemStatus.ACTIVE : ItemStatus.PENDING,
            department_id: departmentId,
            desc: data.desc ?? null,
            sql: data.sql ?? null,
        };
        if (id > 0) {
            delete formData.id;
            updateMutation.mutate({ id, body: formData });
        } else {
            createMutation.mutate({ body: formData });
        }
    };

    return (
        <div className="card">
            <div className="card-header">
                <h4 className="card-title">{id > 0 ? 'Cập nhật portlet' : 'Thêm mới portlet'}</h4>
            </div>
            <div className="card-body">
                <form className="validate-form" onSubmit={handleSubmit(onSubmit)}>
                    <div className="row">
                        <div className="col-12 col-md-4 mb-1">
                            <label className="form-label">
                                Mã portlet <span className="error">*</span>
                            </label>
                            <input
                                type="text"
                                {...register('code')}
                                className={classNames('form-control', {
                                    'is-invalid': Boolean(errors.code?.message),
                                })}
                            />
                            <span className="error">{errors.code?.message}</span>
                        </div>
                        <div className="col-12 col-md-4 mb-1">
                            <label className="form-label">
                                Tên portlet <span className="error">*</span>
                            </label>
                            <input
                                {...register('name')}
                                type="text"
                                className={classNames('form-control', {
                                    'is-invalid': Boolean(errors.name?.message),
                                })}
                            />
                            <span className="error">{errors.name?.message}</span>
                        </div>
                        <div className="col-12 col-md-4 mb-1">
                            <label className="form-label">Loại portlet</label>
                            <select {...register('layout_type_id', { valueAsNumber: true })} className="form-select">
                                {selectItem(PortletTypeNames, t, true)}
                            </select>
                        </div>
                        <div className="col-12 col-md-6">
                            <div className="mb-1">
                                <label className="form-label">Mô tả</label>
                                <input type="text" {...register('desc')} className="form-control" />
                            </div>
                            <div className="mb-1 d-flex">
                                <InputSwitch
                                    className="d-flex flex-column me-2"
                                    labelSwitchName="Hoạt động"
                                    labelFieldName="Trạng thái"
                                    name="status_id"
                                    register={register}
                                />
                                <InputSwitch
                                    className="d-flex flex-column"
                                    labelSwitchName="Có"
                                    labelFieldName="Gen ở Client"
                                    name="is_client"
                                    register={register}
                                />
                            </div>
                            <div className="mb-1">
                                <label className="form-label">SQL</label>
                                <SkipForward
                                    size={14}
                                    className={`text-primary cursor-pointer ms-50 ${
                                        isExecutingSql ? 'opacity-50' : ''
                                    }`}
                                    onClick={!isExecutingSql ? executeSql : undefined}
                                />
                                <textarea {...register('sql')} rows={3} className="form-control"></textarea>
                            </div>
                        </div>
                        <div className="col-12 col-md-6 mb-1">
                            <label className="form-label">Kết quả SQL</label>
                            {isExecutingSql ? (
                                <Spinner />
                            ) : (
                                <div
                                    className="sql-result-container"
                                    style={{
                                        maxHeight: '300px',
                                        overflowY: 'auto',
                                        border: '1px solid #ced4da',
                                        borderRadius: '0.357rem',
                                        padding: '0.5rem',
                                    }}
                                >
                                    <JsonView data={sqlResult} shouldExpandNode={allExpanded} style={defaultStyles} />
                                </div>
                            )}
                        </div>
                        <div className="col-12 mb-1">
                            <label className="form-label">
                                Nội dung <span className="error">*</span>
                            </label>
                            <textarea
                                {...register('content')}
                                rows={5}
                                className={classNames('form-control', {
                                    'is-invalid': Boolean(errors.content?.message),
                                })}
                            ></textarea>
                            <span className="error">{errors.content?.message}</span>
                        </div>
                        <div className="col-12">
                            <UpdateButton btnText="Cập nhật" isLoading={isSubmitting} btnClass={['mt-1']} />
                        </div>
                    </div>
                </form>
            </div>
        </div>
    );
}
