import { useState } from 'react';
import { Helmet } from 'react-helmet-async';
import { useTranslation } from 'react-i18next';
import { find } from 'lodash';
import ContentHeader from '../../../components/partials/ContentHeader';
import Spinner from '../../../components/partials/Spinner';
import ModalConfirm from '../../../components/partials/ModalConfirm';
import ListWorkflow from '../components/ListWorkflow';
import ModalWorkflowUpdate from '../components/ModalWorkflowUpdate';
import { useGraphQLQuery } from '../../../hooks/useGraphQLQuery';
import { useGraphQLMutation } from '../../../hooks/useGraphQLMutation';
import { ItemStatus, ItemStatusNames } from 'types/common/Item';
import SearchForm from '../../../components/partials/SearchForm';
import useQueryParams from '../../../hooks/useQueryParams';
import omitBy from 'lodash/omitBy';
import isUndefined from 'lodash/isUndefined';
import { convertConstantToSelectOptions, generateFilters, showToast } from '../../../utils/common';
import { WORKFLOW_CREATE, WORKFLOW_DELETE, WORKFLOW_LIST, WORKFLOW_UPDATE } from '../../../services/WorkflowService';
import Workflow, {
    SearchWorkflow,
    SearchWorkflowParam,
    workflowFilterConfig,
    WorkflowQuery,
    WorkflowTypeNames,
} from '../../../types/Workflow';
import { PAGINATION, QUERY_KEY } from '../../../constants/common';
import { useAppStore } from '../../../stores/appStore';
import { keepPreviousData } from '@tanstack/react-query';
import PaginationTable from '../../../components/partials/PaginationTable';

export default function WorkflowList() {
    const { t } = useTranslation();
    const [showUpdate, setShowUpdate] = useState<boolean>(false);
    const [showDelete, setShowDelete] = useState<boolean>(false);
    const [itemId, setItemId] = useState<number>(0);
    const departmentId = useAppStore((state) => state.departmentId);

    const { queryParams, setQueryParams } = useQueryParams<SearchWorkflowParam>();
    const paramConfig: SearchWorkflowParam = omitBy(
        {
            limit: queryParams.limit ?? PAGINATION.limit,
            page: queryParams.page ?? '1',
            search: queryParams.search,
            status_id: queryParams.status_id,
            workflow_type_id: queryParams.workflow_type_id,
            department_id: departmentId.toString(),
        },
        isUndefined
    );

    const { search, limit, page, ...dataParamConfig } = paramConfig;
    const filters = generateFilters(dataParamConfig, workflowFilterConfig);

    const { data, isLoading, isRefetching, refetch } = useGraphQLQuery<WorkflowQuery, SearchWorkflow>(
        [QUERY_KEY.WORKFLOWS, paramConfig, filters],
        WORKFLOW_LIST,
        {
            page: Number(page),
            limit: Number(limit),
            search,
            filters: filters.length > 0 ? filters : undefined,
            sorts: ['display_order:ASC'],
        },
        '',
        {
            enabled: !!paramConfig.department_id,
            placeholderData: keepPreviousData,
        }
    );
    const workflows = data?.workflows_list.data ?? [];

    const saveMutation = useGraphQLMutation<{}, { id?: number; body: Partial<Workflow> }>(
        itemId > 0 ? WORKFLOW_UPDATE : WORKFLOW_CREATE,
        '',
        {
            onSuccess: () => {
                showToast(true, [t('success.update')]);
                setShowUpdate(false);
                setItemId(0);
                refetch();
            },
            onError: () => showToast(false, [t('error.common')]),
        }
    );

    const deleteMutation = useGraphQLMutation(WORKFLOW_DELETE, '', {
        onSuccess: () => {
            showToast(true, [t('success.delete')]);
            setShowDelete(false);
            setItemId(0);
            refetch();
        },
        onError: () => showToast(false, [t('error.common')]),
    });

    const updateItem = async (body: Workflow) => {
        body.status_id = body.status_id ? ItemStatus.ACTIVE : ItemStatus.PENDING;
        body.department_id = departmentId;
        delete body.id;
        if (itemId === 0) {
            saveMutation.mutate({ body });
        } else {
            saveMutation.mutate({ id: itemId, body });
        }
    };

    const deleteItem = async () => {
        if (itemId) {
            deleteMutation.mutate({ id: itemId });
        }
    };

    const handleEdit = (id: number) => {
        saveMutation.reset();
        setItemId(id);
        setShowUpdate(true);
    };

    const handleDelete = (id: number) => {
        deleteMutation.reset();
        setItemId(id);
        setShowDelete(true);
    };

    const handlePageChange = (_event: React.ChangeEvent<unknown>, page: number) => {
        setQueryParams({ ...queryParams, page: page.toString() });
    };

    const actionMenu = () => {
        saveMutation.reset();
        setShowUpdate(true);
        setItemId(0);
    };

    return (
        <>
            <Helmet>
                <title>Trạng thái tin bài</title>
            </Helmet>
            <ContentHeader
                title="Trạng thái tin bài"
                contextMenu={[
                    {
                        text: 'Thêm trạng thái',
                        to: '',
                        icon: 'PLUS',
                        fnCallBack: { actionMenu },
                    },
                ]}
            />
            <div className="content-body">
                <div className="col-12">
                    <SearchForm
                        fields={[
                            { name: 'search', type: 'text', label: 'Từ khóa', wrapClassName: 'col-md-4 col-12' },
                            {
                                name: 'workflow_type_id',
                                type: 'select',
                                label: 'Loại trạng thái',
                                wrapClassName: 'col-md-4 col-12',
                                options: {
                                    multiple: true,
                                    choices: convertConstantToSelectOptions(WorkflowTypeNames, t, true),
                                },
                            },
                            {
                                name: 'status_id',
                                type: 'select',
                                label: 'Trạng thái',
                                wrapClassName: 'col-md-4 col-12',
                                options: {
                                    multiple: true,
                                    choices: convertConstantToSelectOptions(ItemStatusNames, t, true),
                                },
                            },
                        ]}
                        isLoading={isLoading || isRefetching}
                    />
                    {(isLoading || isRefetching) && <Spinner />}
                    {!isLoading && !isRefetching && (
                        <>
                            <div className="card">
                                <ListWorkflow items={workflows} handleEdit={handleEdit} handleDelete={handleDelete} />
                                <PaginationTable
                                    countItem={data?.workflows_list.totalCount}
                                    totalPage={data?.workflows_list.totalPages}
                                    currentPage={data?.workflows_list.currentPage}
                                    handlePageChange={handlePageChange}
                                />
                            </div>
                            <ModalWorkflowUpdate
                                show={showUpdate}
                                workflow={find(workflows, { id: itemId })}
                                isLoading={saveMutation.isPending}
                                changeShow={(s: boolean) => setShowUpdate(s)}
                                submitAction={updateItem}
                            />
                        </>
                    )}
                    <ModalConfirm
                        show={showDelete}
                        text={t('confirm.delete')}
                        btnDisabled={deleteMutation.isPending}
                        changeShow={(s: boolean) => setShowDelete(s)}
                        submitAction={deleteItem}
                    />
                </div>
            </div>
        </>
    );
}
