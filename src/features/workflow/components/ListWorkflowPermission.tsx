import { ArrowR<PERSON>, Trash2 } from 'react-feather';
import WorkflowPermission from 'types/WorkflowPermission';
import { genTableIndex, getFieldHtml, getFieldInArrayObject } from 'utils/common';
import { ItemStatusNames } from '../../../types/common/Item';
import { useTranslation } from 'react-i18next';
import { Paging } from '../../../types/common';
import Workflow from '../../../types/Workflow';

interface IProps {
    items: WorkflowPermission[];
    paging: Paging;
    workflows: Workflow[];
    handleEdit: (id: number) => void;
    handleDelete: (id: number) => void;
}

export default function ListWorkflowPermission({
    items,
    paging,
    workflows,
    handleEdit,
    handleDelete,
}: Readonly<IProps>) {
    const { t } = useTranslation();

    return (
        <div className="table-responsive">
            <table className="table">
                <thead>
                    <tr>
                        <th className="text-center">STT</th>
                        <th><PERSON><PERSON><PERSON> quyền</th>
                        <th>Tr<PERSON>ng thái truy cập</th>
                        <th>Quy trình</th>
                        <th className="text-center">Trạng thái</th>
                        <th className="thAction1"></th>
                    </tr>
                </thead>
                <tbody>
                    {items.map((item: WorkflowPermission, index: number) => (
                        <tr key={item.id}>
                            <td className="text-center">{genTableIndex(index, paging.limit, paging.current_page)}</td>
                            <td>
                                <span className="text-primary cursor-pointer" onClick={() => handleEdit(item.id!)}>
                                    {item.name}
                                </span>
                            </td>
                            <td>
                                {item.workflow_ids.map((workflowId) => (
                                    <div key={workflowId}>{getFieldInArrayObject(workflows, workflowId)}</div>
                                ))}
                            </td>
                            <td>
                                {item.workflow_transitions.map((transition) => (
                                    <div key={transition.from}>
                                        {transition.from
                                            ? getFieldInArrayObject(workflows, transition.from)
                                            : 'Khởi tạo'}
                                        <ArrowRight size={14} />
                                        {getFieldInArrayObject(workflows, transition.to)}
                                    </div>
                                ))}
                            </td>
                            <td className="text-center">
                                {getFieldHtml(ItemStatusNames, item.status_id as number, t)}
                            </td>
                            <td className="text-center">
                                <button
                                    type="button"
                                    title="Xoá"
                                    className="btn btn-icon btn-sm btn-flat-danger waves-effect"
                                    onClick={() => handleDelete(item.id!)}
                                >
                                    <Trash2 size={14} />
                                </button>
                            </td>
                        </tr>
                    ))}
                </tbody>
            </table>
        </div>
    );
}
