import { yupResolver } from '@hookform/resolvers/yup';
import classNames from 'classnames';
import UpdateButton from 'components/partials/UpdateButton';
import { useEffect, useLayoutEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import WorkflowPermission, { WorkflowTransition } from 'types/WorkflowPermission';
import { showToast, toggleModalOpen } from 'utils/common';
import * as yup from 'yup';
import { ItemStatus, SelectOption } from '../../../types/common/Item';
import InputSwitch from '../../../components/partials/InputSwitch';
import Workflow from 'types/Workflow';
import Select, { MultiValue } from 'react-select';
import { Plus, Trash2 } from 'react-feather';
import { find, includes, map } from 'lodash';

interface IProps {
    show: boolean;
    workflowPermission: WorkflowPermission | undefined;
    workflows: Workflow[];
    isLoading?: boolean;
    changeShow: (s: boolean) => void;
    submitAction: (data: WorkflowPermission) => void;
}

export default function ModalWorkflowPermissionUpdate({
    show,
    workflowPermission,
    workflows,
    isLoading,
    changeShow,
    submitAction,
}: Readonly<IProps>) {
    const { t } = useTranslation();
    useLayoutEffect(() => toggleModalOpen(show), [show]);
    const [workflowValue, setWorkflowValue] = useState<SelectOption[]>([]);
    const [transitions, setTransitions] = useState<WorkflowTransition[]>([]);

    const schema = yup
        .object({
            name: yup.string().required(t('error.required')).trim(),
            workflow_ids: yup.array().min(1, t('error.required')),
        })
        .required();

    const {
        register,
        handleSubmit,
        reset,
        formState: { errors },
        setValue,
    } = useForm<WorkflowPermission>({
        resolver: yupResolver(schema),
    });

    useEffect(() => {
        if (workflowPermission && show) {
            reset({
                ...workflowPermission,
                status_id: workflowPermission.status_id === ItemStatus.ACTIVE,
            });
            setWorkflowValue(
                workflowPermission.workflow_ids.map((item) => ({
                    value: item,
                    label: workflows.find((workflow) => workflow.id === item)?.name ?? '',
                }))
            );
            setTransitions(workflowPermission.workflow_transitions);
        } else {
            reset({
                name: '',
                desc: '',
                status_id: true,
                workflow_ids: [],
                workflow_transitions: [],
            });
            setWorkflowValue([]);
            setTransitions([]);
        }
    }, [workflowPermission, show, reset, workflows]);

    const onChangeWorkflow = (value: MultiValue<SelectOption>) => {
        setWorkflowValue([...value]);
        setValue(
            'workflow_ids',
            value.map((item) => item.value)
        );
    };

    const handleDelete = (index: number) => {
        const items = [...transitions];
        items.splice(index, 1);
        setTransitions(items);
    };

    const onChangeStatus = (value: number, index: number, label: 'from' | 'to') => {
        const items = [...transitions];
        items[index][label] = value;
        setTransitions(items);
    };

    const onSubmit = (data: WorkflowPermission) => {
        if (find(transitions, (item) => item.from === item.to)) {
            showToast(false, ['Trạng thái nguồn và trạng thái đích không được trùng nhau']);
            return;
        }
        data.workflow_transitions = transitions.map((item) => {
            if (item.from === 0) item.from = null;
            return item;
        });
        submitAction(data);
    };

    return (
        <>
            <div
                className={classNames('modal fade text-start modal-primary', { show })}
                style={{ display: show ? 'block' : 'none' }}
            >
                <div className="modal-dialog modal-dialog-centered modal-lg">
                    <div className="modal-content">
                        <div className="modal-header">
                            <h5 className="modal-title">Cập nhật Quyền toà soạn</h5>
                            <button type="button" className="btn-close" onClick={() => changeShow(false)} />
                        </div>
                        <form className="validate-form pt-50" onSubmit={handleSubmit(onSubmit)}>
                            <div className="modal-body">
                                <div className="row">
                                    <div className="col-12 col-sm-6 mb-1">
                                        <label className="form-label">
                                            Tên quyền <span className="error">*</span>
                                        </label>
                                        <input
                                            {...register('name')}
                                            type="text"
                                            className={classNames('form-control', {
                                                'is-invalid': Boolean(errors.name?.message),
                                            })}
                                        />
                                        <span className="error">{errors.name?.message}</span>
                                    </div>
                                    <InputSwitch
                                        classNameWrap="col-12 col-sm-6 mb-1"
                                        className="d-flex flex-column"
                                        labelSwitchName="Hoạt động"
                                        labelFieldName="Trạng thái"
                                        name="status_id"
                                        register={register}
                                    />
                                    <div className="col-12 mb-1">
                                        <label className="form-label">Mô tả</label>
                                        <input type="text" {...register('desc')} className="form-control" />
                                    </div>
                                    <div className="col-12 mb-1">
                                        <label className="form-label">
                                            Trạng thái tin bài <span className="error">*</span>
                                        </label>
                                        <Select
                                            options={workflows.map((item) => ({
                                                value: item.id!,
                                                label: item.name,
                                            }))}
                                            isMulti={true}
                                            onChange={onChangeWorkflow}
                                            value={workflowValue}
                                            isClearable
                                            placeholder="Chọn..."
                                        />
                                        <span className="error">{errors.workflow_ids?.message}</span>
                                    </div>
                                    <div className="col-12 mb-1">
                                        <label className="form-label">
                                            Quy trình toà soạn <span className="error">*</span>{' '}
                                            <Plus
                                                size={14}
                                                className="text-primary cursor-pointer"
                                                onClick={() => setTransitions([...transitions, { from: 0, to: 0 }])}
                                            />
                                        </label>
                                        <div className="table-responsive">
                                            <table className="table">
                                                <thead>
                                                    <tr>
                                                        <th className="text-center">STT</th>
                                                        <th className="text-center">Trạng thái nguồn</th>
                                                        <th className="text-center">Trạng thái đích</th>
                                                        <th className="thAction1"></th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    {transitions.map((item, index) => (
                                                        <tr key={index}>
                                                            <td className="text-center">{index + 1}</td>
                                                            <td className="text-center">
                                                                <select
                                                                    className="form-select"
                                                                    value={item.from ?? 0}
                                                                    onChange={(e) =>
                                                                        onChangeStatus(+e.target.value, index, 'from')
                                                                    }
                                                                >
                                                                    <option value={0}>Khởi tạo</option>
                                                                    {workflows
                                                                        .filter((workflow) =>
                                                                            includes(
                                                                                map(workflowValue, 'value'),
                                                                                workflow.id
                                                                            )
                                                                        )
                                                                        .map((workflow) => (
                                                                            <option
                                                                                key={workflow.id}
                                                                                value={workflow.id}
                                                                            >
                                                                                {workflow.name}
                                                                            </option>
                                                                        ))}
                                                                </select>
                                                            </td>
                                                            <td className="text-center">
                                                                <select
                                                                    className="form-select"
                                                                    value={item.to}
                                                                    onChange={(e) =>
                                                                        onChangeStatus(+e.target.value, index, 'to')
                                                                    }
                                                                >
                                                                    <option value={0}>Khởi tạo</option>
                                                                    {workflows
                                                                        .filter((workflow) =>
                                                                            includes(
                                                                                map(workflowValue, 'value'),
                                                                                workflow.id
                                                                            )
                                                                        )
                                                                        .map((workflow) => (
                                                                            <option
                                                                                key={workflow.id}
                                                                                value={workflow.id}
                                                                            >
                                                                                {workflow.name}
                                                                            </option>
                                                                        ))}
                                                                </select>
                                                            </td>
                                                            <td className="text-center">
                                                                <button
                                                                    type="button"
                                                                    title="Xoá"
                                                                    className="btn btn-icon btn-sm btn-flat-danger waves-effect"
                                                                    onClick={() => handleDelete(index)}
                                                                >
                                                                    <Trash2 size={14} />
                                                                </button>
                                                            </td>
                                                        </tr>
                                                    ))}
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div className="modal-footer">
                                <UpdateButton btnText="Cập nhật" isLoading={isLoading} hasDivWrap={false} />
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            {show && <div className="modal-backdrop fade show" />}
        </>
    );
}
