import { Trash2 } from 'react-feather';
import Workflow, { WorkflowType, WorkflowTypeNames } from 'types/Workflow';
import { getFieldHtml } from 'utils/common';
import { ItemStatusNames } from '../../../types/common/Item';
import { useTranslation } from 'react-i18next';
import { DEFAULT_COLOR } from '../../../constants/common';

interface IProps {
    items: Workflow[];
    handleEdit: (id: number) => void;
    handleDelete: (id: number) => void;
}

export default function ListWorkflow({ items, handleEdit, handleDelete }: Readonly<IProps>) {
    const { t } = useTranslation();

    return (
        <div className="table-responsive">
            <table className="table">
                <thead>
                    <tr>
                        <th className="text-center">STT</th>
                        <th>Trạng thái tin bài</th>
                        <th className="text-center">Màu</th>
                        <th className="text-center"><PERSON><PERSON>i</th>
                        <th className="text-center">Trạng thái</th>
                        <th className="thAction1"></th>
                    </tr>
                </thead>
                <tbody>
                    {items.map((item: Workflow) => (
                        <tr key={item.id}>
                            <td className="text-center">{item.display_order}</td>
                            <td>
                                <span className="text-primary cursor-pointer" onClick={() => handleEdit(item.id!)}>
                                    {item.name}
                                </span>
                            </td>
                            <td className="text-center">
                                <div
                                    className="avatar"
                                    style={{
                                        color: item.desc ?? DEFAULT_COLOR,
                                        backgroundColor: item.desc ?? DEFAULT_COLOR,
                                    }}
                                >
                                    <div className="avatar-content wh-14"></div>
                                </div>
                            </td>
                            <td className="text-center">{getFieldHtml(WorkflowTypeNames, item.workflow_type_id, t)}</td>
                            <td className="text-center">
                                {getFieldHtml(ItemStatusNames, item.status_id as number, t)}
                            </td>
                            <td className="text-center">
                                {item.workflow_type_id === WorkflowType.EDITING && (
                                    <button
                                        type="button"
                                        title="Xoá"
                                        className="btn btn-icon btn-sm btn-flat-danger waves-effect"
                                        onClick={() => handleDelete(item.id!)}
                                    >
                                        <Trash2 size={14} />
                                    </button>
                                )}
                            </td>
                        </tr>
                    ))}
                </tbody>
            </table>
        </div>
    );
}
