import React from 'react';
import { MessageCircle } from 'react-feather';
import { IHighlight } from '../../../types/PdfHighlighter';

interface HighlightSidebarProps {
    highlights: IHighlight[];
    onHighlightClick: (highlight: IHighlight) => void;
    onDeleteHighlight?: (highlightId: string) => void;
}

const HighlightSidebar = ({ highlights, onHighlightClick, onDeleteHighlight }: Readonly<HighlightSidebarProps>) => {
    const scrollToHighlight = (highlight: IHighlight) => {
        onHighlightClick(highlight);
    };

    const handleDeleteHighlight = (highlightId: string) => {
        if (window.confirm('Bạn có chắc chắn muốn xóa highlight này?')) {
            onDeleteHighlight?.(highlightId);
        }
    };

    return (
        <div className="w-[25%] bg-white border-l border-gray-200 h-full overflow-y-auto">
            <div className="px-4 py-3 border-b border-gray-200">
                <h3 className="text-lg font-semibold text-gray-800 flex items-center gap-2">
                    <MessageCircle size={20} />
                    Danh sách Highlights ({highlights.length})
                </h3>
            </div>

            <div className="px-4 py-3">
                {highlights.length === 0 ? (
                    <div className="text-center text-gray-500 py-8">
                        <MessageCircle size={48} className="mx-auto mb-4 text-gray-300" />
                        <p>Chưa có highlight</p>
                        <p className="text-sm mt-2">Chọn văn bản trong PDF để tạo highlight đầu tiên</p>
                    </div>
                ) : (
                    <div className="space-y-4">
                        {highlights.map((highlight, index) => (
                            <div
                                key={index}
                                className="border border-gray-200 rounded-lg p-1 hover:shadow-md transition-shadow cursor-pointer"
                            >
                                <div className="flex items-start justify-between mb-2">
                                    <div className="flex items-center gap-2">
                                        <span className="text-sm font-medium text-gray-600">
                                            Highlight #{index + 1}
                                        </span>
                                    </div>
                                </div>

                                <div onClick={() => scrollToHighlight(highlight)} className="mb-2">
                                    <p className="text-sm text-gray-700 mb-2">{highlight.comment?.text}</p>
                                    {highlight.content?.text && (
                                        <div className="bg-yellow-50 border-l-4 border-yellow-400 p-2 rounded">
                                            <p className="text-xs text-gray-600 italic">
                                                "
                                                {highlight.content.text.length > 100
                                                    ? highlight.content.text.substring(0, 100) + '...'
                                                    : highlight.content.text}
                                                "
                                            </p>
                                        </div>
                                    )}
                                    {highlight.content?.image && (
                                        <div className="bg-blue-50 border-l-4 border-blue-400 p-2 rounded">
                                            <p className="text-xs text-gray-600">📷 Highlight vùng chọn</p>
                                        </div>
                                    )}
                                </div>
                                <div className="text-xs text-gray-400">Trang {highlight.position?.pageNumber}</div>
                            </div>
                        ))}
                    </div>
                )}
            </div>
        </div>
    );
};

export { HighlightSidebar };
