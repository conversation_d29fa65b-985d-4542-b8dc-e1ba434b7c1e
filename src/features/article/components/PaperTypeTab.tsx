import React, { useMemo } from 'react';
import { PaperTypeNames } from '../pages/PaperPagePlanEditor';
import Article from '../../../types/Article';
import classNames from 'classnames';
import { normalizeVietnameseText } from '../../../utils/common';

interface IProps {
    activeTab: number;
    onTabChange: (tabId: number) => void;
    articles: Article[];
    onSearchChange: (searchValue: string) => void;
    searchValue: string;
    onAddArticle: (article: Article) => void;
}

export default function PaperTypeTab({
    activeTab,
    onTabChange,
    articles,
    onSearchChange,
    searchValue,
    onAddArticle,
}: Readonly<IProps>) {
    const filteredArticles = useMemo(() => {
        // First filter by tab type
        let tabFilteredArticles =
            activeTab === PaperTypeNames[0].id ? articles.filter((article) => !article.issue_id) : articles;

        // Then filter by search value if provided
        if (searchValue.trim()) {
            const normalizedSearchValue = normalizeVietnameseText(searchValue.trim());
            tabFilteredArticles = tabFilteredArticles.filter((article) => {
                const normalizedTitle = normalizeVietnameseText(article.title || '');
                return normalizedTitle.includes(normalizedSearchValue);
            });
        }

        return tabFilteredArticles;
    }, [activeTab, articles, searchValue]);
    return (
        <div className="nav-align-top nav-tabs-shadow">
            {/* Search Input */}
            <div className="mb-2">
                <input
                    type="text"
                    className="form-control"
                    placeholder="Tìm kiếm tiêu đề"
                    value={searchValue}
                    onChange={(e) => onSearchChange(e.target.value)}
                />
            </div>

            <ul className="nav nav-tabs">
                {PaperTypeNames.map((item) => (
                    <li className="nav-item" key={item.id}>
                        <button
                            type="button"
                            className={`nav-link waves-effect ${activeTab === item.id ? 'active' : ''}`}
                            role="tab"
                            data-bs-toggle="tab"
                            data-bs-target={`#navs-top-paper-type-${item.id}`}
                            aria-controls={`navs-top-paper-type-${item.id}`}
                            aria-selected={activeTab === item.id ? 'true' : 'false'}
                            tabIndex={-1}
                            onClick={() => onTabChange(item.id)}
                        >
                            {item.name}
                        </button>
                    </li>
                ))}
            </ul>
            <div className="tab-content">
                {PaperTypeNames.map((item) => (
                    <div
                        className={classNames('tab-pane fade', { 'show active': activeTab === item.id })}
                        id={`navs-top-paper-type-${item.id}`}
                        role="tabpanel"
                        key={item.id}
                    >
                        <div className="article-list overflow-auto">
                            {filteredArticles.map((article) => (
                                <div key={article.id} className="border border-gray-300 rounded-lg mb-1 p-1 bg-white">
                                    <div className="flex justify-end mb-2">
                                        <button
                                            className="btn btn-primary text-white text-xs rounded"
                                            style={{ padding: '8px' }}
                                            onClick={() => onAddArticle(article)}
                                        >
                                            Thêm
                                        </button>
                                    </div>
                                    <div className="flex items-start gap-3">
                                        <div className="flex-shrink-0">
                                            {article.avatar1?.file_url ? (
                                                <img
                                                    src={article.avatar1.file_url}
                                                    alt="Article avatar"
                                                    className="w-16 h-16 object-cover rounded border"
                                                />
                                            ) : (
                                                <div className="w-16 h-16 bg-gray-200 rounded border flex items-center justify-center">
                                                    <svg
                                                        className="w-8 h-8 text-gray-400"
                                                        fill="currentColor"
                                                        viewBox="0 0 20 20"
                                                    >
                                                        <path
                                                            fillRule="evenodd"
                                                            d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z"
                                                            clipRule="evenodd"
                                                        />
                                                    </svg>
                                                </div>
                                            )}
                                        </div>

                                        <div className="flex-1 min-w-0">
                                            <h4 className="text-sm font-medium text-gray-900 mb-1 line-clamp-2">
                                                {article.title}
                                            </h4>
                                            <p className="text-xs text-gray-600 mb-1">
                                                <span className="font-medium">Trạng thái bài viết:</span>
                                                {article.workflow?.name || 'Chưa có trạng thái'}
                                            </p>
                                            <p className="text-xs text-gray-600 mb-1">
                                                <span className="font-medium">Trạng thái dàn trang:</span> Đang dàn
                                                trang
                                            </p>
                                            <p className="text-xs text-gray-600 mb-1">
                                                <span className="font-medium">Chuyên mục:</span>{' '}
                                                {article.articleCategories && article.articleCategories.length > 0
                                                    ? article.articleCategories
                                                          .map((cat) => cat.category?.name)
                                                          .join(', ')
                                                    : 'Chưa có chuyên mục'}
                                            </p>
                                            <p className="text-xs text-gray-600">
                                                <span className="font-medium">Tác giả:</span>{' '}
                                                {article.pseudonym?.name || 'Chưa có tác giả'}
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            ))}

                            {filteredArticles.length === 0 && articles.length > 0 && (
                                <div className="text-center text-gray-500 py-8">Không tìm thấy bài viết nào</div>
                            )}

                            {articles.length === 0 && (
                                <div className="text-center text-gray-500 py-8">Không có bài viết nào</div>
                            )}
                        </div>
                    </div>
                ))}
            </div>
        </div>
    );
}
