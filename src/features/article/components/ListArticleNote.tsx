import { isEmpty } from 'lodash';
import { useState } from 'react';
import { Plus } from 'react-feather';
import ArticleNote from 'types/ArticleNote';
import { FORMAT_DATE, formatDateTime } from 'utils/date';
import { useAuthStore } from '../../../stores/authStore';

interface IProps {
    items: ArticleNote[];
    handleAdd: (note: string) => void;
}

export default function ListArticleNote({ items, handleAdd }: Readonly<IProps>) {
    const [note, setNote] = useState('');
    const currentUser = useAuthStore((state) => state.user);

    return (
        <div className="card-body p-[.75rem]">
            <div className="mb-1 mt-1">
                <textarea
                    className="form-control"
                    rows={4}
                    placeholder="Nhập nội dung trao đổi"
                    value={note}
                    onChange={(e) => setNote(e.target.value)}
                />
            </div>
            <div className="mb-1 d-flex justify-content-end">
                <button
                    type="button"
                    className="btn btn-sm waves-effect waves-float waves-light !bg-[#FCF3F5FF] rounded-[4px] !text-[#A42D49FF]"
                    disabled={!note}
                    onClick={() => {
                        handleAdd(note);
                        setNote('');
                    }}
                >
                    <Plus size={14} className="me-25 align-bottom inline-block" />
                    <span>Thêm</span>
                </button>
            </div>
            {!isEmpty(items) && (
                <div className="mb-1">
                    <table className="table">
                        <thead>
                            <tr>
                                <th>Nội dung trao đổi</th>
                                <th className="text-center">Thời gian</th>
                            </tr>
                        </thead>
                        <tbody>
                            {items.map((item, index) => (
                                <tr key={index}>
                                    <td>
                                        <span>
                                            {!item.id ? currentUser?.full_name : item.createdByUser?.full_name}:
                                        </span>
                                        <span className="fw-bold ms-1">{item.content}</span>
                                    </td>
                                    <td className="text-center">
                                        {item.created_at &&
                                            formatDateTime(item.created_at, FORMAT_DATE.SHOW_DATE_MINUTE)}
                                    </td>
                                </tr>
                            ))}
                        </tbody>
                    </table>
                </div>
            )}
        </div>
    );
}
