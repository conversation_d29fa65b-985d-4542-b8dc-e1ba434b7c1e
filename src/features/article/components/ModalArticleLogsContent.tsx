import Spinner from 'components/partials/Spinner';
import { LIMIT_MAX, PAGE_NUMBER_DEFAULT, QUERY_KEY } from 'constants/common';
import { useGraphQLQuery } from 'hooks/useGraphQLQuery';
import { isEmpty } from 'lodash';
import { useMemo, useState } from 'react';
import { Eye } from 'react-feather';
import { ARTICLE_LOGS_LIST } from 'services/ArticleService';
import { USER_LIST } from 'services/UserService';
import ArticleLog, { ArticleLogAction, ArticleLogsQueryRes } from 'types/ArticleLog';
import { ItemStatus } from 'types/common/Item';
import { UserListQuery } from 'types/User';
import { FORMAT_DATE, formatDateTime } from 'utils/date';
import ModalViewArticleLog from './ModalViewArticleLog';

type Props = {
    articleId: number | null;
};

const ModalArticleLogsContent = ({ articleId }: Props) => {
    const [isShowLogDetail, setIsShowLogDetail] = useState(false);
    const [selectedArticleLog, setSelectedArticleLog] = useState<ArticleLog | undefined>();

    const articleRelationFilter = useMemo(() => [`article_id:=(${articleId})`], [articleId]);

    const { data: articleLogsData, isLoading } = useGraphQLQuery<ArticleLogsQueryRes>(
        [QUERY_KEY.ARTICLE_LOGS, { page: PAGE_NUMBER_DEFAULT, limit: LIMIT_MAX, filters: articleRelationFilter }],
        ARTICLE_LOGS_LIST,
        {
            page: PAGE_NUMBER_DEFAULT,
            limit: LIMIT_MAX,
            filters: articleRelationFilter,
        },
        '',
        {
            enabled: Boolean(articleId),
        }
    );

    const articleLogs = useMemo(() => articleLogsData?.article_logs_list.data ?? [], [articleLogsData]);

    const { data: userData } = useGraphQLQuery<UserListQuery>([QUERY_KEY.USERS], USER_LIST, {
        page: 1,
        limit: LIMIT_MAX,
        search: '',
        filters: [`status_id:=(${ItemStatus.ACTIVE})`],
    });

    const users = useMemo(() => userData?.users_list.data ?? [], [userData]);

    const parseContent = (log: ArticleLog) => JSON.parse(log.content);

    const getUser = (log: ArticleLog) => {
        const userId = parseContent(log).user_id;

        return users.find((u) => u.id === userId)?.full_name;
    };

    const getActionText = (log: ArticleLog) => {
        const action = parseContent(log).action;

        switch (action) {
            case ArticleLogAction.ARTICLE_UPDATED:
                return 'Chỉnh sửa bài viết';

            case ArticleLogAction.ARTICLE_CREATED:
                return 'Tạo mới bài viết';

            default:
                return '';
        }
    };

    const onShowLogDetail = (log: ArticleLog) => {
        setIsShowLogDetail(true);
        setSelectedArticleLog(log);
    };

    const onCloseLogDetail = (isOpen: boolean) => {
        setIsShowLogDetail(isOpen);
        setSelectedArticleLog(undefined);
    };

    if (isLoading) return <Spinner />;

    return (
        <>
            <div className="card-body p-[.75rem]">
                <div className="mt-1 mb-1">
                    {isEmpty(articleLogs) ? (
                        <div className="text-center">Bài viết này không có lịch sử thay đổi.</div>
                    ) : (
                        <div className="table-responsive">
                            <table
                                className="table"
                                style={{
                                    tableLayout: 'fixed',
                                }}
                            >
                                <thead>
                                    <tr>
                                        <th className="text-center !px-2" style={{ width: '128px' }}>
                                            Người thực hiện
                                        </th>
                                        <th className="text-center !px-2" style={{ width: '128px' }}>
                                            Loại
                                        </th>
                                        <th className="text-center !px-2" style={{ width: '132px' }}>
                                            Thời gian
                                        </th>
                                        <th className="text-center !px-2" style={{ width: '48px' }}>
                                            Xem
                                        </th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {articleLogs.map((item) => (
                                        <tr key={item.id}>
                                            <td className="text-center !px-2 truncate">{getUser(item)}</td>
                                            <td className="text-center !px-2 truncate">{getActionText(item)}</td>
                                            <td className="text-center !px-2 truncate">
                                                {formatDateTime(
                                                    item.created_at?.toString() ?? '',
                                                    FORMAT_DATE.SHOW_DATE_MINUTE
                                                )}
                                            </td>
                                            <td className="text-center !px-2">
                                                {parseContent(item).action === ArticleLogAction.ARTICLE_UPDATED && (
                                                    <button
                                                        type="button"
                                                        title="Xem"
                                                        className="btn btn-icon btn-sm btn-flat-danger waves-effect"
                                                        onClick={() => onShowLogDetail(item)}
                                                    >
                                                        <Eye size={14} />
                                                    </button>
                                                )}
                                            </td>
                                        </tr>
                                    ))}
                                </tbody>
                            </table>
                        </div>
                    )}
                </div>
            </div>
            <ModalViewArticleLog articleLog={selectedArticleLog} show={isShowLogDetail} changeShow={onCloseLogDetail} />
        </>
    );
};

export default ModalArticleLogsContent;
