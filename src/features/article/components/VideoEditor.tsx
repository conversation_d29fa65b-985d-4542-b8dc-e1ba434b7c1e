import React, { useState, useRef, useCallback, useEffect, SetStateAction } from 'react';
import { Upload, Play, Pause, Camera, X } from 'react-feather';
import FileService from '../../../services/FileService';
import { showToast } from '../../../utils/common';
import classNames from 'classnames';
import FrameCarousel from './FrameCarousel';
import { VIDEO_MAX_SIZE } from '../../../constants/common';
import ModalContent from '../../../components/partials/ModalContent';
import ModalEditImage from '../../../components/partials/ModalEditImage';
import { TypeMedia } from '../../../types/common/Item';

interface CapturedFrame {
    id: string;
    dataUrl: string;
    timestamp: number;
}

interface VideoEditorProps {
    initialData: string;
    onEditorChange: (value: string) => void;
    onSubmitMedia: (urlMedia: string, id?: number) => void;
    departmentId: number;
    currentAvatarUrl?: string;
    hanleResetAvatar1: () => void;
    setTypeModalMedia: (value: SetStateAction<TypeMedia | undefined>) => void;
}

// Helper function to extract video URL from HTML content
export const extractVideoUrl = (content: string): string => {
    if (!content) return '';

    // Trim whitespace
    content = content.trim();

    // If it's already a plain URL, return it
    if (content.startsWith('http://') || content.startsWith('https://')) {
        return content;
    }

    // Extract URL from HTML tags (p, div, etc.)
    const urlRegex = /(https?:\/\/[^\s<>"']+)/i;
    const match = content.match(urlRegex);

    return match ? match[1] : '';
};

// Helper function to check if content is a valid video URL
const isValidVideoUrl = (content: string): boolean => {
    const url = extractVideoUrl(content);

    if (!url) return false;

    // Check if it's a valid URL
    try {
        const urlObj = new URL(url);
        // Must be http or https
        if (!['http:', 'https:'].includes(urlObj.protocol)) {
            return false;
        }
    } catch {
        return false;
    }

    // Check if it has video file extension
    const videoExtensions = ['.mp4', '.webm', '.ogg', '.avi', '.mov', '.wmv', '.flv', '.mkv', '.m4v', '.3gp'];
    const lowerUrl = url.toLowerCase();

    // Check file extensions (at end of URL or before query parameters)
    return videoExtensions.some(
        (ext) => lowerUrl.endsWith(ext) || lowerUrl.includes(ext + '?') || lowerUrl.includes(ext + '#')
    );
};

export default function VideoEditor({
    initialData,
    onEditorChange,
    onSubmitMedia,
    departmentId,
    currentAvatarUrl,
    hanleResetAvatar1,
    setTypeModalMedia,
}: VideoEditorProps) {
    const [videoUrl, setVideoUrl] = useState<string>(() =>
        isValidVideoUrl(initialData) ? extractVideoUrl(initialData) : ''
    );
    const [isPlaying, setIsPlaying] = useState(false);
    const [capturedFrames, setCapturedFrames] = useState<CapturedFrame[]>([]);
    const [selectedFrameId, setSelectedFrameId] = useState<string>('');
    const [isVideoReady, setIsVideoReady] = useState(false);
    const [showModalMedia, setShowModalMedia] = useState(false);

    const videoRef = useRef<HTMLVideoElement>(null);
    const canvasRef = useRef<HTMLCanvasElement>(null);

    // Update video URL when initialData changes
    useEffect(() => {
        const newVideoUrl = isValidVideoUrl(initialData) ? extractVideoUrl(initialData) : '';

        if (newVideoUrl !== videoUrl) {
            setVideoUrl(newVideoUrl);
            setIsVideoReady(false);

            // Clear captured frames when switching to different video or clearing video
            if (!newVideoUrl || (videoUrl && newVideoUrl !== videoUrl)) {
                setCapturedFrames([]);
                setSelectedFrameId('');
            }
        }
    }, [initialData, videoUrl]);

    // Track if we're currently uploading a frame to avoid resetting selectedFrameId during upload
    const [isUploadingFrame, setIsUploadingFrame] = useState(false);

    // Sync selectedFrameId when currentAvatarUrl changes (e.g., avatar1 is cleared)
    // Only reset if we're not currently uploading a frame (to avoid reset during upload process)
    useEffect(() => {
        if (!currentAvatarUrl && selectedFrameId && !isUploadingFrame) {
            setSelectedFrameId('');
        }
    }, [currentAvatarUrl, selectedFrameId, isUploadingFrame]);

    const handleOpenVideoModal = useCallback(() => {
        setTypeModalMedia('video');
        setShowModalMedia(true);
    }, [setTypeModalMedia]);

    const handleVideoSelect = useCallback(
        (url: string, id?: number) => {
            setVideoUrl(url);
            setIsVideoReady(false); // Reset video ready state for new video
            onEditorChange(url);
            setShowModalMedia(false);
            setTypeModalMedia(undefined);

            // Clear captured frames when switching to different video
            setCapturedFrames([]);
            setSelectedFrameId('');

            showToast(true, ['Chọn video thành công']);
        },
        [onEditorChange, setTypeModalMedia]
    );

    const handlePlayPause = useCallback(() => {
        if (!videoRef.current) return;

        if (isPlaying) {
            videoRef.current.pause();
        } else {
            videoRef.current.play();
        }
        setIsPlaying(!isPlaying);
    }, [isPlaying]);

    const captureFrame = useCallback(() => {
        if (!videoRef.current || !canvasRef.current || !isVideoReady) return;

        const video = videoRef.current;
        const canvas = canvasRef.current;
        const ctx = canvas.getContext('2d');

        if (!ctx) return;

        try {
            // Set canvas dimensions to match video
            canvas.width = video.videoWidth;
            canvas.height = video.videoHeight;

            // Draw current video frame to canvas
            ctx.drawImage(video, 0, 0, canvas.width, canvas.height);

            // Convert canvas to data URL
            const dataUrl = canvas.toDataURL('image/jpeg', 0.8);

            const newFrame: CapturedFrame = {
                id: Date.now().toString(),
                dataUrl,
                timestamp: video.currentTime,
            };

            setCapturedFrames((prev) => [...prev, newFrame]);
            showToast(true, ['Đã chụp khung hình']);
        } catch (error) {
            if (error instanceof DOMException && error.name === 'SecurityError') {
                showToast(false, ['Không thể chụp ảnh do hạn chế bảo mật. Vui lòng thử upload video khác.']);
            } else {
                showToast(false, ['Không thể chụp khung hình. Vui lòng thử lại.']);
            }
        }
    }, [isVideoReady]);

    const selectFrame = useCallback(
        (frameId: string) => {
            if (selectedFrameId === frameId) {
                // Deselect if clicking the same frame
                setSelectedFrameId('');
                return;
            }

            setSelectedFrameId(frameId);
            const selectedFrame = capturedFrames.find((frame) => frame.id === frameId);
            if (selectedFrame) {
                // Set uploading flag to prevent reset during upload
                setIsUploadingFrame(true);

                // Convert data URL to blob and upload as avatar
                fetch(selectedFrame.dataUrl)
                    .then((res) => res.blob())
                    .then((blob) => {
                        const file = new File([blob], `frame-${frameId}.jpg`, { type: 'image/jpeg' });
                        return FileService.upload(file, 'image', departmentId || 1);
                    })
                    .then((response) => {
                        if (response?.upload?.url) {
                            onSubmitMedia(response.upload.url, response.upload.id);
                            showToast(true, ['Đã chọn khung hình làm avatar']);
                        }
                    })
                    .catch((error) => {
                        showToast(false, ['Upload khung hình thất bại']);
                        setSelectedFrameId(''); // Reset selection on error
                    })
                    .finally(() => {
                        // Clear uploading flag after upload completes (success or error)
                        setIsUploadingFrame(false);
                    });
            }
        },
        [capturedFrames, departmentId, onSubmitMedia, selectedFrameId]
    );

    const removeFrame = useCallback(
        (frameId: string) => {
            setCapturedFrames((prev) => prev.filter((frame) => frame.id !== frameId));
            if (selectedFrameId === frameId) {
                setSelectedFrameId('');
                hanleResetAvatar1();
            }
        },
        [selectedFrameId, hanleResetAvatar1]
    );

    const formatTime = useCallback((seconds: number) => {
        const mins = Math.floor(seconds / 60);
        const secs = Math.floor(seconds % 60);
        return `${mins}:${secs.toString().padStart(2, '0')}`;
    }, []);

    return (
        <div className="video-editor h-[calc(100vh-286px)] flex flex-col">
            {/* Upload Section */}
            {!videoUrl && (
                <div className="flex-1 flex items-center justify-center border-2 border-dashed border-gray-300 rounded-lg">
                    <div className="text-center">
                        <Upload size={48} className="mx-auto mb-4 text-gray-400" />
                        <h3 className="text-lg font-medium text-gray-900 mb-2">Chọn Video</h3>
                        <p className="text-sm text-gray-500 mb-4">Chọn video từ thư viện hoặc upload video mới</p>
                        <button type="button" onClick={handleOpenVideoModal} className="btn btn-primary">
                            Chọn Video
                        </button>
                    </div>
                </div>
            )}

            {/* Video Player Section */}
            {videoUrl && (
                <div className="flex-1 flex flex-col">
                    <div className="relative bg-black rounded-lg overflow-hidden mb-4">
                        <video
                            ref={videoRef}
                            src={videoUrl}
                            className="w-full h-[400px] object-contain"
                            onPlay={() => setIsPlaying(true)}
                            onPause={() => setIsPlaying(false)}
                            onLoadedMetadata={() => {
                                setIsVideoReady(true);
                            }}
                            onCanPlay={() => {
                                setIsVideoReady(true);
                            }}
                            controls={false}
                            crossOrigin="anonymous"
                            onError={(e) => {
                                setIsVideoReady(false);
                                showToast(false, ['Không thể tải video']);
                            }}
                        />

                        {/* Custom Controls */}
                        <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/70 to-transparent p-4">
                            <div className="flex items-center gap-4">
                                <button
                                    type="button"
                                    onClick={handlePlayPause}
                                    className="text-white hover:text-gray-300"
                                >
                                    {isPlaying ? <Pause size={24} /> : <Play size={24} />}
                                </button>

                                <button
                                    type="button"
                                    onClick={captureFrame}
                                    disabled={!isVideoReady}
                                    className={classNames(
                                        'flex items-center gap-2 px-3 py-1 rounded text-sm font-medium transition-colors',
                                        {
                                            'bg-red-600 text-white hover:bg-red-700': isVideoReady,
                                            'bg-gray-600 text-gray-300 cursor-not-allowed': !isVideoReady,
                                        }
                                    )}
                                    title={isVideoReady ? 'Chụp khung hình hiện tại' : 'Đang tải video...'}
                                >
                                    <Camera size={16} />
                                    {isVideoReady ? 'Chụp ảnh' : 'Đang tải...'}
                                </button>

                                <div className="flex-1" />

                                <button
                                    type="button"
                                    onClick={() => {
                                        setVideoUrl('');
                                        onEditorChange('');
                                        setCapturedFrames([]);
                                        setSelectedFrameId('');
                                        setIsVideoReady(false);
                                        hanleResetAvatar1();
                                        setIsPlaying(false);
                                    }}
                                    className="text-white hover:text-gray-300"
                                >
                                    <X size={20} />
                                </button>
                            </div>
                        </div>
                    </div>

                    {/* Instructions */}
                    <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 mb-4">
                        <div className="flex items-start gap-2">
                            <div className="text-blue-600 mt-0.5">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-6h2v6zm0-8h-2V7h2v2z" />
                                </svg>
                            </div>
                            <div className="text-sm text-blue-800">
                                <p className="font-medium mb-1">Hướng dẫn sử dụng:</p>
                                <ul className="text-xs space-y-1">
                                    <li>• Có thể chụp ảnh bất kỳ lúc nào (kể cả khi video đang phát)</li>
                                    <li>• Nhấn nút "Chụp ảnh" để lưu khung hình hiện tại</li>
                                    <li>• Click vào khung hình trong danh sách để chọn làm avatar</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    {/* Captured Frames Carousel */}
                    <FrameCarousel
                        frames={capturedFrames}
                        selectedFrameId={selectedFrameId}
                        onSelectFrame={selectFrame}
                        onRemoveFrame={removeFrame}
                        formatTime={formatTime}
                    />
                </div>
            )}

            {/* Hidden canvas for frame capture */}
            <canvas ref={canvasRef} className="hidden" />

            {/* Video Selection Modal */}
            <ModalContent
                show={showModalMedia}
                changeShow={(s: boolean) => setShowModalMedia(s)}
                title="Chọn video từ thư viện"
                content={
                    <ModalEditImage
                        onSubmit={handleVideoSelect}
                        isArticle={false}
                        show={showModalMedia}
                        fileType="video"
                    />
                }
            />
        </div>
    );
}
