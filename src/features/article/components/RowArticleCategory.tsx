import classNames from 'classnames';
import find from 'lodash/find';
import includes from 'lodash/includes';
import isEmpty from 'lodash/isEmpty';
import { ChevronDown, ChevronRight } from 'react-feather';
import { Fragment } from 'react/jsx-runtime';
import ArticleCategory from 'types/ArticleCategory';
import ArticleCategoryMap from 'types/ArticleCategoryMap';
import { getFieldInArrayObject } from 'utils/common';

interface IProps {
    item: ArticleCategory;
    articleCategoryMaps: ArticleCategoryMap[];
    level: number;
    expandCategoryIds: number[];
    toggleCategory: (categoryId: number, checked: boolean) => void;
    onChangeCategoryPriority?: (priority: number, categoryId: number) => void;
    onExpandCategory?: (categoryId: number, checked: boolean) => void;
    isHiddenCategoryPriority?: boolean;
}
export default function RowArticleCategory({
    item,
    articleCategoryMaps,
    level,
    expandCategoryIds,
    toggleCategory,
    onChangeCategoryPriority,
    onExpandCategory,
    isHiddenCategoryPriority,
}: Readonly<IProps>) {
    return (
        <tr
            className={classNames({
                hidden: level > 1 && !includes(expandCategoryIds, item.category.parent_id!),
                'table-row': level === 1 || includes(expandCategoryIds, item.category.parent_id!),
            })}
        >
            <td className={classNames('py-25', { '!pl-16': level === 2 })}>
                {level === 1 && !isEmpty(item.category.children) && onExpandCategory && (
                    <Fragment>
                        {includes(expandCategoryIds, item.id) ? (
                            <ChevronDown
                                size={14}
                                className="me-25 inline-block cursor-pointer"
                                onClick={() => onExpandCategory(item.id!, false)}
                            />
                        ) : (
                            <ChevronRight
                                size={14}
                                className="me-25 inline-block cursor-pointer"
                                onClick={() => onExpandCategory(item.id!, true)}
                            />
                        )}
                    </Fragment>
                )}
                <div
                    className={classNames('form-check form-check-danger inline-block', {
                        'ms-125': level === 1 && isEmpty(item.category.children),
                    })}
                >
                    <input
                        checked={
                            !!find(articleCategoryMaps, {
                                saArticleCateId: item.id,
                            })
                        }
                        onChange={(e) => toggleCategory(Number(e.target.value), e.target.checked)}
                        className="form-check-input"
                        type="checkbox"
                        id={`cbCategory${item.id}`}
                        value={item.id}
                    />
                    <label className="form-check-label mb-50" htmlFor={`cbCategory${item.id}`}>
                        {'item.name'}
                    </label>
                </div>
            </td>
            {!isHiddenCategoryPriority && (
                <td className="text-center py-25">
                    <select
                        value={
                            +getFieldInArrayObject(articleCategoryMaps, item.id!, 'priority', '1', 'saArticleCateId')
                        }
                        onChange={(e) => onChangeCategoryPriority?.(+e.target.value, item.id!)}
                        disabled={
                            !find(articleCategoryMaps, {
                                saArticleCateId: item.id,
                            })
                        }
                        className="form-select-sm px-3 py-2 border border-[#BDC1CAFF] rounded"
                    >
                        {Array.from({ length: 20 }, (_, i) => (
                            <option key={i} value={i + 1}>
                                {i + 1}
                            </option>
                        ))}
                    </select>
                </td>
            )}
        </tr>
    );
}
