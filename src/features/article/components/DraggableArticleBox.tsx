import React, { useState, useRef, useEffect } from 'react';
import { ArticleBox } from '../../../types/Article';
import { X } from 'react-feather';

interface IProps {
    box: ArticleBox;
    canvasWidth: number;
    canvasHeight: number;
    onUpdate: (boxId: string | number, updates: Partial<ArticleBox>) => void;
    onRemove: (boxId: string | number) => void;
}

export default function DraggableArticleBox({ box, canvasWidth, canvasHeight, onUpdate, onRemove }: Readonly<IProps>) {
    const [isDragging, setIsDragging] = useState(false);
    const [isResizing, setIsResizing] = useState(false);
    const [resizeDirection, setResizeDirection] = useState<string>('');
    const [dragStart, setDragStart] = useState({ x: 0, y: 0 });
    const [resizeStart, setResizeStart] = useState({ x: 0, y: 0, width: 0, height: 0, boxX: 0, boxY: 0 });
    const boxRef = useRef<HTMLDivElement>(null);
    const contentRef = useRef<HTMLDivElement>(null);

    // Calculate character count
    const characterCount = box.article.content ? box.article.content.replace(/<[^>]*>/g, '').length : 0;

    // Check if content is overflowing
    useEffect(() => {
        if (contentRef.current) {
            const isOverflowing = contentRef.current.scrollHeight > contentRef.current.clientHeight;
            if (isOverflowing !== box.isOverflowing) {
                onUpdate(box.id, { isOverflowing });
            }
        }
    }, [box.width, box.height, box.article.content, box.isOverflowing, box.id, onUpdate]);

    const handleMouseDown = (e: React.MouseEvent) => {
        // Chỉ cho phép drag khi click vào content area, không phải resize handles
        const target = e.target as HTMLElement;
        if (!target.classList.contains('resize-handle')) {
            setIsDragging(true);
            setDragStart({
                x: e.clientX - box.x,
                y: e.clientY - box.y,
            });
            e.preventDefault();
        }
    };

    const handleResizeMouseDown = (direction: string) => (e: React.MouseEvent) => {
        setIsResizing(true);
        setResizeDirection(direction);
        setResizeStart({
            x: e.clientX,
            y: e.clientY,
            width: box.width,
            height: box.height,
            boxX: box.x,
            boxY: box.y,
        });
        e.preventDefault();
        e.stopPropagation();
    };

    useEffect(() => {
        const handleMouseMove = (e: MouseEvent) => {
            if (isDragging) {
                const newX = Math.max(0, Math.min(canvasWidth - box.width, e.clientX - dragStart.x));
                const newY = Math.max(0, Math.min(canvasHeight - box.height, e.clientY - dragStart.y));
                onUpdate(box.id, { x: newX, y: newY });
            } else if (isResizing) {
                const deltaX = e.clientX - resizeStart.x;
                const deltaY = e.clientY - resizeStart.y;

                let newX = resizeStart.boxX;
                let newY = resizeStart.boxY;
                let newWidth = resizeStart.width;
                let newHeight = resizeStart.height;

                switch (resizeDirection) {
                    case 'nw': // Top-left
                        newWidth = Math.max(100, resizeStart.width - deltaX);
                        newHeight = Math.max(80, resizeStart.height - deltaY);
                        // Khi resize từ top-left, vị trí phải thay đổi ngược lại với kích thước
                        newX = resizeStart.boxX + (resizeStart.width - newWidth);
                        newY = resizeStart.boxY + (resizeStart.height - newHeight);
                        // Đảm bảo không vượt ra ngoài canvas
                        newX = Math.max(0, newX);
                        newY = Math.max(0, newY);
                        break;
                    case 'ne': // Top-right
                        newWidth = Math.max(100, Math.min(canvasWidth - resizeStart.boxX, resizeStart.width + deltaX));
                        newHeight = Math.max(80, resizeStart.height - deltaY);
                        newX = resizeStart.boxX; // X không thay đổi
                        newY = resizeStart.boxY + (resizeStart.height - newHeight);
                        newY = Math.max(0, newY);
                        break;
                    case 'sw': // Bottom-left
                        newWidth = Math.max(100, resizeStart.width - deltaX);
                        newHeight = Math.max(
                            80,
                            Math.min(canvasHeight - resizeStart.boxY, resizeStart.height + deltaY)
                        );
                        newX = resizeStart.boxX + (resizeStart.width - newWidth);
                        newY = resizeStart.boxY; // Y không thay đổi
                        newX = Math.max(0, newX);
                        break;
                    case 'se': // Bottom-right (default)
                    default:
                        newWidth = Math.max(100, Math.min(canvasWidth - resizeStart.boxX, resizeStart.width + deltaX));
                        newHeight = Math.max(
                            80,
                            Math.min(canvasHeight - resizeStart.boxY, resizeStart.height + deltaY)
                        );
                        newX = resizeStart.boxX; // X không thay đổi
                        newY = resizeStart.boxY; // Y không thay đổi
                        break;
                }

                onUpdate(box.id, { x: newX, y: newY, width: newWidth, height: newHeight });
            }
        };

        const handleMouseUp = () => {
            setIsDragging(false);
            setIsResizing(false);
            setResizeDirection('');
        };

        if (isDragging || isResizing) {
            document.addEventListener('mousemove', handleMouseMove);
            document.addEventListener('mouseup', handleMouseUp);
        }

        return () => {
            document.removeEventListener('mousemove', handleMouseMove);
            document.removeEventListener('mouseup', handleMouseUp);
        };
    }, [isDragging, isResizing, dragStart, resizeStart, resizeDirection, box, canvasWidth, canvasHeight, onUpdate]);

    const borderColor = box.isOverflowing ? 'border-danger' : 'border-[#50C878]';

    return (
        <div
            ref={boxRef}
            className={`position-absolute border-2 ${borderColor} bg-white shadow-sm`}
            style={{
                left: `${box.x}px`,
                top: `${box.y}px`,
                width: `${box.width}px`,
                height: `${box.height}px`,
                cursor: isDragging ? 'grabbing' : 'grab',
                zIndex: isDragging || isResizing ? 1000 : 1,
                overflow: 'hidden',
                boxSizing: 'border-box',
            }}
            onMouseDown={handleMouseDown}
        >
            {/* Header with character count and controls */}
            <div className="d-flex justify-content-between align-items-center p-1 bg-light border-bottom">
                <div className="small text-muted">Tổng số ký tự: {characterCount}</div>
                <button
                    className="btn btn-sm btn-outline-danger p-0"
                    style={{ width: '20px', height: '20px' }}
                    onClick={(e) => {
                        e.stopPropagation();
                        onRemove(box.id);
                    }}
                >
                    <X size={12} />
                </button>
            </div>

            {/* Content area */}
            <div
                ref={contentRef}
                className="p-2"
                style={{
                    height: 'calc(100% - 32px)',
                    fontSize: '10px',
                    lineHeight: '1.2',
                    overflow: 'hidden',
                    wordWrap: 'break-word',
                    wordBreak: 'break-word',
                }}
            >
                <div
                    className="fw-bold mb-1"
                    style={{
                        fontSize: '11px',
                        overflow: 'hidden',
                        textOverflow: 'ellipsis',
                        whiteSpace: 'nowrap',
                    }}
                >
                    {box.article.title}
                </div>
                <div
                    style={{
                        overflow: 'hidden',
                        wordWrap: 'break-word',
                        wordBreak: 'break-word',
                        textOverflow: 'ellipsis',
                    }}
                    dangerouslySetInnerHTML={{
                        __html: box.article.content || 'Không có nội dung',
                    }}
                />
            </div>

            {/* Resize handles ở 4 góc */}
            {/* Top-left */}
            <div
                className="resize-handle position-absolute bg-primary"
                style={{
                    top: '-4px',
                    left: '-4px',
                    width: '8px',
                    height: '8px',
                    cursor: 'nw-resize',
                    borderRadius: '50%',
                }}
                onMouseDown={handleResizeMouseDown('nw')}
            />

            {/* Top-right */}
            <div
                className="resize-handle position-absolute bg-primary"
                style={{
                    top: '-4px',
                    right: '-4px',
                    width: '8px',
                    height: '8px',
                    cursor: 'ne-resize',
                    borderRadius: '50%',
                }}
                onMouseDown={handleResizeMouseDown('ne')}
            />

            {/* Bottom-left */}
            <div
                className="resize-handle position-absolute bg-primary"
                style={{
                    bottom: '-4px',
                    left: '-4px',
                    width: '8px',
                    height: '8px',
                    cursor: 'sw-resize',
                    borderRadius: '50%',
                }}
                onMouseDown={handleResizeMouseDown('sw')}
            />

            {/* Bottom-right */}
            <div
                className="resize-handle position-absolute bg-primary"
                style={{
                    bottom: '-4px',
                    right: '-4px',
                    width: '8px',
                    height: '8px',
                    cursor: 'se-resize',
                    borderRadius: '50%',
                }}
                onMouseDown={handleResizeMouseDown('se')}
            />
        </div>
    );
}
