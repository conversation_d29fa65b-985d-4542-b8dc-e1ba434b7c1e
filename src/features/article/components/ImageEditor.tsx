import React, { useState, useEffect } from 'react';
import LeftSidebar from './ImageEditor/LeftSidebar';
import CenterPanel from './ImageEditor/CenterPanel';
import RightSidebar from './ImageEditor/RightSidebar';
import CropConfirmModal from './ImageEditor/CropConfirmModal';
import imageEditorService, { ImageEditorSaveData } from '../../../services/ImageEditorService';
import { showToast } from '../../../utils/common';
import useImageHistory from '../../../hooks/useImageHistory';
import { ActionType, CropArea } from '../../../types/ImageEditor';
import { FileResponse } from '../../../types/common/Item';

interface ImageEditorProps {
    imageUrl?: string;
    departmentId?: number;
    onSave?: (data: ImageEditorSaveData) => void;
    onCancel?: () => void;
    initialDescription?: string; // Ch<PERSON> thích ban đầu của ảnh
    rootImg: FileResponse | null;
    onEditRootImage: (rootImg: FileResponse) => void;
}

const ImageEditor = ({
    imageUrl = '/api/placeholder/400/300',
    departmentId = 1,
    onSave,
    onCancel,
    initialDescription = '',
    rootImg,
    onEditRootImage,
}: Readonly<ImageEditorProps>) => {
    const [activeTab, setActiveTab] = useState('info');

    // Crop states
    const [isCropMode, setIsCropMode] = useState(false);
    const [showCropConfirm, setShowCropConfirm] = useState(false);
    const [pendingAction, setPendingAction] = useState<(() => void) | null>(null);
    const [pendingCropArea, setPendingCropArea] = useState<CropArea | null>(null);

    // Initialize image history hook
    const { currentState, canUndo, canRedo, addToHistory, undo, redo, resetHistory } = useImageHistory(
        imageUrl,
        imageUrl,
        initialDescription
    );

    // Reset history when imageUrl or initialDescription changes
    useEffect(() => {
        resetHistory(imageUrl, imageUrl, initialDescription);
    }, [imageUrl, initialDescription, resetHistory]);

    const handleSave = async () => {
        // Zoom level validation removed since zoom is now visual only

        try {
            // Tạo data để save với tất cả thông tin - không xử lý ảnh ở đây
            const saveData = imageEditorService.createSaveData(
                currentState.description,
                currentState.tags,
                currentState.imageUrl,
                currentState.zoomLevel,
                undefined, // imageId sẽ được xử lý ở TextEditorTinyMce
                currentState.originalImageUrl,
                {
                    cropArea: currentState.cropArea,
                    flipHorizontal: currentState.flipHorizontal,
                    flipVertical: currentState.flipVertical,
                    rotation: currentState.rotation,
                    watermark: currentState.watermark,
                    textOverlays: currentState.textOverlays,
                    frame: currentState.frame,
                    imageWidth: currentState.imageWidth,
                    imageHeight: currentState.imageHeight,
                }
            );

            // Trả về data cho parent component xử lý
            onSave?.(saveData);
        } catch (error) {
            showToast(false, [`Lỗi tạo dữ liệu: ${error}`]);
        }
    };

    const handleCancel = () => {
        onCancel?.();
    };

    const handleUndo = () => {
        const previousState = undo();
        if (previousState) {
            showToast(true, ['Đã hoàn tác thao tác']);
        }
    };

    const handleRedo = () => {
        const nextState = redo();
        if (nextState) {
            showToast(true, ['Đã làm lại thao tác']);
        }
    };

    const handleRestore = () => {
        addToHistory(
            {
                imageUrl: currentState.originalImageUrl,
                zoomLevel: 100,
                flipHorizontal: false,
                flipVertical: false,
                rotation: 0,
                cropArea: undefined,
                watermark: undefined,
                textOverlays: [],
                frame: undefined,
            },
            ActionType.RESTORE
        );
        showToast(true, ['Đã khôi phục ảnh gốc']);
    };

    // Handler functions for different actions
    const handleZoomChange = (newZoomLevel: number) => {
        addToHistory({ zoomLevel: newZoomLevel }, ActionType.ZOOM);
    };

    const handleDescriptionChange = (description: string) => {
        addToHistory({ description }, ActionType.ZOOM); // Using ZOOM as general update
    };

    const handleTagsChange = (tags: string[]) => {
        addToHistory({ tags }, ActionType.ZOOM); // Using ZOOM as general update
    };

    // Crop handlers
    const handleCropModeChange = (isActive: boolean) => {
        setIsCropMode(isActive);
        if (!isActive) {
            setPendingCropArea(null);
        }
    };

    const handleCropDataChange = (cropArea: CropArea | null) => {
        setPendingCropArea(cropArea);
    };

    const handleCropApply = async (cropArea: CropArea) => {
        try {
            // Apply crop using service
            const croppedImageUrl = await imageEditorService.applyCrop(currentState.imageUrl, cropArea);

            // Add to history
            addToHistory(
                {
                    imageUrl: croppedImageUrl,
                    cropArea,
                },
                ActionType.CROP
            );

            showToast(true, ['Đã cắt ảnh thành công']);
        } catch (error) {
            showToast(false, [`Lỗi cắt ảnh: ${error}`]);
        }
    };

    const handleConfirmPendingCrop = (action?: () => void) => {
        if (isCropMode) {
            if (action) {
                setPendingAction(() => action);
            }
            setShowCropConfirm(true);
        } else if (action) {
            // If not in crop mode, execute action directly
            action();
        }
    };

    const handleCropConfirm = async () => {
        // Apply current crop first, then execute pending action
        if (pendingCropArea) {
            try {
                await handleCropApply(pendingCropArea);
            } catch (error) {
                showToast(false, [`Lỗi áp dụng cắt: ${error}`]);
            }
        }

        setIsCropMode(false);
        setShowCropConfirm(false);
        setPendingCropArea(null);

        if (pendingAction) {
            pendingAction();
            setPendingAction(null);
        }
    };

    const handleCropCancel = () => {
        setShowCropConfirm(false);
        setPendingAction(null);
        setPendingCropArea(null);
    };

    const handleCropDiscard = () => {
        // Discard crop and execute pending action
        setIsCropMode(false);
        setShowCropConfirm(false);
        setPendingCropArea(null);

        if (pendingAction) {
            pendingAction();
            setPendingAction(null);
        }
    };

    return (
        <div className="flex bg-gray-50">
            <LeftSidebar activeTab={activeTab} onTabChange={setActiveTab} />

            <CenterPanel
                imageUrl={currentState.imageUrl}
                zoomLevel={currentState.zoomLevel}
                rotation={currentState.rotation}
                currentState={currentState}
                addToHistory={addToHistory}
                onZoomChange={handleZoomChange}
                onUndo={handleUndo}
                onRedo={handleRedo}
                onRestore={handleRestore}
                canUndo={canUndo}
                canRedo={canRedo}
                isCropMode={isCropMode}
                onCropModeChange={handleCropModeChange}
                onCropApply={handleCropApply}
                onCropDataChange={handleCropDataChange}
            />

            <RightSidebar
                activeTab={activeTab}
                description={currentState.description}
                tags={currentState.tags}
                onDescriptionChange={handleDescriptionChange}
                onTagsChange={handleTagsChange}
                onSave={handleSave}
                onCancel={handleCancel}
                currentState={currentState}
                addToHistory={addToHistory}
                isCropMode={isCropMode}
                onCropModeChange={handleCropModeChange}
                onConfirmPendingCrop={handleConfirmPendingCrop}
                rootImg={rootImg}
                onEditRootImage={onEditRootImage}
            />

            {/* Crop Confirmation Modal */}
            <CropConfirmModal
                show={showCropConfirm}
                onApplyCrop={handleCropConfirm}
                onDiscardCrop={handleCropDiscard}
                onCancel={handleCropCancel}
            />
        </div>
    );
};

export default ImageEditor;
