import { ARTICLE_TAB } from 'constants/common';
import { Download, PlayCircle, X, Image, Video, Music, FileText, File, CheckSquare, Square } from 'react-feather';
import ArticleFile from '../../../types/ArticleFile';
import { useEffect, useState } from 'react';
import { useGraphQLMutation } from 'hooks/useGraphQLMutation';
import { ARTICLE_FILE_UPDATE_IS_ROYALTY } from 'services/ArticleFilesUpdateIsRoyaltyService';
import { showToast } from 'utils/common';
import { t } from 'i18next';

// File type constants
const FILE_TYPES = {
    IMAGE: 'image',
    VIDEO: 'video',
    AUDIO: 'audio',
    DOCUMENT: 'document',
} as const;

type FileType = (typeof FILE_TYPES)[keyof typeof FILE_TYPES];

interface UpdateArticleFormTabMediaProps {
    setTabAction(tab: ARTICLE_TAB | null): void;
    articleFiles?: ArticleFile[];
    refetchArticle?: () => void;
}

// Utility functions
const getFileType = (mimeType: string): FileType => {
    if (mimeType.startsWith('image/')) return FILE_TYPES.IMAGE;
    if (mimeType.startsWith('video/')) return FILE_TYPES.VIDEO;
    if (mimeType.startsWith('audio/')) return FILE_TYPES.AUDIO;
    return FILE_TYPES.DOCUMENT;
};

const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

const getFileIcon = (fileType: FileType, size: number = 22) => {
    switch (fileType) {
        case FILE_TYPES.IMAGE:
            return <Image size={size} className="text-success" />;
        case FILE_TYPES.VIDEO:
            return <Video size={size} className="text-primary" />;
        case FILE_TYPES.AUDIO:
            return <Music size={size} className="text-warning" />;
        case FILE_TYPES.DOCUMENT:
            return <FileText size={size} className="text-info" />;
        default:
            return <File size={size} className="text-muted" />;
    }
};

const downloadFile = (fileUrl: string, fileName: string) => {
    const link = document.createElement('a');
    link.href = fileUrl;
    link.download = fileName;
    link.target = '_blank';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
};

const openFileInNewTab = (fileUrl: string) => {
    window.open(fileUrl, '_blank');
};

const groupFilesByType = (articleFiles: ArticleFile[]) => {
    const grouped: Record<FileType, ArticleFile[]> = {
        [FILE_TYPES.IMAGE]: [],
        [FILE_TYPES.VIDEO]: [],
        [FILE_TYPES.AUDIO]: [],
        [FILE_TYPES.DOCUMENT]: [],
    };

    articleFiles.forEach((articleFile) => {
        const fileType = getFileType(articleFile.file.mime_type);
        grouped[fileType].push(articleFile);
    });

    return grouped;
};

const getFileTypeTitle = (fileType: FileType): string => {
    switch (fileType) {
        case FILE_TYPES.IMAGE:
            return 'Hình ảnh';
        case FILE_TYPES.VIDEO:
            return 'Video';
        case FILE_TYPES.AUDIO:
            return 'Audio';
        case FILE_TYPES.DOCUMENT:
            return 'Tài liệu';
        default:
            return 'Khác';
    }
};

export const UpdateArticleFormTabMedia = ({
    setTabAction,
    articleFiles = [],
    refetchArticle,
}: UpdateArticleFormTabMediaProps) => {
    const groupedFiles = groupFilesByType(articleFiles);
    const [localArticleFiles, setLocalArticleFiles] = useState<ArticleFile[]>([]);

    useEffect(() => {
        setLocalArticleFiles(articleFiles);
    }, [articleFiles]);

    const updateRoyaltyMutation = useGraphQLMutation<{ id: number; body: { is_royalty: boolean } }>(
        ARTICLE_FILE_UPDATE_IS_ROYALTY,
        '',
        {
            onSuccess: () => {
                showToast(true, [t('success.update')]);
                refetchArticle?.();
            },
            onError: () => {
                showToast(false, [t('error.common')]);
            },
        }
    );

    const handleRoyaltyChange = (id: number, currentValue: boolean) => {
        const updated = !currentValue;

        setLocalArticleFiles((prev) => prev.map((file) => (file.id === id ? { ...file, is_royalty: updated } : file)));

        if (id) {
            updateRoyaltyMutation.mutate({
                id,
                body: { is_royalty: updated },
            });
        }
    };

    const renderFileItem = (articleFile: ArticleFile) => {
        const { file } = articleFile;
        const fileType = getFileType(file.mime_type);
        const isImage = fileType === FILE_TYPES.IMAGE;
        const isVideo = fileType === FILE_TYPES.VIDEO;
        const current = localArticleFiles.find((file) => file.id === articleFile.id);

        return (
            <div key={articleFile.id} className="grid grid-cols-[48px,1fr,40px] items-center">
                <div className="relative w-[48px] h-[48px]">
                    {isImage ? (
                        <img
                            src={file.file_url}
                            className="w-[48px] h-[48px] object-cover rounded"
                            alt={file.file_name}
                            onError={(e) => {
                                // Fallback to icon if image fails to load
                                const target = e.target as HTMLImageElement;
                                target.style.display = 'none';
                                target.nextElementSibling?.classList.remove('hidden');
                            }}
                        />
                    ) : (
                        <div className="w-[48px] h-[48px] bg-gray-100 rounded flex items-center justify-center">
                            {getFileIcon(fileType, 24)}
                        </div>
                    )}
                    {isVideo && (
                        <PlayCircle
                            className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 text-white"
                            size={22}
                        />
                    )}
                </div>
                <div className="pl-3 pr-[5px] flex flex-col justify-center">
                    <div className="flex">
                        <span
                            className="flex-1 text-[14px] text-[#171A1FFF] line-clamp-1 cursor-pointer hover:text-blue-600"
                            onClick={() => openFileInNewTab(file.file_url)}
                            title={file.file_name}
                        >
                            {file.file_title || 'Không có tiêu đề'}
                        </span>
                    </div>
                    <div className="my-[2px]">
                        <span className="text-[#9095A1FF] text-[14px]">{formatFileSize(file.file_size)}</span>
                    </div>
                </div>
                <div className="flex items-center justify-end gap-x-5">
                    <button
                        className="hover:bg-gray-100 rounded"
                        onClick={() => downloadFile(file.file_url, file.file_name)}
                        title="Tải xuống"
                    >
                        <Download className="text-[#379AE6FF]" size={20} />
                    </button>
                    <div className="form-check form-check-inline">
                        <input
                            type="checkbox"
                            className="form-check-input custom-checkbox"
                            id={`royalty-${articleFile.id}`}
                            checked={current?.is_royalty ?? false}
                            onChange={() => handleRoyaltyChange(articleFile.id as number, current?.is_royalty ?? false)}
                        />
                    </div>
                </div>
            </div>
        );
    };

    const renderFileTypeSection = (fileType: FileType) => {
        const files = groupedFiles[fileType];
        if (files.length === 0) return null;

        return (
            <div key={fileType} className="mt-1">
                <div className="flex flex-row justify-between  bg-[#F8F9FA]">
                    <h4 className="card-title py-50 mb-0 px-1 !text-base !font-normal">{getFileTypeTitle(fileType)}</h4>
                    <h4 className="card-title py-50 mb-0 px-1 !text-base !font-normal">Nhuận bút</h4>
                </div>
                <div className="p-[.75rem] flex flex-col gap-y-7">{files.map(renderFileItem)}</div>
            </div>
        );
    };

    return (
        <div className="card !mb-0">
            <div className="card-header bg-[#FCF3F5FF] p-[.75rem] py-25">
                <h4 className="card-title !text-base text-[#A42D49FF]">Kho media</h4>
                <div className="heading-elements">
                    <ul className="mb-0 list-inline">
                        <li>
                            <X
                                size={24}
                                className="text-[#A42D49FF] pt-1.5 cursor-pointer"
                                onClick={() => setTabAction(null)}
                            />
                        </li>
                    </ul>
                </div>
            </div>
            <div>
                <div className="p-0 card-body">
                    {articleFiles.length === 0 ? (
                        <div className="p-4 text-center text-gray-500">Không có file media nào</div>
                    ) : (
                        <>{Object.values(FILE_TYPES).map((fileType) => renderFileTypeSection(fileType))}</>
                    )}
                </div>
            </div>
        </div>
    );
};
