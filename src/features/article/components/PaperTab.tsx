import React from 'react';
import { PlanTypesName } from '../../../types/Article';

interface IProps {
    activeTab: number;
    onTabChange: (tabId: number) => void;
}

export default function PaperTab({ activeTab, onTabChange }: Readonly<IProps>) {
    return (
        <ul className="nav nav-tabs" role="tablist">
            {PlanTypesName.map((item) => (
                <li className="nav-item" role="presentation" key={item.id}>
                    <button
                        type="button"
                        className={`nav-link waves-effect ${activeTab === item.id ? 'active' : ''}`}
                        role="tab"
                        data-bs-toggle="tab"
                        data-bs-target={`#navs-top-${item.id}`}
                        aria-controls={`navs-top-${item.id}`}
                        aria-selected={activeTab === item.id ? 'true' : 'false'}
                        tabIndex={-1}
                        onClick={() => onTabChange(item.id)}
                    >
                        {item.name}
                    </button>
                </li>
            ))}
        </ul>
    );
}
