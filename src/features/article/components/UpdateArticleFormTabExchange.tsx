import classNames from 'classnames';
import { ARTICLE_TAB } from 'constants/common';
import { X } from 'react-feather';
import ArticleNote from 'types/ArticleNote';
import ListArticleNote from './ListArticleNote';

interface UpdateArticleFormTabExchangeProps {
    setTabAction(tab: ARTICLE_TAB | null): void;
    articleNotes: ArticleNote[];
    onAddNote(note: string): void;
}

export const UpdateArticleFormTabExchange = ({
    setTabAction,
    articleNotes,
    onAddNote,
}: UpdateArticleFormTabExchangeProps) => (
    <div className="card !mb-0">
        <div className="card-header bg-[#FCF3F5FF] p-[.75rem] py-25">
            <h4 className="card-title !text-base text-[#A42D49FF]">Ghi chú trao đổi</h4>
            <div className="heading-elements">
                <ul className="mb-0 list-inline">
                    <li>
                        <X
                            size={24}
                            className="text-[#A42D49FF] pt-1.5 cursor-pointer"
                            onClick={() => setTabAction(null)}
                        />
                    </li>
                </ul>
            </div>
        </div>
        <div>
            <ListArticleNote items={articleNotes} handleAdd={onAddNote} />
        </div>
    </div>
);
