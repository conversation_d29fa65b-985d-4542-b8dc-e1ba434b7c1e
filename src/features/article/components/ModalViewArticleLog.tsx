import classNames from 'classnames';
import { capitalize, isEmpty } from 'lodash';
import { useMemo } from 'react';
import { X } from 'react-feather';
import ArticleLog from 'types/ArticleLog';

type Props = {
    show: boolean;
    changeShow: (s: boolean) => void;
    articleLog?: ArticleLog;
};

const ModalViewArticleLog = ({ show, changeShow, articleLog }: Props) => {
    const changes = useMemo(() => {
        if (isEmpty(articleLog)) return {};

        const content = JSON.parse(articleLog.content);

        return content?.details?.changes?.field_changes || {};
    }, [articleLog]);

    return (
        <>
            <div
                className={classNames('modal fade text-start modal-primary z-[1350]', { show })}
                style={{ display: show ? 'block' : 'none' }}
            >
                <div className="modal-dialog modal-dialog-centered modal-lg">
                    <div className="modal-content">
                        <div className="flex px-[20px] pt-6 mb-2">
                            <h5 className="text-2xl font-bold text-[#171A1FFF] flex-1"><PERSON><PERSON>ch sử thay đổi</h5>
                            <button type="button" onClick={() => changeShow(false)}>
                                <X />
                            </button>
                        </div>

                        <div className="px-6 pb-6">
                            <div className="table-responsive">
                                <table
                                    className="table"
                                    style={{
                                        tableLayout: 'fixed',
                                    }}
                                >
                                    <thead>
                                        <tr>
                                            <th className="text-center !px-2" style={{ width: '96px' }}>
                                                Trường
                                            </th>
                                            <th className="text-center !px-2" style={{ width: '128px' }}>
                                                Giá trị cũ
                                            </th>
                                            <th className="text-center !px-2" style={{ width: '132px' }}>
                                                Giá trị mới
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {Object.keys(changes).map((key) => (
                                            <tr key={key}>
                                                <td className="text-center !px-2">{capitalize(key)}</td>
                                                <td className="text-center !px-2">
                                                    <div
                                                        className="truncate"
                                                        dangerouslySetInnerHTML={{
                                                            __html: changes[key].from,
                                                        }}
                                                    />
                                                </td>
                                                <td className="text-center !px-2">
                                                    <div
                                                        className="truncate"
                                                        dangerouslySetInnerHTML={{
                                                            __html: changes[key].to,
                                                        }}
                                                    />
                                                </td>
                                            </tr>
                                        ))}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {show && <div className="modal-backdrop fade show" />}
        </>
    );
};

export default ModalViewArticleLog;
