import React from 'react';
import { useTranslation } from 'react-i18next';
import { ArticleType, ArticleTypeNames } from '../../../types/common/Item';
import { CategoryType } from '../../../types/Category';
import { genTableIndex } from '../../../utils/common';
import { formatDateTime, FORMAT_DATE } from '../../../utils/date';
import { Trash2, Edit, PenTool } from 'react-feather';
import Article, { ArticlePageType } from '../../../types/Article';
import { Paging } from '../../../types/common';
import ArticleCategory from '../../../types/ArticleCategory';
import { Link } from 'react-router-dom';

interface IProps {
    articles: Article[];
    paging: Paging;
    onDelete: (id: number) => void;
    acticlePageType: ArticlePageType;
    isRoyaltiesPage?: boolean;
}

export default function AllArticleList({
    articles,
    paging,
    onDelete,
    acticlePageType,
    isRoyaltiesPage = false,
}: Readonly<IProps>) {
    const { t } = useTranslation();

    const renderActionColumn = (article: Article) => {
        if (isRoyaltiesPage) {
            return (
                <div className="d-flex justify-content-center">
                    <Link to="/" className="btn btn-icon btn-sm btn-flat-primary waves-effect" title="Chấm điểm">
                        <PenTool size={14} />
                    </Link>
                </div>
            );
        }

        return (
            <div className="d-flex justify-content-center">
                <Link
                    to={`/article/edit/${acticlePageType}/${article.article_type_id}/${article.id}`}
                    className="btn btn-icon btn-sm btn-flat-primary waves-effect me-1"
                    title="Sửa"
                >
                    <Edit size={14} />
                </Link>
                <button
                    className="btn btn-icon btn-sm btn-flat-danger waves-effect"
                    title="Xóa"
                    onClick={() => onDelete(article.id!)}
                >
                    <Trash2 size={14} />
                </button>
            </div>
        );
    };

    return (
        <tbody>
            {articles.map((article, index) => {
                // Nếu có children articles, hiển thị với rowspan cho STT và Tiêu đề
                if (article.childrenArticles && article.childrenArticles.length > 0) {
                    return (
                        <React.Fragment key={article.id}>
                            {/* Row hiển thị thông tin của article chính */}
                            <tr key={`${article.id}-parent`}>
                                <td className="text-center" rowSpan={article.childrenArticles.length + 1}>
                                    {genTableIndex(index, paging.limit, paging.current_page)}
                                </td>
                                <td rowSpan={article.childrenArticles.length + 1}>
                                    <div>{article.title}</div>
                                    <div className="text-muted small">
                                        {article.pseudonym?.name || 'Chưa có bút danh'}
                                    </div>
                                    <div className="text-muted small">
                                        {article.publish_date
                                            ? formatDateTime(article.publish_date, FORMAT_DATE.SHOW_DATE_TIME)
                                            : 'Chưa có ngày đăng'}
                                    </div>
                                </td>
                                {/* Hiển thị thông tin của article chính */}
                                <td>
                                    {t(
                                        `${
                                            ArticleTypeNames.find((type) => type.id === article.article_type_id)
                                                ?.name || 'unclassified'
                                        }.single`
                                    )}
                                </td>
                                <td>
                                    {article.articleCategories && article.articleCategories.length > 0 ? (
                                        article.articleCategories
                                            .filter(
                                                (ac: ArticleCategory) =>
                                                    ac.category?.category_type_id === CategoryType.CATEGORY
                                            )
                                            .map((ac: ArticleCategory) => <div key={ac.id}>{ac.category?.name}</div>)
                                    ) : (
                                        <div className="text-muted small">Chưa có chuyên mục</div>
                                    )}
                                </td>
                                <td>
                                    <div>{article.updatedByUser?.full_name || article.createdByUser?.full_name}</div>
                                    <div className="text-muted small">
                                        Ngày biên soạn:{' '}
                                        {formatDateTime(
                                            article.updated_at || article.created_at!,
                                            FORMAT_DATE.SHOW_ONLY_DATE
                                        )}
                                    </div>
                                </td>
                                <td className="text-center">
                                    <span
                                        style={{ backgroundColor: article.workflow?.desc || '#000000' }}
                                        className={`text-[#fff] badge`}
                                    >
                                        {article.workflow?.name || 'Chưa có trạng thái'}
                                    </span>
                                </td>
                                <td className="text-center">{renderActionColumn(article)}</td>
                            </tr>
                            {/* Các row hiển thị thông tin của childrenArticles */}
                            {article.childrenArticles.map((childArticle) => (
                                <tr key={`${article.id}-child-${childArticle.id}`}>
                                    {/* Từ cột Thể loại tin trở đi hiển thị thông tin của childArticle */}
                                    <td>
                                        {t(
                                            `${
                                                ArticleTypeNames.find(
                                                    (type) => type.id === childArticle.article_type_id
                                                )?.name || 'unclassified'
                                            }.single`
                                        )}
                                    </td>
                                    <td>
                                        {childArticle.articleCategories && childArticle.articleCategories.length > 0 ? (
                                            childArticle.articleCategories
                                                .filter(
                                                    (ac: ArticleCategory) =>
                                                        ac.category?.category_type_id === CategoryType.CATEGORY
                                                )
                                                .map((ac: ArticleCategory) => (
                                                    <div key={ac.id}>{ac.category?.name}</div>
                                                ))
                                        ) : (
                                            <div className="text-muted small">Chưa có chuyên mục</div>
                                        )}
                                    </td>
                                    <td>
                                        <div>
                                            {childArticle.updatedByUser?.full_name ||
                                                childArticle.createdByUser?.full_name}
                                        </div>
                                        <div className="text-muted small">
                                            Ngày biên soạn:{' '}
                                            {formatDateTime(
                                                childArticle.updated_at || childArticle.created_at!,
                                                FORMAT_DATE.SHOW_ONLY_DATE
                                            )}
                                        </div>
                                    </td>
                                    <td className="text-center">
                                        <span
                                            style={{ backgroundColor: article.workflow?.desc || '#000000' }}
                                            className={`text-[#fff] badge `}
                                        >
                                            {childArticle.workflow?.name || 'Chưa có trạng thái'}
                                        </span>
                                    </td>
                                    <td className="text-center">{renderActionColumn(childArticle)}</td>
                                </tr>
                            ))}
                        </React.Fragment>
                    );
                } else {
                    // Hiển thị parent article nếu không có children
                    return (
                        <tr key={article.id}>
                            <td className="text-center">{genTableIndex(index, paging.limit, paging.current_page)}</td>
                            <td>
                                <div>{article.title}</div>
                                <div className="text-muted small">{article.pseudonym?.name || 'Chưa có bút danh'}</div>
                                <div className="text-muted small">
                                    {formatDateTime(
                                        article.publish_date || new Date().toISOString(),
                                        FORMAT_DATE.SHOW_DATE_TIME
                                    )}
                                </div>
                            </td>
                            <td>
                                {t(
                                    `${
                                        ArticleTypeNames.find((type) => type.id === article.article_type_id)?.name ||
                                        'unclassified'
                                    }.single`
                                )}
                            </td>
                            <td>
                                {article.articleCategories && article.articleCategories.length > 0 ? (
                                    article.articleCategories
                                        .filter(
                                            (ac: ArticleCategory) =>
                                                ac.category?.category_type_id === CategoryType.CATEGORY
                                        )
                                        .map((ac: ArticleCategory) => <div key={ac.id}>{ac.category?.name}</div>)
                                ) : (
                                    <div className="text-muted small">Chưa có chuyên mục</div>
                                )}
                            </td>
                            <td>
                                <div>{article.updatedByUser?.full_name || article.createdByUser?.full_name}</div>
                                <div className="text-muted small">
                                    Ngày biên soạn:{' '}
                                    {formatDateTime(
                                        article.updated_at || article.created_at!,
                                        FORMAT_DATE.SHOW_ONLY_DATE
                                    )}
                                </div>
                            </td>
                            <td className="text-center">
                                <span
                                    className={`text-[#fff] badge`}
                                    style={{ backgroundColor: article.workflow?.desc || '#000000' }}
                                >
                                    {article.workflow?.name || 'Chưa có trạng thái'}
                                </span>
                            </td>
                            <td className="text-center">{renderActionColumn(article)}</td>
                        </tr>
                    );
                }
            })}
        </tbody>
    );
}
