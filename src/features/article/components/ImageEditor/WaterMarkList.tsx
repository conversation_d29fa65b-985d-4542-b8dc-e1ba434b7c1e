import Config from 'types/Config';

interface WatermarkListProps {
    items: Config[];
    selectedWatermark: number | null;
    handleWatermarkSelect: (id: number) => void;
}

const WatermarkList: React.FC<WatermarkListProps> = ({
    items,
    selectedWatermark,
    handleWatermarkSelect,
}: Readonly<WatermarkListProps>) => (
    <div className="grid grid-cols-3 gap-2 mb-3">
        {items.map((item) => (
            <div
                key={item.id}
                onClick={() => item.id !== undefined && handleWatermarkSelect(item.id)}
                className={`w-[200px] h-[200px] relative cursor-pointer rounded-md border-1 transition-colors ${
                    selectedWatermark === item.id ? 'border-[#A42C48]' : 'border-gray-200 hover:border-gray-300'
                }`}
            >
                <img src={item.content} alt="" className="h100px mx-auto object-contain" />
            </div>
        ))}
    </div>
);

export default WatermarkList;
