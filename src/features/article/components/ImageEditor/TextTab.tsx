import React from 'react';
import { ImageState, ActionType } from '../../../../types/ImageEditor';

interface TextTabProps {
    currentState: ImageState;
    addToHistory: (newState: Partial<ImageState>, actionType: ActionType) => void;
}

const TextTab = ({ currentState, addToHistory }: Readonly<TextTabProps>) => (
    <div className="space-y-5">
        <h3 className="text-lg font-medium text-gray-900">Thêm văn bản</h3>
        <p className="text-gray-500">Nội dung tab thêm văn bản sẽ được thêm vào đây</p>
    </div>
);

export default TextTab;
