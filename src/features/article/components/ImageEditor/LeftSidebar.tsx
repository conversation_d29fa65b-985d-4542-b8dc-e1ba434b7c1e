import React from 'react';
import { Home, Type, Square } from 'react-feather';
import AspectRatioIcon from '@mui/icons-material/AspectRatio';

interface LeftSidebarProps {
    activeTab: string;
    onTabChange: (tabId: string) => void;
}

const LeftSidebar = ({ activeTab, onTabChange }: Readonly<LeftSidebarProps>) => {
    const tabs = [
        { id: 'info', label: 'Thông tin', icon: Home },
        { id: 'archive', label: 'Kích thước', icon: AspectRatioIcon },
        { id: 'text', label: 'Thêm văn bản', icon: Type },
        { id: 'frame', label: 'Khung', icon: Square },
    ];

    return (
        <div className="w-48 bg-white flex flex-col">
            {tabs.map((tab) => {
                const IconComponent = tab.icon;
                return (
                    <button
                        key={tab.id}
                        type="button"
                        onClick={() => onTabChange(tab.id)}
                        className={`flex flex-col items-center justify-center p-1 mx-2 transition-colors ${
                            activeTab === tab.id ? 'bg-[#FCF3F5] text-primary' : 'text-gray-600'
                        }`}
                    >
                        <IconComponent size={18} className="mb-1" />
                        <span className="text-xs text-center leading-tight">{tab.label}</span>
                    </button>
                );
            })}
        </div>
    );
};

export default LeftSidebar;
