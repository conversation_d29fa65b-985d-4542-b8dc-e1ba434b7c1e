import ReactTags from 'react-tag-autocomplete';
import Tag from 'types/Tag';
import handleKeyDown from './HandleTag';
import { FileResponse } from '../../../../types/common/Item';

interface InfoTabProps {
    description: string;
    tagAdds: string[];
    setTagAdds(tagAdds: string[]): void;
    tags: Tag[];
    onDescriptionChange: (value: string) => void;
    onTagsChange: (tags: string[]) => void;
    rootImg: FileResponse | null;
    onEditRootImage: (rootImg: FileResponse) => void;
}

const InfoTab = ({
    description,
    tagAdds,
    setTagAdds,
    tags,
    onDescriptionChange,
    onTagsChange,
    rootImg,
    onEditRootImage,
}: Readonly<InfoTabProps>) => (
    <>
        {/* Description Input */}
        <div className="mb-1">
            <label className="form-label"><PERSON><PERSON> thích</label>
            <input
                type="text"
                value={description}
                onChange={(e) => onDescriptionChange(e.target.value)}
                placeholder="Nhập chú thích"
                className="form-control"
            />
        </div>

        {/* Tags Input */}
        <div className="mt-1 mb-1">
            <div className="d-flex justify-content-between flex-md-row flex-column">
                <label className="form-label">Tags</label>
                <div className="d-flex">
                    <button
                        type="button"
                        className="btn btn-icon btn-sm btn-flat-danger waves-effect"
                        onClick={() => setTagAdds([])}
                    >
                        Xóa hết tag
                    </button>
                </div>
            </div>
            <div onKeyDown={handleKeyDown}>
                <ReactTags
                    tags={tagAdds.map((name, index) => ({ id: index, name }))}
                    suggestions={tags
                        .filter((tag) => typeof tag.id === 'number')
                        .map((tag) => ({ id: tag.id as number, name: tag.name }))}
                    onAddition={(tag) => {
                        if (!tagAdds.includes(tag.name)) {
                            setTagAdds([...tagAdds, tag.name]);
                        }
                    }}
                    onDelete={(i) => {
                        const newTags = [...tagAdds];
                        newTags.splice(i, 1);
                        setTagAdds(newTags);
                    }}
                />
            </div>
            <label className="form-label">Nhấn Enter để thêm tags</label>
        </div>
        {rootImg && (
            <div className="mb-1">
                <label className="form-label">Ảnh gốc</label>
                <img src={rootImg.file_url} alt="Ảnh gốc" className="w-[150px] h-auto rounded" />
                <button type="button" className="btn btn-primary btn-sm mt-1" onClick={() => onEditRootImage(rootImg)}>
                    Sửa ảnh gốc
                </button>
            </div>
        )}
    </>
);

export default InfoTab;
