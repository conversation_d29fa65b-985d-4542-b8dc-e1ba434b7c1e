import { ARTICLE_TAB, LIMIT_MAX, QUERY_KEY } from 'constants/common';
import { Plus, Trash2, X } from 'react-feather';
import { UseFormSetValue, UseFormWatch } from 'react-hook-form';
import Article, { ArticleIssueInput } from 'types/Article';
import Select, { SingleValue } from 'react-select';
import { Dispatch, SetStateAction, useEffect, useMemo, useState } from 'react';
import { useGraphQLQuery } from '../../../hooks/useGraphQLQuery';
import { PressPublicationQuery } from '../../../types/PressPublication';
import { PRESS_PUBLICATIONS_LIST } from '../../../services/PressPublicationSevice';
import { keepPreviousData } from '@tanstack/react-query';
import { IssueQuery } from '../../../types/Issue';
import { ISSUE_LIST } from '../../../services/IssueService';
import { ISSUE_PAGE_LIST } from '../../../services/IssuePageService';
import { ItemParamModel, ItemStatus, SelectOptionModel } from '../../../types/common/Item';
import { convertObjectToSelectOptions } from '../../../utils/common';
import { IssuePageQuery } from '../../../types/IssuePage';

interface ArticleIssuePageForm {
    issue_page_id: number;
    display_order: string;
    comment: string;
}

interface PublishPaperData {
    selectedPublicationId: number | null;
    selectedIssueId: number | null;
    articleIssuePages: ArticleIssuePageForm[];
}

interface UpdateArticleFormTabPublishPaperProps {
    setTabAction(tab: ARTICLE_TAB | null): void;
    setValue: UseFormSetValue<Article>;
    watch: UseFormWatch<Article>;
    departmentId: number;
    article?: Article;
    publishPaperData: PublishPaperData;
    setPublishPaperData: Dispatch<SetStateAction<PublishPaperData>>;
}

export const UpdateArticleFormTabPublishPaper = ({
    setTabAction,
    setValue,
    watch,
    departmentId,
    article,
    publishPaperData,
    setPublishPaperData,
}: UpdateArticleFormTabPublishPaperProps) => {
    // Watch form values to restore state when component mounts
    const formPressPublicationId = watch('press_publication_id');
    const formIssueId = watch('issue_id');
    const formArticleIssuePages = watch('article_issue_pages');

    // Create select option values for current selections
    const [publicationValue, setPublicationValue] = useState<SelectOptionModel>({ value: '', label: '' });
    const [issueValue, setIssueValue] = useState<SelectOptionModel>({ value: '', label: '' });

    // Initialize data from form values or existing article when editing
    useEffect(() => {
        // First priority: restore from form values (when switching back to tab)
        if (formPressPublicationId !== undefined && formPressPublicationId !== publishPaperData.selectedPublicationId) {
            setPublishPaperData((prev) => ({ ...prev, selectedPublicationId: formPressPublicationId }));
        }

        if (formIssueId !== undefined && formIssueId !== publishPaperData.selectedIssueId) {
            setPublishPaperData((prev) => ({ ...prev, selectedIssueId: formIssueId }));
        }

        // Restore from completed form pages
        if (formArticleIssuePages && formArticleIssuePages.length > 0) {
            const existingPages = formArticleIssuePages.map((page) => ({
                issue_page_id: page.issue_page_id,
                display_order: page.display_order.toString(),
                comment: page.comment ?? '',
            }));
            // Only update if the pages are different
            if (JSON.stringify(existingPages) !== JSON.stringify(publishPaperData.articleIssuePages)) {
                setPublishPaperData((prev) => ({ ...prev, articleIssuePages: existingPages }));
            }
        }
        // If no form pages but we have selectedIssueId, add default empty page
        else if (
            formIssueId &&
            (!formArticleIssuePages || formArticleIssuePages.length === 0) &&
            publishPaperData.articleIssuePages.length === 0
        ) {
            const defaultPage: ArticleIssuePageForm = {
                issue_page_id: 0,
                display_order: '',
                comment: '',
            };
            setPublishPaperData((prev) => ({ ...prev, articleIssuePages: [defaultPage] }));
        }

        // Note: Article data initialization is now handled in parent UpdateArticleForm component
    }, [setValue, formPressPublicationId, formIssueId, formArticleIssuePages, publishPaperData, setPublishPaperData]);

    // Sync form values with publishPaperData when component mounts (for editing)
    useEffect(() => {
        if (publishPaperData.selectedPublicationId && formPressPublicationId === undefined) {
            setValue('press_publication_id', publishPaperData.selectedPublicationId);
        }
        if (publishPaperData.selectedIssueId && formIssueId === undefined) {
            setValue('issue_id', publishPaperData.selectedIssueId);
        }
        if (
            publishPaperData.articleIssuePages.length > 0 &&
            (!formArticleIssuePages || formArticleIssuePages.length === 0)
        ) {
            const formattedPages = publishPaperData.articleIssuePages
                .filter((page) => page.issue_page_id && page.display_order)
                .map((page) => ({
                    issue_page_id: page.issue_page_id,
                    display_order: parseInt(page.display_order),
                    comment: page.comment,
                }));
            if (formattedPages.length > 0) {
                setValue('article_issue_pages', formattedPages);
            }
        }
    }, [publishPaperData, setValue, formPressPublicationId, formIssueId, formArticleIssuePages]);

    const baseFilters = useMemo(() => {
        const filters = [`status_id:=(${ItemStatus.ACTIVE})`];

        if (departmentId) {
            filters.push(`department_id:=(${departmentId})`);
        }

        return filters;
    }, [departmentId]);

    // Press Publications Query
    const { data: pressPublicationData } = useGraphQLQuery<PressPublicationQuery>(
        [QUERY_KEY.PRESS_PUBLICATIONS],
        PRESS_PUBLICATIONS_LIST,
        {
            page: 1,
            limit: LIMIT_MAX,
            search: '',
            filters: baseFilters,
        },
        '',
        {
            placeholderData: keepPreviousData,
        }
    );

    // Issues Query - filtered by selected publication
    const issueFilters = useMemo(() => {
        const filters = [...baseFilters];
        if (publishPaperData.selectedPublicationId) {
            filters.push(`press_publication_id:=(${publishPaperData.selectedPublicationId})`);
        }
        return filters;
    }, [publishPaperData.selectedPublicationId, baseFilters]);

    const { data: issueData } = useGraphQLQuery<IssueQuery>(
        [QUERY_KEY.ISSUES, publishPaperData.selectedPublicationId],
        ISSUE_LIST,
        {
            page: 1,
            limit: LIMIT_MAX,
            search: '',
            filters: issueFilters,
        },
        '',
        {
            enabled: !!publishPaperData.selectedPublicationId,
            placeholderData: keepPreviousData,
        }
    );

    // Issue Pages Query - filtered by selected issue
    const issuePageFilters = useMemo(() => {
        const filters = [...baseFilters];
        if (publishPaperData.selectedPublicationId) {
            filters.push(`press_publication_id:=(${publishPaperData.selectedPublicationId})`);
        }
        if (publishPaperData.selectedIssueId) {
            filters.push(`issue_id:=(${publishPaperData.selectedIssueId})`);
        }
        return filters;
    }, [baseFilters, publishPaperData.selectedIssueId, publishPaperData.selectedPublicationId]);

    const { data: issuePageData } = useGraphQLQuery<IssuePageQuery>(
        [QUERY_KEY.ISSUE_PAGES, publishPaperData.selectedIssueId],
        ISSUE_PAGE_LIST,
        {
            page: 1,
            limit: LIMIT_MAX,
            search: '',
            filters: issuePageFilters,
        },
        '',
        {
            enabled: !!publishPaperData.selectedIssueId,
            placeholderData: keepPreviousData,
        }
    );

    // Convert data to select options using the same pattern as UpdateArticleFormTabInformation
    const pressPublicationOptions = useMemo(
        () =>
            convertObjectToSelectOptions(
                (pressPublicationData?.press_publications_list.data.map((item) => ({
                    id: item.id?.toString(),
                    name: item.name,
                })) as ItemParamModel[]) || []
            ),
        [pressPublicationData]
    );

    const issueOptions = useMemo(
        () =>
            convertObjectToSelectOptions(
                (issueData?.issues_list.data.map((item) => ({
                    id: item.id?.toString(),
                    name: item.name,
                })) as ItemParamModel[]) || []
            ),
        [issueData]
    );

    const issuePageOptions = useMemo(
        () =>
            convertObjectToSelectOptions(
                (issuePageData?.issue_pages_list.data.map((item) => ({
                    id: item.id?.toString(),
                    name: item.name,
                })) as ItemParamModel[]) || []
            ),
        [issuePageData]
    );

    // Update select values when data changes
    useEffect(() => {
        if (publishPaperData.selectedPublicationId && pressPublicationOptions.length > 0) {
            const option = pressPublicationOptions.find(
                (opt) => opt.value === publishPaperData.selectedPublicationId?.toString()
            );
            if (option) setPublicationValue(option);
        }
    }, [publishPaperData.selectedPublicationId, pressPublicationOptions]);

    useEffect(() => {
        if (publishPaperData.selectedIssueId && issueOptions.length > 0) {
            const option = issueOptions.find((opt) => opt.value === publishPaperData.selectedIssueId?.toString());
            if (option) setIssueValue(option);
        }
    }, [publishPaperData.selectedIssueId, issueOptions]);

    // Handle publication change
    const handlePublicationChange = (option: SingleValue<SelectOptionModel>) => {
        const publicationId = option?.value ? Number(option.value) : null;
        setPublishPaperData((prev) => ({
            ...prev,
            selectedPublicationId: publicationId,
            selectedIssueId: null,
            articleIssuePages: [],
        }));
        setPublicationValue(option || { value: '', label: '' });
        setIssueValue({ value: '', label: '' });
        setValue('press_publication_id', publicationId ?? undefined);
        setValue('issue_id', undefined);
        setValue('article_issue_pages', undefined);
    };

    // Handle issue change
    const handleIssueChange = (option: SingleValue<SelectOptionModel>) => {
        const issueId = option?.value ? Number(option.value) : null;
        setIssueValue(option || { value: '', label: '' });
        setValue('issue_id', issueId ?? undefined);

        // Reset pages when issue changes and add default page if issue is selected
        if (issueId) {
            const defaultPage: ArticleIssuePageForm = {
                issue_page_id: 0,
                display_order: '',
                comment: '',
            };
            setPublishPaperData((prev) => ({
                ...prev,
                selectedIssueId: issueId,
                articleIssuePages: [defaultPage],
            }));
            setValue('article_issue_pages', undefined);
        } else {
            setPublishPaperData((prev) => ({
                ...prev,
                selectedIssueId: null,
                articleIssuePages: [],
            }));
            setValue('article_issue_pages', undefined);
        }
    };

    // Handle adding new page entry
    const handleAddPage = () => {
        const newPage: ArticleIssuePageForm = {
            issue_page_id: 0,
            display_order: '',
            comment: '',
        };
        const updatedPages = [...publishPaperData.articleIssuePages, newPage];
        setPublishPaperData((prev) => ({ ...prev, articleIssuePages: updatedPages }));
        // Update form value to ensure persistence when switching tabs
        updateFormValue(updatedPages);
    };

    // Handle removing page entry
    const handleRemovePage = (index: number) => {
        const updatedPages = publishPaperData.articleIssuePages.filter((_, i) => i !== index);
        setPublishPaperData((prev) => ({ ...prev, articleIssuePages: updatedPages }));
        updateFormValue(updatedPages);
    };

    // Handle page field changes
    const handlePageFieldChange = (index: number, field: keyof ArticleIssuePageForm, value: string | number) => {
        const updatedPages = [...publishPaperData.articleIssuePages];
        updatedPages[index] = { ...updatedPages[index], [field]: value };
        setPublishPaperData((prev) => ({ ...prev, articleIssuePages: updatedPages }));
        updateFormValue(updatedPages);
    };

    // Update form value
    const updateFormValue = (pages: ArticleIssuePageForm[]) => {
        // Save all pages to form for persistence, but only include valid ones in final submission
        const formattedPages: ArticleIssueInput[] = pages
            .filter((page) => page.issue_page_id && page.display_order)
            .map((page) => ({
                issue_page_id: page.issue_page_id,
                display_order: parseInt(page.display_order),
                comment: page.comment,
            }));
        setValue('article_issue_pages', formattedPages);
    };

    return (
        <div className="card !mb-0 max-h-[calc(100vh-200px)] min-h-[300px] overflow-y-auto overflow-x-hidden">
            <div className="card-header bg-[#FCF3F5FF] p-[.75rem] py-25">
                <h4 className="card-title !text-base text-[#A42D49FF]">Thông tin xuất bản</h4>
                <div className="heading-elements">
                    <ul className="mb-0 list-inline">
                        <li>
                            <X
                                size={24}
                                className="text-[#A42D49FF] pt-1.5 cursor-pointer"
                                onClick={() => setTabAction(null)}
                            />
                        </li>
                    </ul>
                </div>
            </div>
            <div>
                <div className="card-body p-[.75rem]">
                    <div className="mb-1">
                        <label className="form-label">Ấn Phẩm</label>
                        <Select
                            options={pressPublicationOptions}
                            onChange={handlePublicationChange}
                            value={publicationValue}
                            isClearable
                            placeholder="Chọn ấn phẩm"
                        />
                    </div>

                    <div className="mb-1">
                        <label className="form-label">Số</label>
                        <Select
                            options={issueOptions}
                            onChange={handleIssueChange}
                            value={issueValue}
                            isClearable
                            isDisabled={!publishPaperData.selectedPublicationId}
                            placeholder="Chọn số báo"
                        />
                    </div>

                    {publishPaperData.selectedIssueId && (
                        <div className="mb-1">
                            <div className="d-flex justify-content-end align-items-center mb-2">
                                <button
                                    type="button"
                                    className="btn btn-sm btn-outline-primary"
                                    onClick={handleAddPage}
                                >
                                    <Plus size={16} className="me-1" />
                                    Thêm
                                </button>
                            </div>

                            {publishPaperData.articleIssuePages.map((page, index) => (
                                <div key={index} className="border rounded p-1 mb-2">
                                    <div className="row">
                                        {index > 0 && (
                                            <div className="col-md-12 mb-1 d-flex align-items-end justify-content-end">
                                                <button
                                                    type="button"
                                                    className="btn btn-sm btn-outline-danger"
                                                    onClick={() => handleRemovePage(index)}
                                                >
                                                    <Trash2 size={16} />
                                                </button>
                                            </div>
                                        )}
                                        <div className="col-md-12 mb-1">
                                            <label className="form-label">Trang</label>
                                            <Select
                                                options={issuePageOptions}
                                                onChange={(option) =>
                                                    handlePageFieldChange(
                                                        index,
                                                        'issue_page_id',
                                                        Number(option?.value) || 0
                                                    )
                                                }
                                                value={
                                                    issuePageOptions.find(
                                                        (option) => option.value === page.issue_page_id.toString()
                                                    ) || null
                                                }
                                                isClearable
                                                placeholder="Chọn trang"
                                            />
                                        </div>
                                        <div className="col-md-12 mb-1">
                                            <label className="form-label">STT</label>
                                            <input
                                                type="number"
                                                className="form-control"
                                                value={page.display_order}
                                                onChange={(e) =>
                                                    handlePageFieldChange(index, 'display_order', e.target.value)
                                                }
                                                placeholder="Nhập STT"
                                                min="1"
                                            />
                                        </div>
                                        <div className="col-md-12 mb-1">
                                            <label className="form-label">Mô tả</label>
                                            <input
                                                type="text"
                                                className="form-control"
                                                value={page.comment}
                                                onChange={(e) =>
                                                    handlePageFieldChange(index, 'comment', e.target.value)
                                                }
                                                placeholder="Nhập mô tả"
                                            />
                                        </div>
                                    </div>
                                </div>
                            ))}

                            {publishPaperData.articleIssuePages.length === 0 && publishPaperData.selectedIssueId && (
                                <div className="text-center text-muted py-3">Đang tải dữ liệu trang...</div>
                            )}
                        </div>
                    )}
                </div>
            </div>
        </div>
    );
};
