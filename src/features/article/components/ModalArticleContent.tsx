import React, { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { Button, Table } from 'reactstrap';
import { useGraphQLQuery } from '../../../hooks/useGraphQLQuery';
import { SearchWorkflow, WorkflowQuery } from '../../../types/Workflow';
import { LIMIT_MAX, OPERATION_NAME, PAGINATION, QUERY_KEY } from '../../../constants/common';
import { WORKFLOW_LIST } from '../../../services/WorkflowService';
import { ArticleType, ArticleTypeNames, ItemStatus } from '../../../types/common/Item';
import { useAppStore } from '../../../stores/appStore';
import { keepPreviousData } from '@tanstack/react-query';
import { ARTICLE_LIST } from '../../../services/ArticleService';
import { ArticleQueryRes, SearchArticle } from '../../../types/Article';
import { convertDataToSelectOptions } from '../../../utils/common';
import PaginationTable from '../../../components/partials/PaginationTable';
import Spinner from '../../../components/partials/Spinner';
import Select from 'react-select';
import { useTranslation } from 'react-i18next';

interface SearchFormData {
    title: string;
    department_id: string;
    workflow_id: number[];
    article_type_id: number;
}

interface IProps {
    onSelectedArticles?: (articleIds: number[]) => void;
    onSubmitArticle?: () => void;
    onCancelArticle?: () => void;
}

export default function ModalArticleContent({
    onSelectedArticles,
    onSubmitArticle,
    onCancelArticle,
}: Readonly<IProps>) {
    const { t } = useTranslation();
    const departmentId = useAppStore((state) => state.departmentId);
    const [page, setPage] = useState(1);
    const [selectedArticleIds, setSelectedArticleIds] = useState<number[]>([]);
    const [selectAll, setSelectAll] = useState(false);
    const [searchParams, setSearchParams] = useState<SearchFormData>({
        title: '',
        department_id: '',
        workflow_id: [],
        article_type_id: ArticleType.PAPER,
    });

    const { register, handleSubmit, reset, watch, setValue } = useForm<SearchFormData>({
        defaultValues: {
            title: '',
            department_id: '',
            workflow_id: [],
            article_type_id: ArticleType.PAPER,
        },
    });

    const formData = watch();

    const { data: workflowData } = useGraphQLQuery<WorkflowQuery, SearchWorkflow>(
        [QUERY_KEY.WORKFLOWS],
        WORKFLOW_LIST,
        {
            page: 1,
            limit: LIMIT_MAX,
            search: '',
            filters: [`status_id:=(${ItemStatus.ACTIVE})`, `department_id:=(${departmentId})`],
            sorts: ['display_order:ASC'],
        },
        '',
        {
            enabled: !!departmentId,
            placeholderData: keepPreviousData,
        }
    );

    const workflows = workflowData?.workflows_list?.data || [];

    const {
        data: articleData,
        isLoading,
        refetch,
    } = useGraphQLQuery<ArticleQueryRes, SearchArticle>(
        [QUERY_KEY.ARTICLES, searchParams, page],
        ARTICLE_LIST,
        {
            page,
            limit: PAGINATION.limit,
            search: searchParams.title || undefined,
            filters: [
                `article_type_id:=(${searchParams.article_type_id})`,
                `department_id:=(${departmentId})`,
                ...(searchParams.workflow_id.length > 0
                    ? [`workflow_id:=(${searchParams.workflow_id.join(',')})`]
                    : []),
            ].filter(Boolean),
        },
        OPERATION_NAME.CALL_STATIC_TOKEN,
        {
            enabled: !!departmentId,
            placeholderData: keepPreviousData,
        }
    );

    const articles = articleData?.articles_list?.data || [];

    const onSubmit = (data: SearchFormData) => {
        setSearchParams(data);
        setPage(1);
        setSelectedArticleIds([]);
        setSelectAll(false);
        setTimeout(() => {
            refetch();
        }, 0);
    };

    const handleReset = () => {
        const resetData = {
            title: '',
            department_id: '',
            workflow_id: [],
            article_type_id: ArticleType.PAPER,
        };
        reset(resetData);
        setSearchParams(resetData);
        setPage(1);
        setSelectedArticleIds([]);
        setSelectAll(false);
        setTimeout(() => {
            refetch();
        }, 0);
    };

    const handlePageChange = (_event: React.ChangeEvent<unknown>, newPage: number) => {
        setPage(newPage);
        setSelectedArticleIds([]);
        setSelectAll(false);
    };

    useEffect(() => {
        if (page > 1) {
            refetch();
        }
    }, [page, refetch]);

    const handleCheckboxChange = (articleId: number, checked: boolean) => {
        if (checked) {
            setSelectedArticleIds((prev) => [...prev, articleId]);
        } else {
            setSelectedArticleIds((prev) => prev.filter((id) => id !== articleId));
            setSelectAll(false);
        }
    };

    const handleSelectAll = (checked: boolean) => {
        setSelectAll(checked);
        if (checked) {
            setSelectedArticleIds(articles.map((article) => article.id).filter((id): id is number => id !== undefined));
        } else {
            setSelectedArticleIds([]);
        }
    };

    useEffect(() => {
        if (onSelectedArticles) {
            onSelectedArticles(selectedArticleIds);
        }
    }, [selectedArticleIds, onSelectedArticles]);

    useEffect(() => {
        if (articles.length > 0) {
            setSelectAll(selectedArticleIds.length === articles.length);
        }
    }, [selectedArticleIds, articles.length]);

    return (
        <div className="p-2">
            <form onSubmit={handleSubmit(onSubmit)} className="mb-3">
                <div className="row">
                    <div className="col-md-4 col-12 mb-2">
                        <label className="form-label">Tiêu đề</label>
                        <input
                            type="text"
                            className="form-control"
                            placeholder="Nhập tiêu đề..."
                            {...register('title')}
                        />
                    </div>
                    <div className="col-md-4 col-12 mb-2">
                        <label className="form-label">Phòng ban</label>
                        <select className="form-control" {...register('department_id')}>
                            <option value="">Chọn phòng ban</option>
                            {/* TODO: Add department options when API is available */}
                        </select>
                    </div>
                    <div className="col-md-4 col-12 mb-2">
                        <label className="form-label">Trạng thái</label>
                        <Select
                            isMulti
                            options={convertDataToSelectOptions(workflows, 'id', 'name', undefined, null)}
                            value={convertDataToSelectOptions(workflows, 'id', 'name', undefined, null).filter(
                                (option) => formData.workflow_id.includes(option.value)
                            )}
                            onChange={(selectedOptions) => {
                                setValue(
                                    'workflow_id',
                                    selectedOptions ? selectedOptions.map((option) => option.value) : []
                                );
                            }}
                            placeholder="Chọn trạng thái..."
                            className="react-select-container"
                            classNamePrefix="react-select"
                        />
                    </div>
                </div>
                <div className="row">
                    <div className="col-12">
                        <Button type="submit" color="primary" className="me-2">
                            Tìm kiếm
                        </Button>
                        <Button type="button" color="secondary" onClick={handleReset}>
                            Đặt lại
                        </Button>
                    </div>
                </div>
                <input type="hidden" {...register('article_type_id')} />
            </form>

            {isLoading && <Spinner />}

            {!isLoading && (
                <div className="card">
                    <div className="card-body">
                        <Table responsive>
                            <thead>
                                <tr>
                                    <th style={{ width: '50px' }}>
                                        <div className="form-check">
                                            <input
                                                className="form-check-input"
                                                type="checkbox"
                                                checked={selectAll}
                                                onChange={(e) => handleSelectAll(e.target.checked)}
                                            />
                                        </div>
                                    </th>
                                    <th>Tiêu đề</th>
                                    <th>Thể loại tin</th>
                                    <th>Chuyên mục</th>
                                    <th>Biên tập viên</th>
                                    <th>Trạng thái</th>
                                </tr>
                            </thead>
                            <tbody>
                                {articles.length === 0 ? (
                                    <tr>
                                        <td colSpan={6} className="text-center">
                                            Không có dữ liệu để hiển thị
                                        </td>
                                    </tr>
                                ) : (
                                    articles.map((article) => (
                                        <tr key={article.id}>
                                            <td>
                                                <div className="form-check">
                                                    <input
                                                        className="form-check-input"
                                                        type="checkbox"
                                                        checked={selectedArticleIds.includes(article.id!)}
                                                        onChange={(e) =>
                                                            handleCheckboxChange(article.id!, e.target.checked)
                                                        }
                                                    />
                                                </div>
                                            </td>
                                            <td>
                                                <div>{article.title}</div>
                                                <div className="text-muted small">
                                                    {article.pseudonym?.name || 'Chưa có bút danh'}
                                                </div>
                                            </td>
                                            <td>
                                                {t(
                                                    `${
                                                        ArticleTypeNames.find(
                                                            (type) => type.id === article.article_type_id
                                                        )?.name ?? 'unclassified'
                                                    }.single`
                                                )}
                                            </td>
                                            <td>
                                                {article.articleCategories && article.articleCategories.length > 0
                                                    ? article.articleCategories
                                                          .map((cat) => cat.category?.name)
                                                          .filter(Boolean)
                                                          .join(', ')
                                                    : 'Chưa có chuyên mục'}
                                            </td>
                                            <td>Biên tập viên</td>
                                            <td>
                                                <span className="badge bg-primary">
                                                    {article.workflow?.name || 'Chưa có trạng thái'}
                                                </span>
                                            </td>
                                        </tr>
                                    ))
                                )}
                            </tbody>
                        </Table>

                        {articleData && (
                            <PaginationTable
                                countItem={articleData.articles_list.totalCount}
                                totalPage={articleData.articles_list.totalPages}
                                currentPage={articleData.articles_list.currentPage}
                                handlePageChange={handlePageChange}
                            />
                        )}
                    </div>
                    <div className="card-footer">
                        <div className="d-flex justify-content-end">
                            <Button
                                color="primary"
                                onClick={onSubmitArticle}
                                type="button"
                                disabled={!selectedArticleIds.length}
                            >
                                Chọn
                            </Button>
                            <Button color="secondary" onClick={onCancelArticle} className="ms-2" type="button">
                                Huỷ
                            </Button>
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
}
