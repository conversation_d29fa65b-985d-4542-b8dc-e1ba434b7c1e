import React, { useState, useEffect } from 'react';
import { ChevronDown, ChevronRight } from 'react-feather';
import Category from '../../../types/Category';
import { ItemStatus } from '../../../types/common/Item';
import classNames from 'classnames';

export interface CategoryItem {
    category_id: number;
    display_order: number;
    is_major: boolean;
}

interface CategoryListProps {
    categories: Category[];
    isMajor: boolean;
    onChange: (selectedCategories: CategoryItem[]) => void;
    defaultSelectedCategories?: CategoryItem[]; // Prop mới, optional
}

export default function CategoryListCheckbox({
    categories,
    isMajor,
    onChange,
    defaultSelectedCategories = [],
}: Readonly<CategoryListProps>) {
    const [expandedCategories, setExpandedCategories] = useState<number[]>([]);
    const [selectedCategories, setSelectedCategories] = useState<CategoryItem[]>([]);

    // Tạo unique name cho radio group
    const radioGroupName = `majorCategory_${isMajor ? 'main' : 'sub'}`;

    useEffect(() => {
        setSelectedCategories(defaultSelectedCategories);
    }, [defaultSelectedCategories]);

    const filteredCategories = categories.filter((category) => category.is_major === isMajor && !category.parent_id);

    // Tự động mở rộng các danh mục có con được chọn
    useEffect(() => {
        const findParentCategory = (childId: number): Category | undefined => {
            for (const category of categories) {
                if (category.children && category.children.some((child) => child.id === childId)) {
                    return category;
                }
            }
            return undefined;
        };
        if (defaultSelectedCategories.length > 0) {
            const categoryIdsToExpand: number[] = [];

            // Tìm tất cả các danh mục cha có con được chọn
            defaultSelectedCategories.forEach((item) => {
                const parent = findParentCategory(item.category_id);
                if (parent && parent.id) {
                    categoryIdsToExpand.push(parent.id);
                }
            });

            setExpandedCategories(categoryIdsToExpand);
        }
    }, [defaultSelectedCategories, categories]);

    const toggleExpand = (categoryId: number) => {
        setExpandedCategories((prev) =>
            prev.includes(categoryId) ? prev.filter((id) => id !== categoryId) : [...prev, categoryId]
        );
    };

    const isExpanded = (categoryId: number) => expandedCategories.includes(categoryId);

    const isSelected = (categoryId: number) => selectedCategories.some((item) => item.category_id === categoryId);

    const getDisplayOrder = (categoryId: number) => {
        const item = selectedCategories.find((item) => item.category_id === categoryId);
        return item ? item.display_order : 1;
    };

    const getIsMajor = (categoryId: number) => {
        const item = selectedCategories.find((item) => item.category_id === categoryId);
        return item ? item.is_major : false;
    };

    const findParentCategory = (childId: number): Category | undefined => {
        for (const category of categories) {
            if (category.children && category.children.some((child) => child.id === childId)) {
                return category;
            }
        }
        return undefined;
    };

    const handleCheckboxChange = (category: Category, checked: boolean) => {
        let newSelectedCategories = [...selectedCategories];

        if (checked) {
            // Thêm category hiện tại nếu chưa được chọn
            if (!isSelected(category.id!)) {
                newSelectedCategories.push({ category_id: category.id!, display_order: 1, is_major: false });
            }

            // Thêm tất cả children nếu có
            if (category.children && category.children.length > 0) {
                category.children.forEach((child) => {
                    if (!isSelected(child.id!)) {
                        newSelectedCategories.push({ category_id: child.id!, display_order: 1, is_major: false });
                    }
                });
            }

            // Thêm parent nếu đây là một child category
            const parent = findParentCategory(category.id!);
            if (parent && !isSelected(parent.id!)) {
                newSelectedCategories.push({ category_id: parent.id!, display_order: 1, is_major: false });
            }
        } else {
            // Xóa category hiện tại
            newSelectedCategories = newSelectedCategories.filter((item) => item.category_id !== category.id);

            // Xóa tất cả children nếu có
            if (category.children && category.children.length > 0) {
                category.children.forEach((child) => {
                    newSelectedCategories = newSelectedCategories.filter((item) => item.category_id !== child.id);
                });
            }

            // Kiểm tra xem có nên xóa parent không
            const parent = findParentCategory(category.id!);
            if (parent) {
                // Nếu không còn child nào được chọn, xóa parent
                const otherChildrenSelected = parent.children!.some(
                    (child) => child.id !== category.id && isSelected(child.id!)
                );

                if (!otherChildrenSelected) {
                    newSelectedCategories = newSelectedCategories.filter((item) => item.category_id !== parent.id);
                }
            }
        }

        setSelectedCategories(newSelectedCategories);
        onChange(newSelectedCategories);
    };

    const handleDisplayOrderChange = (categoryId: number, value: number) => {
        const newSelectedCategories = selectedCategories.map((item) =>
            item.category_id === categoryId ? { ...item, display_order: value } : item
        );
        setSelectedCategories(newSelectedCategories);
        onChange(newSelectedCategories);
    };

    const handleIsMajorChange = (categoryId: number, isMajor: boolean) => {
        const newSelectedCategories = selectedCategories.map(
            (item) => (item.category_id === categoryId ? { ...item, is_major: isMajor } : { ...item, is_major: false }) // Chỉ một chuyên mục có thể là major
        );
        setSelectedCategories(newSelectedCategories);
        onChange(newSelectedCategories);
    };

    const renderCategoryRow = (category: Category, level: number = 0): JSX.Element => {
        let categoryChild: Category[] = [];
        if (category.children) {
            categoryChild = category.children.filter((child) => child.status_id === ItemStatus.ACTIVE);
        }
        const hasChildren = categoryChild.length > 0;

        return (
            <React.Fragment key={category.id}>
                <tr>
                    <td className="px-1">
                        <div className="d-flex align-items-center" style={{ paddingLeft: `${level * 30}px` }}>
                            {hasChildren && (
                                <button
                                    type="button"
                                    className="btn btn-sm btn-icon p-0 mr-1"
                                    onClick={() => toggleExpand(category.id!)}
                                >
                                    {isExpanded(category.id!) ? <ChevronDown size={16} /> : <ChevronRight size={16} />}
                                </button>
                            )}
                            <div
                                className={classNames('form-check', {
                                    'ml-6': !hasChildren && !category.parent_id,
                                })}
                            >
                                <input
                                    checked={isSelected(category.id!)}
                                    onChange={(e) => handleCheckboxChange(category, e.target.checked)}
                                    className="form-check-input"
                                    type="checkbox"
                                    id={`cbCategory${category.id}`}
                                    value={category.id}
                                />
                                <label className="form-check-label mb-0" htmlFor={`cbCategory${category.id}`}>
                                    {category.name}
                                </label>
                            </div>
                        </div>
                    </td>
                    <td className="text-center py-25 px-1">
                        <div className="form-check d-flex justify-content-center">
                            <input
                                checked={getIsMajor(category.id!)}
                                onChange={(e) => handleIsMajorChange(category.id!, e.target.checked)}
                                disabled={!isSelected(category.id!)}
                                className="form-check-input"
                                type="radio"
                                name={radioGroupName}
                                id={`radioMajor${category.id}`}
                            />
                        </div>
                    </td>
                    <td className="text-center py-25 px-1">
                        <select
                            value={getDisplayOrder(category.id!)}
                            onChange={(e) => handleDisplayOrderChange(category.id!, parseInt(e.target.value))}
                            disabled={!isSelected(category.id!)}
                            className="form-select form-control"
                        >
                            {Array.from({ length: 99 }, (_, i) => i + 1).map((num) => (
                                <option key={num} value={num}>
                                    {num}
                                </option>
                            ))}
                        </select>
                    </td>
                </tr>

                {/* Render children if expanded */}
                {hasChildren &&
                    isExpanded(category.id!) &&
                    categoryChild!.map((child) => renderCategoryRow(child, level + 1))}
            </React.Fragment>
        );
    };

    return <>{filteredCategories.map((category) => renderCategoryRow(category))}</>;
}
