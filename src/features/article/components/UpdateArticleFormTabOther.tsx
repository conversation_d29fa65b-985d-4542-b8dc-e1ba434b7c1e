import FileUpload from 'components/partials/FileUpload';
import PaginationTable from 'components/partials/PaginationTable';
import { ARTICLE_TAB, OPERATION_NAME, QUERY_KEY } from 'constants/common';
import { useGraphQLQuery } from 'hooks/useGraphQLQuery';
import { isUndefined, omitBy } from 'lodash';
import { useEffect, useState } from 'react';
import { X } from 'react-feather';
import { ARTICLE_LIST } from 'services/ArticleService';
import Article, { SearchArticleParam, ArticleQueryRes, SearchArticle, articleFilterConfig } from 'types/Article';
import { generateFilters, getArticlePageType } from 'utils/common';
import Spinner from '../../../components/partials/Spinner';
import { FORMAT_DATE, formatDateTime } from '../../../utils/date';
import { Link } from 'react-router-dom';
import { useAuthStore } from '../../../stores/authStore';

interface UpdateArticleFormTabOtherProps {
    onChangeFileArticle: (file: File) => void;
    setTabAction(tab: ARTICLE_TAB | null): void;
}

export const UpdateArticleFormTabOther = ({ onChangeFileArticle, setTabAction }: UpdateArticleFormTabOtherProps) => {
    const currentUser = useAuthStore((state) => state.user);
    const [title, setTitle] = useState('');
    const [duplicateArticles, setDuplicateArticles] = useState<Article[]>([]);
    const [currentPage, setCurrentPage] = useState(1);
    const [searchTitle, setSearchTitle] = useState('');
    const [shouldFetch, setShouldFetch] = useState(false);
    const pageSize = 5;

    const paramConfig: SearchArticleParam = omitBy(
        {
            limit: pageSize,
            page: currentPage.toString(),
            title: searchTitle,
        },
        isUndefined
    );
    const { search, limit, page, ...dataParamConfig } = paramConfig;
    const filters = generateFilters(dataParamConfig, articleFilterConfig);

    const { data, isLoading: isLoadingDuplicateArticle } = useGraphQLQuery<ArticleQueryRes, SearchArticle>(
        [QUERY_KEY.ARTICLES, paramConfig, filters],
        ARTICLE_LIST,
        {
            page: Number(page),
            limit: pageSize,
            search,
            filters: filters.length > 0 ? filters : undefined,
        },
        OPERATION_NAME.CALL_STATIC_TOKEN,
        {
            enabled: shouldFetch && !!searchTitle,
        }
    );

    useEffect(() => {
        if (data && shouldFetch) {
            const allArticles = data?.articles_list.data ?? [];
            setDuplicateArticles(allArticles);
        }
    }, [data, shouldFetch]);

    const handlePageChange = (_event: React.ChangeEvent<unknown>, page: number) => {
        setCurrentPage(page);
    };

    const onCheckDuplicate = async () => {
        if (!title) {
            setDuplicateArticles([]);
            setShouldFetch(false);
            return;
        }
        setSearchTitle(title);
        setCurrentPage(1);
        setShouldFetch(true);
    };

    return (
        <div className="card !mb-0">
            <div className="card-header bg-[#FCF3F5FF] p-[.75rem] py-25">
                <h4 className="card-title !text-base text-[#A42D49FF]">Khác</h4>
                <div className="heading-elements">
                    <ul className="mb-0 list-inline">
                        <li>
                            <X
                                size={24}
                                className="text-[#A42D49FF] pt-1.5 cursor-pointer"
                                onClick={() => setTabAction(null)}
                            />
                        </li>
                    </ul>
                </div>
            </div>
            <div>
                <div className="card-body p-[.75rem]">
                    <div className="mt-1 mb-1">
                        <label className="form-label">Lấy tin bài tự động</label>
                        <input className="form-control" type="text" placeholder="Nhập link URL bài viết" />
                    </div>
                    <div className="mb-1 d-flex justify-content-end">
                        <button
                            type="button"
                            className="btn btn-sm waves-effect waves-float waves-light !bg-[#FCF3F5FF] rounded-[4px] !text-[#A42D49FF]"
                        >
                            Lấy tin
                        </button>
                    </div>
                    <div className="mb-1">
                        <div className="d-flex justify-content-between flex-md-row flex-column">
                            <label className="form-label leading-[22px]">Khôi phục bài viết</label>
                            <button
                                type="button"
                                className="btn btn-sm btn-primary waves-effect waves-float waves-light"
                            >
                                Khôi phục
                            </button>
                        </div>
                    </div>
                    <div className="mb-1">
                        <label className="form-label">Lọc trùng bài viết</label>
                        <input
                            className="form-control"
                            type="text"
                            placeholder="Nhập tiêu đề bài viết"
                            onChange={(e) => setTitle(e.target.value)}
                        />
                    </div>
                    <div className="mb-1 d-flex justify-content-end">
                        <button
                            type="button"
                            className="btn btn-sm waves-effect waves-float waves-light !bg-[#FCF3F5FF] rounded-[4px] !text-[#A42D49FF]"
                            onClick={onCheckDuplicate}
                        >
                            Kiểm tra
                        </button>
                    </div>
                    {isLoadingDuplicateArticle && <Spinner />}
                    {!isLoadingDuplicateArticle && duplicateArticles.length > 0 && (
                        <>
                            <div className="w-full rounded-md border bg-white shadow-sm mt-1">
                                <div className="px-1 py-1 border-b font-semibold bg-gray-100">Bài viết</div>

                                <div className="divide-y">
                                    {duplicateArticles.map((article) => (
                                        <Link
                                            to={`/article/edit/${getArticlePageType(article, currentUser!)}/${
                                                article.article_type_id
                                            }/${article.id}`}
                                            target="_blank"
                                            key={article.id}
                                            className="flex gap-2 px-1 py-1 hover:bg-gray-50"
                                        >
                                            <div className="flex-1">
                                                <div className="font-medium text-sm">{article.title}</div>
                                                <p className="text-xs italic">
                                                    <span className="text-green-400">{article.pseudonym?.name}</span>{' '}
                                                    {!!article.publish_date && (
                                                        <>
                                                            {'- '}
                                                            <span className="text-gray-400">
                                                                {formatDateTime(
                                                                    article.publish_date,
                                                                    FORMAT_DATE.SHOW_ONLY_DATE
                                                                )}
                                                            </span>
                                                        </>
                                                    )}
                                                </p>
                                            </div>
                                        </Link>
                                    ))}
                                </div>
                            </div>
                            <PaginationTable
                                countItem={data?.articles_list.totalCount ?? 0}
                                totalPage={data?.articles_list.totalPages ?? 0}
                                currentPage={currentPage}
                                handlePageChange={handlePageChange}
                            />
                        </>
                    )}
                    <div className="mb-1 mt-2">
                        <label className="form-label">Import file bài viết</label>
                        <FileUpload type="document" onFileChange={onChangeFileArticle} />
                    </div>
                </div>
            </div>
        </div>
    );
};
