import { ARTICLE_TAB } from 'constants/common';
import { X } from 'react-feather';
import ReactTags from 'react-tag-autocomplete';
import Tag from 'types/Tag';
import Spinner from 'components/partials/Spinner';
import { useLayoutEffect, useRef } from 'react';
import '../../../../public/assets/css/style.css';
import handleKeyDown from './ImageEditor/HandleTag';

interface UpdateArticleFormTabSeoProps {
    setTabAction(tab: ARTICLE_TAB | null): void;

    tagAdds: string[];

    setTagAdds(tagAdds: string[]): void;

    tags: Tag[];

    onAddTag(tagname: string): void;

    onSuggestTags?(): void;

    isLoadingSuggestion?: boolean;
    onReachBottom?: () => void;
}

export const UpdateArticleFormTabSeo = ({
    setTabAction,
    tagAdds,
    setTagAdds,
    tags,
    onAddTag,
    onSuggestTags,
    isLoadingSuggestion,
    onReachBottom,
}: UpdateArticleFormTabSeoProps) => {
    const scrollRef = useRef<HTMLDivElement>(null);

    useLayoutEffect(() => {
        const handleScroll = () => {
            const el = scrollRef.current;
            if (!el) return;

            const isBottom = el.scrollTop + el.clientHeight >= el.scrollHeight - 10;
            if (isBottom) {
                onReachBottom?.();
            }
        };

        const el = scrollRef.current;
        el?.addEventListener('scroll', handleScroll);
        return () => el?.removeEventListener('scroll', handleScroll);
    }, [onReachBottom]);

    return (
        <div className="card !mb-0">
            {isLoadingSuggestion && (
                <div
                    className="position-absolute w-100 h-100 d-flex justify-content-center align-items-center"
                    style={{
                        backgroundColor: 'rgba(255, 255, 255, 0.8)',
                        zIndex: 1050,
                        top: 0,
                        left: 0,
                    }}
                >
                    <Spinner />
                </div>
            )}
            <div className="card-header bg-[#FCF3F5FF] p-[.75rem] py-25">
                <h4 className="card-title !text-base text-[#A42D49FF]">Thông tin SEO</h4>
                <div className="heading-elements">
                    <ul className="mb-0 list-inline">
                        <li>
                            <X
                                size={24}
                                className="text-[#A42D49FF] pt-1.5 cursor-pointer"
                                onClick={() => setTabAction(null)}
                            />
                        </li>
                    </ul>
                </div>
            </div>
            <div>
                <div className="card-body p-[.75rem]">
                    <div className="mt-1 mb-1">
                        <div className="d-flex justify-content-between flex-md-row flex-column">
                            <label className="form-label">Tags</label>
                            <div className="d-flex">
                                <button
                                    type="button"
                                    className="btn btn-icon btn-sm btn-flat-danger waves-effect"
                                    onClick={() => setTagAdds([])}
                                >
                                    Xóa hết tag
                                </button>
                                <button
                                    type="button"
                                    className="btn btn-icon btn-sm btn-flat-primary waves-effect"
                                    onClick={() => onSuggestTags && onSuggestTags()}
                                >
                                    Gợi ý tag
                                </button>
                            </div>
                        </div>
                        <div onKeyDown={handleKeyDown}>
                            <ReactTags
                                tags={tagAdds.map((name, index) => ({ id: index, name }))}
                                suggestions={tags
                                    .filter((tag) => typeof tag.id === 'number')
                                    .map((tag) => ({ id: tag.id as number, name: tag.name }))}
                                onAddition={(tag) => {
                                    if (!tagAdds.includes(tag.name)) {
                                        setTagAdds([...tagAdds, tag.name]);
                                    }
                                }}
                                onDelete={(i) => {
                                    const newTags = [...tagAdds];
                                    newTags.splice(i, 1);
                                    setTagAdds(newTags);
                                }}
                            />
                        </div>
                        <label className="form-label">Nhấn Enter để thêm tags</label>
                    </div>
                    {/* <div className="badge-wrapper">
                        {tags.map((_item) => (
                            <span
                                key={_item.id}
                                className="cursor-pointer badge rounded-pill badge-light-danger me-50 mb-50"
                                onClick={() => onAddTag(_item.name)}
                            >
                                {_item.name}
                            </span>
                        ))}
                    </div> */}
                    {/* <div className="mt-1">
                        <h4 className="card-title py-50 mb-0 px-3 !text-base !font-normal bg-[#F8F9FA]">Cơ bản</h4>
                        <div className="p-[.75rem]">
                            <p className="mb-50">
                                <Check size={14} className="me-25 bg-[#4AB96F] text-white inline-block" /> Tiêu đề chứa
                                ít nhất một tag.
                            </p>
                            <p className="mb-50">
                                <X size={14} className="me-25 bg-danger text-white inline-block" /> Mô tả chứa ít nhất
                                một tag.
                            </p>
                            <p className="mb-50">
                                <X size={14} className="me-25 bg-danger text-white inline-block" /> Đường dẫn URL bài
                                viết chứa tag.
                            </p>
                            <p className="mb-50">
                                <X size={14} className="me-25 bg-danger text-white inline-block" /> Tag nằm trong 10%
                                nội dung đầu của bài viết.
                            </p>
                            <p className="mb-50">
                                <X size={14} className="me-25 bg-danger text-white inline-block" /> Tag nằm trong nội
                                dung của bài viết.
                            </p>
                            <p className="mb-50">
                                <X size={14} className="me-25 bg-danger text-white inline-block" /> Nội dung bài viết
                                đạt đủ độ dài. Nội dung nên đạt trên 2550 ký tự.
                            </p>
                        </div>
                    </div>
                    <div>
                        <h4 className="card-title py-50 mb-0 px-3 !text-base !font-normal bg-[#F8F9FA]">Bổ sung</h4>
                        <div className="p-[.75rem]">
                            <p className="mb-50">
                                <Check size={14} className="me-25 bg-[#4AB96F] text-white inline-block" /> Tag nằm trong
                                thẻ subheading.
                            </p>
                            <p className="mb-50">
                                <X size={14} className="me-25 bg-danger text-white inline-block" /> Tag nằm trong chú
                                thích hình ảnh.
                            </p>
                            <p className="mb-50">
                                <X size={14} className="me-25 bg-danger text-white inline-block" /> Tag chiếm hơn 1% nội
                                dung bài viết.
                            </p>
                            <p className="mb-50">
                                <X size={14} className="me-25 bg-danger text-white inline-block" /> Đường dẫn URL có
                                lượng ký tự dài trên 50 ký tự.
                            </p>
                            <p className="mb-50">
                                <X size={14} className="me-25 bg-danger text-white inline-block" /> Trong bài viết chứa
                                đường dẫn bên ngoài trang.
                            </p>
                            <p className="mb-50">
                                <X size={14} className="me-25 bg-danger text-white inline-block" /> Trong bài viết chứa
                                đường dẫn nội bộ trong trang.
                            </p>
                            <p className="mb-50">
                                <X size={14} className="me-25 bg-danger text-white inline-block" /> Trong bài viết chứa
                                đường dẫn nội bộ trong trang.
                            </p>
                            <p className="mb-50">
                                <X size={14} className="me-25 bg-danger text-white inline-block" /> Tag xuất hiện trong
                                bài viết chưa được tối ưu ở bất kỳ bài viết nào trên hệ thống.
                            </p>
                        </div>
                    </div> */}
                    {/* <div>
                        <h4 className="card-title py-50 mb-0 px-3 !text-base !font-normal bg-[#F8F9FA]">
                            Tiêu đề dễ đọc
                        </h4>
                        <div className="p-[.75rem]">
                            <p className="mb-50">
                                <Check size={14} className="me-25 bg-[#4AB96F] text-white inline-block" /> Xuất hiện tag
                                trong tiêu đề.
                            </p>
                            <p className="mb-50">
                                <X size={14} className="me-25 bg-danger text-white inline-block" /> Trong tiêu đề chứa
                                ít nhất một số.
                            </p>
                        </div>
                    </div>
                    <div>
                        <h4 className="card-title py-50 mb-0 px-3 !text-base !font-normal bg-[#F8F9FA]">
                            Nội dung dễ đọc
                        </h4>
                        <div className="p-[.75rem]">
                            <p className="mb-50">
                                <X size={14} className="me-25 bg-danger text-white inline-block" /> Sử dụng mục lục cho
                                bài viết và nội dung đánh dấu đầu dòng.
                            </p>
                            <p className="mb-50">
                                <X size={14} className="me-25 bg-danger text-white inline-block" /> Không sử dụng các
                                đoạn văn dài.
                            </p>
                            <p className="mb-50">
                                <X size={14} className="me-25 bg-danger text-white inline-block" /> Bài viết có sử dụng
                                hình ảnh và/hoặc video.
                            </p>
                        </div>
                    </div> */}
                </div>
            </div>
        </div>
    );
};
