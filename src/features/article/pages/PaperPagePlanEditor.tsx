import React, { useEffect, useMemo, useRef, useState } from 'react';
import { Helmet } from 'react-helmet-async';
import { useGraphQLQuery } from '../../../hooks/useGraphQLQuery';
import { LIMIT_MAX, OPERATION_NAME, QUERY_KEY } from '../../../constants/common';
import { useAppStore } from '../../../stores/appStore';
import {
    ARTICLE_ISSUE_PAGE_CREATE,
    ARTICLE_ISSUE_PAGE_DELETE,
    ARTICLE_ISSUE_PAGE_UPDATE_POSITION,
    ARTICLE_LIST,
} from '../../../services/ArticleService';
import { ArticleType } from '../../../types/common/Item';
import { keepPreviousData } from '@tanstack/react-query';
import PaperTypeTab from '../components/PaperTypeTab';
import PaperCanvas from '../components/PaperCanvas';
import { ArrowLeft } from 'react-feather';
import { useNavigate, useParams } from 'react-router-dom';
import Article, {
    ArticleBox,
    ArticleQueryRes,
    PaperSize,
    PaperSizeOptions,
    SearchArticle,
    createTempBoxId,
    getBoxDisplayId,
    ArticleIssuePage,
} from '../../../types/Article';
import { IssuePageDetailQuery } from '../../../types/IssuePage';
import { ISSUE_PAGE_DETAIL } from '../../../services/IssuePageService';
import { useGraphQLMutation } from '../../../hooks/useGraphQLMutation';
import { showToast } from '../../../utils/common';
import ModalConfirm from '../../../components/partials/ModalConfirm';

export const PaperTypeNames = [
    { id: 1, name: 'Chưa chọn số báo' },
    { id: 2, name: 'Chưa chọn số trang' },
];

export default function PaperPagePlanEditor() {
    const { id } = useParams<{ id: string }>();
    const navigate = useNavigate();
    const departmentId = useAppStore((state) => state.departmentId);
    const [title, setTitle] = useState('');
    const [activeTab, setActiveTab] = useState(PaperTypeNames[0].id);
    const [paperSize, setPaperSize] = useState<PaperSize>(PaperSize.A3);
    const [articleBoxes, setArticleBoxes] = useState<ArticleBox[]>([]);
    const [articleIssuePageId, setArticleIssuePageId] = useState<number | null>(null);
    const [showDelete, setShowDelete] = useState(false);
    const [maxDisplayOrder, setMaxDisplayOrder] = useState(0);

    const { data, isLoading, refetch } = useGraphQLQuery<IssuePageDetailQuery, { id: number }>(
        [QUERY_KEY.ISSUE_PAGE, id],
        ISSUE_PAGE_DETAIL,
        {
            id: Number(id),
        },
        '',
        {
            enabled: !!id,
        }
    );
    const issuePage = useMemo(() => data?.issue_pages_detail, [data]);
    useEffect(() => {
        if (issuePage) {
            const articleIssuePages = issuePage.articleIssuePages;
            if (articleIssuePages && articleIssuePages.length > 0) {
                const boxes: ArticleBox[] = articleIssuePages.map((page, index) => ({
                    id: page.id!,
                    articleId: page.article_id,
                    article: page.article,
                    isOverflowing: false,
                    isTemporary: false,
                    x: page.position?.x ?? 50,
                    y: page.position?.y ?? 50,
                    width: page.position?.width ?? 300,
                    height: page.position?.height ?? 200,
                }));
                setArticleBoxes(boxes);

                //find articleIssuePage with max display_order
                const maxOrder = Math.max(...articleIssuePages.map((page) => page.display_order));
                setMaxDisplayOrder(maxOrder);
            }
        }
    }, [issuePage]);

    const { data: articleData } = useGraphQLQuery<ArticleQueryRes, SearchArticle>(
        [QUERY_KEY.ARTICLES, departmentId],
        ARTICLE_LIST,
        {
            page: 1,
            limit: LIMIT_MAX,
            search: '',
            filters: [`article_type_id:=(${ArticleType.PAPER})`, `department_id:=(${departmentId})`],
        },
        OPERATION_NAME.CALL_STATIC_TOKEN,
        {
            placeholderData: keepPreviousData,
        }
    );

    const articles = useMemo(() => articleData?.articles_list.data ?? [], [articleData]);

    const handleSearchChange = (searchValue: string) => {
        setTitle(searchValue);
    };

    const addArticleIssuePageMutation = useGraphQLMutation<
        { article_issue_pages_create: ArticleIssuePage },
        {
            article_id: number;
            issue_page_id: number;
            display_order: number;
            comment: string;
            position: { x: number; y: number; width: number; height: number };
        }
    >(ARTICLE_ISSUE_PAGE_CREATE, '', {
        onSuccess: () => {
            showToast(true, ['Thêm bài viết thành công']);
            refetch();
        },
    });

    const handleAddArticle = (article: Article) => {
        addArticleIssuePageMutation.mutate({
            article_id: article.id!,
            issue_page_id: Number(id),
            display_order: maxDisplayOrder + 1,
            comment: '',
            position: {
                x: 50,
                y: 50,
                width: 300,
                height: 200,
            },
        });
    };

    const handleBackClick = () => {
        navigate(-1);
    };

    const updatePositionMutation = useGraphQLMutation<
        {},
        { items: { id: number; position: { x: number; y: number; width: number; height: number } }[] }
    >(ARTICLE_ISSUE_PAGE_UPDATE_POSITION, '', {
        onSuccess: () => {
            showToast(true, ['Cập nhật thành công']);
            setTimeout(() => {
                handleBackClick();
            }, 500);
        },
    });

    const handleSaveLayout = async () => {
        if (!issuePage || !articleBoxes.length) return;
        const items = articleBoxes.map((box) => ({
            id: Number(box.id),
            position: {
                x: box.x,
                y: box.y,
                width: box.width,
                height: box.height,
            },
        }));
        updatePositionMutation.mutate({ items });
    };

    const handlePaperSizeChange = (size: PaperSize) => {
        setPaperSize(size);
    };

    const handleUpdateBox = (boxId: string | number, updates: Partial<ArticleBox>) => {
        setArticleBoxes((prev) => prev.map((box) => (box.id === boxId ? { ...box, ...updates } : box)));
    };

    const handleRemoveBox = (boxId: string | number) => {
        setArticleIssuePageId(Number(boxId));
        setShowDelete(true);
    };

    const deleteArticleIssuePageMutation = useGraphQLMutation<{ article_issue_pages_delete: boolean }, { id: number }>(
        ARTICLE_ISSUE_PAGE_DELETE,
        '',
        {
            onSuccess: () => {
                setShowDelete(false);
                setArticleIssuePageId(null);
                showToast(true, ['Xóa thành công']);
                refetch();
            },
        }
    );

    const handleDeleteArticleIssuePage = () => {
        if (articleIssuePageId) {
            deleteArticleIssuePageMutation.mutate({
                id: articleIssuePageId,
            });
        }
    };

    return (
        <>
            <Helmet>
                <title>{'Xem nội dung'}</title>
            </Helmet>
            <div className="content-body">
                {/* Header */}
                <div className="mb-1 p-1 bg-light rounded border-b border-gray-200">
                    <h2 className="mb-0 text-xl font-bold">
                        Xem nội dung - {issuePage?.pressPublication?.name} - {issuePage?.issue?.name}
                    </h2>
                </div>

                <div className="row px-1">
                    <div className="col-md-3 col-12">
                        <PaperTypeTab
                            activeTab={activeTab}
                            onTabChange={setActiveTab}
                            articles={articles}
                            onSearchChange={handleSearchChange}
                            searchValue={title}
                            onAddArticle={handleAddArticle}
                        />
                    </div>
                    <div className="col-md-9 col-12">
                        {/* Navigation and Title */}
                        <div className="d-flex justify-content-between align-items-center mb-3">
                            <div className="d-flex align-items-center">
                                <button
                                    className="btn btn-link p-0 me-3 text-decoration-none"
                                    onClick={handleBackClick}
                                >
                                    <ArrowLeft size={20} className="me-1" />
                                    Trở về
                                </button>
                                <h5 className="mb-0">Trang 2: Thời sự - kinh tế</h5>
                            </div>
                            <button
                                className="btn btn-dark"
                                onClick={handleSaveLayout}
                                disabled={updatePositionMutation.isPending}
                            >
                                Lưu dàn trang
                            </button>
                        </div>

                        {/* Paper Size Selection */}
                        <div className="mb-1">
                            <div className="d-flex align-items-center">
                                <label className="me-3">Khổ giấy:</label>
                                <select
                                    className="form-select w-auto"
                                    value={paperSize}
                                    onChange={(e) => handlePaperSizeChange(e.target.value as PaperSize)}
                                >
                                    {PaperSizeOptions.map((option) => (
                                        <option key={option.value} value={option.value}>
                                            {option.label}
                                        </option>
                                    ))}
                                </select>
                            </div>
                        </div>

                        {/* Paper Canvas */}
                        <PaperCanvas
                            paperSize={paperSize}
                            articleBoxes={articleBoxes}
                            onUpdateBox={handleUpdateBox}
                            onRemoveBox={handleRemoveBox}
                        />
                    </div>
                </div>
            </div>
            <ModalConfirm
                show={showDelete}
                text="Bạn có chắc chắn muốn xóa bài viết này?"
                btnDisabled={deleteArticleIssuePageMutation.isPending}
                changeShow={(s: boolean) => setShowDelete(s)}
                submitAction={handleDeleteArticleIssuePage}
            />
        </>
    );
}
