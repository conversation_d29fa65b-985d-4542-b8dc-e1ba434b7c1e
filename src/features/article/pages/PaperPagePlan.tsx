import { keepPreviousData } from '@tanstack/react-query';
import ModalIssuePageFileUpdate from 'features/issue/components/ModalIssuePageFileUpdate';
import { find, isUndefined, omitBy } from 'lodash';
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { Helmet } from 'react-helmet-async';
import { useTranslation } from 'react-i18next';
import { useSearchParams } from 'react-router-dom';
import {
    ISSUE_PAGE_FILE_CREATE,
    ISSUE_PAGE_FILE_DETELE,
    ISSUE_PAGE_FILE_LIST,
    ISSUE_PAGE_FILE_UPDATE,
} from 'services/IssuePageFileService';
import IssuePageFile, { issueApproveFilterConfig, IssuePageFileQuery } from 'types/IssuePageFile';
import ModalConfirm from '../../../components/partials/ModalConfirm';
import ModalContent from '../../../components/partials/ModalContent';
import PaginationTable from '../../../components/partials/PaginationTable';
import { LIMIT_MAX, PAGE_NUMBER_DEFAULT, QUERY_KEY } from '../../../constants/common';
import { useGraphQLMutation } from '../../../hooks/useGraphQLMutation';
import { useGraphQLQuery } from '../../../hooks/useGraphQLQuery';
import {
    ARTICLE_CHANGE_TYPE_SETTING_STATUS,
    ARTICLE_ISSUE_PAGE_CREATE_BATCH,
    ARTICLE_ISSUE_PAGE_DELETE,
    ARTICLE_ISSUE_PAGE_UPDATE_PAGE,
    ARTICLES_CLONE,
} from '../../../services/ArticleService';
import { ISSUE_PAGE_LIST } from '../../../services/IssuePageService';
import { ISSUE_LIST } from '../../../services/IssueService';
import { PRESS_PUBLICATIONS_LIST } from '../../../services/PressPublicationSevice';
import { useAppStore } from '../../../stores/appStore';
import { useAuthStore } from '../../../stores/authStore';
import { ArticleIssuePage, PlanTypes } from '../../../types/Article';
import { ArticleType, ItemStatus } from '../../../types/common/Item';
import { IssueQuery } from '../../../types/Issue';
import { IssuePageQuery } from '../../../types/IssuePage';
import { PressPublicationQuery } from '../../../types/PressPublication';
import { generateFilters, showToast } from '../../../utils/common';
import ModalArticleContent from '../components/ModalArticleContent';
import ModalArticleLogsContent from '../components/ModalArticleLogsContent';
import ModalMoveIssuePage from '../components/ModalMoveIssuePage';
import PaperTab from '../components/PaperTab';
import PaperTabContent from '../components/PaperTabContent';
import SearchPaperPagePlanForm from '../components/SearchPaperPagePlanForm';

interface SearchFormData {
    title: string;
    press_publication_id: number | null;
    issue_id: number | null;
}

export default function PaperPagePlan() {
    const currentUser = useAuthStore((state) => state.user);
    const [activeTab, setActiveTab] = useState(PlanTypes.PAPER);
    const [showModalArticle, setShowModalArticle] = useState(false);
    const departmentId = useAppStore((state) => state.departmentId);
    const [searchParams, setSearchParams] = useSearchParams();
    const debounceRef = useRef<NodeJS.Timeout | null>(null);
    const [page, setPage] = useState(1);
    const [articleTypeSettingStatusTo, setArticleTypeSettingStatusTo] = useState<number | null>(null);
    const [articleSelectedId, setArticleSelectedId] = useState<number | null>(null);
    const [showModalChangeStatus, setShowModalChangeStatus] = useState(false);
    const [issuePageSelectedId, setIssuePageSelectedId] = useState<number | null>(null);
    const [articleSelectedIds, setArticleSelectedIds] = useState<number[]>([]);
    const [showModalIssuePage, setShowModalIssuePage] = useState(false);
    const [customFilterIssuePages, setCustomFilterIssuePages] = useState<string[]>([]);
    const [showModalClone, setShowModalClone] = useState(false);
    const [articleIssuePageId, setArticleIssuePageId] = useState<number | null>(null);
    const [showDelete, setShowDelete] = useState(false);
    const [itemId, setItemId] = useState<number>(0);
    const [showUpdate, setShowUpdate] = useState(false);
    const [showDeleteIssuePageFile, setShowDeleteIssuePageFile] = useState(false);
    const [showModalArticleLogs, setShowModalArticleLogs] = useState(false);

    useEffect(() => {
        setPage(1);
    }, [activeTab]);

    // Search form state
    const [searchFormData, setSearchFormData] = useState<SearchFormData>({
        title: searchParams.get('title') ?? '',
        press_publication_id: searchParams.get('press_publication_id')
            ? Number(searchParams.get('press_publication_id'))
            : null,
        issue_id: searchParams.get('issue_id') ? Number(searchParams.get('issue_id')) : null,
    });

    const baseFilters = useMemo(() => {
        const filters = [`status_id:=(${ItemStatus.ACTIVE})`];

        if (departmentId) {
            filters.push(`department_id:=(${departmentId})`);
        }

        return filters;
    }, [departmentId]);

    // Press Publications Query
    const { data: pressPublicationData } = useGraphQLQuery<PressPublicationQuery>(
        [QUERY_KEY.PRESS_PUBLICATIONS],
        PRESS_PUBLICATIONS_LIST,
        {
            page: 1,
            limit: LIMIT_MAX,
            search: '',
            filters: baseFilters,
        },
        '',
        {
            placeholderData: keepPreviousData,
        }
    );
    const pressPublications = useMemo(
        () => pressPublicationData?.press_publications_list.data ?? [],
        [pressPublicationData]
    );

    // Issues Query - filtered by selected publication
    const issueFilters = useMemo(() => {
        const filters = [];
        if (departmentId) {
            filters.push(`department_id:=(${departmentId})`);
        }
        if (searchFormData.press_publication_id) {
            filters.push(`press_publication_id:=(${searchFormData.press_publication_id})`);
        }
        return filters;
    }, [searchFormData.press_publication_id, departmentId]);

    const { data: issueData } = useGraphQLQuery<IssueQuery>(
        [QUERY_KEY.ISSUES, searchFormData.press_publication_id],
        ISSUE_LIST,
        {
            page: 1,
            limit: LIMIT_MAX,
            search: '',
            filters: issueFilters,
        },
        '',
        {
            enabled: !!searchFormData.press_publication_id,
            placeholderData: keepPreviousData,
        }
    );
    const issues = useMemo(() => issueData?.issues_list.data ?? [], [issueData]);

    // Issue Pages Query - for data fetching
    const issuePageFilters = useMemo(() => {
        const filters = [...baseFilters];
        const filterAll = [...baseFilters];
        if (searchFormData.press_publication_id) {
            filters.push(`press_publication_id:=(${searchFormData.press_publication_id})`);
            filterAll.push(`press_publication_id:=(${searchFormData.press_publication_id})`);
        }
        if (searchFormData.issue_id) {
            filters.push(`issue_id:=(${searchFormData.issue_id})`);
            filterAll.push(`issue_id:=(${searchFormData.issue_id})`);
        }
        if (searchFormData.title) {
            filters.push(`articleIssuePages.article.title:~(${searchFormData.title})`);
        }
        // if (activeTab === PlanTypes.WAITING) {
        //     filters.push(`articleIssuePages.article.typesetting_status_id:!=(${TypeSettingStatus.WAIT})`);
        // } else {
        //     filters.push(`articleIssuePages.article.typesetting_status_id:=(${TypeSettingStatus.WAIT})`);
        // }
        // if (activeTab === PlanTypes.ORGANIZATION || activeTab === PlanTypes.WAITING) {
        //     filters.push(`articleIssuePages.article.workflow.workflow_type_id:=(${WorkflowType.PUBLISHING})`);
        // }
        setCustomFilterIssuePages(filterAll);
        return filters;
    }, [baseFilters, searchFormData.press_publication_id, searchFormData.issue_id, searchFormData.title]);

    const { data: issuePageData, refetch: refetchIssuePages } = useGraphQLQuery<IssuePageQuery>(
        [
            QUERY_KEY.ISSUE_PAGES,
            searchFormData.press_publication_id,
            searchFormData.issue_id,
            searchFormData.title,
            activeTab,
            page,
        ],
        ISSUE_PAGE_LIST,
        {
            page: page,
            limit: 4,
            search: '',
            filters: issuePageFilters,
            sorts: ['page_number:ASC'],
        },
        '',
        {
            enabled: !!searchFormData.issue_id,
            placeholderData: keepPreviousData,
        }
    );
    const issuePages = useMemo(() => issuePageData?.issue_pages_list.data ?? [], [issuePageData]);

    const { data: allIssuePageData } = useGraphQLQuery<IssuePageQuery>(
        [QUERY_KEY.ISSUE_PAGES, searchFormData.press_publication_id, searchFormData.issue_id, activeTab],
        ISSUE_PAGE_LIST,
        {
            page: PAGE_NUMBER_DEFAULT,
            limit: LIMIT_MAX,
            search: '',
            filters: customFilterIssuePages,
        },
        '',
        {
            enabled: !!searchFormData.issue_id && showModalIssuePage,
            placeholderData: keepPreviousData,
        }
    );

    const issuePageInTab = useMemo(
        () => allIssuePageData?.issue_pages_list.data.filter((item) => item.id !== issuePageSelectedId) ?? [],
        [allIssuePageData, issuePageSelectedId]
    );

    const handlePageChange = (_event: React.ChangeEvent<unknown>, page: number) => {
        setPage(page);
    };

    // Update URL query params with debounce
    const updateQueryParams = useCallback(
        (newFormData: SearchFormData) => {
            if (debounceRef.current) {
                clearTimeout(debounceRef.current);
            }

            debounceRef.current = setTimeout(() => {
                const params = new URLSearchParams(searchParams);

                if (newFormData.title) {
                    params.set('title', newFormData.title);
                } else {
                    params.delete('title');
                }

                if (newFormData.press_publication_id) {
                    params.set('press_publication_id', newFormData.press_publication_id.toString());
                } else {
                    params.delete('press_publication_id');
                }

                if (newFormData.issue_id) {
                    params.set('issue_id', newFormData.issue_id.toString());
                } else {
                    params.delete('issue_id');
                }

                setSearchParams(params);
            }, 500);
        },
        [searchParams, setSearchParams]
    );

    // Handle search form data change
    const handleSearchFormChange = useCallback(
        (newFormData: SearchFormData) => {
            setSearchFormData(newFormData);
            updateQueryParams(newFormData);
        },
        [updateQueryParams]
    );

    const handleTabChange = (tabId: number) => {
        setActiveTab(tabId);
    };

    const updateArticleTypeSettingStatusMutation = useGraphQLMutation<
        {},
        { id: number; typesetting_status_id: number }
    >(ARTICLE_CHANGE_TYPE_SETTING_STATUS, '', {
        onSuccess: () => {
            setArticleTypeSettingStatusTo(null);
            setArticleSelectedId(null);
            showToast(true, ['Cập nhật trạng thái thành công']);
            refetchIssuePages();
        },
    });

    const handleUpdateArticleTypeSettingStatus = () => {
        if (articleSelectedId && articleTypeSettingStatusTo) {
            updateArticleTypeSettingStatusMutation.mutate({
                id: articleSelectedId,
                typesetting_status_id: articleTypeSettingStatusTo,
            });
        }
        setShowModalChangeStatus(false);
    };

    const addArticleIssuePagesMutation = useGraphQLMutation<
        { article_issue_pages_create_batch: ArticleIssuePage[] },
        { article_ids: number[]; issue_page_id: number }
    >(ARTICLE_ISSUE_PAGE_CREATE_BATCH, '', {
        onSuccess: () => {
            setShowModalArticle(false);
            setIssuePageSelectedId(null);
            setArticleSelectedIds([]);
            showToast(true, ['Thêm bài viết thành công']);
            refetchIssuePages();
        },
    });

    const handleAddArticleIssuePages = () => {
        if (!issuePageSelectedId || articleSelectedIds.length === 0) return;
        addArticleIssuePagesMutation.mutate({
            article_ids: articleSelectedIds,
            issue_page_id: issuePageSelectedId,
        });
    };

    const handleMoveIssuePage = (issuePageId: number, articleIssuePageId: number) => {
        setIssuePageSelectedId(issuePageId);
        setArticleIssuePageId(articleIssuePageId);
        setShowModalIssuePage(true);
    };

    const updatePageForArticleIssuePageMutation = useGraphQLMutation<
        { article_issue_pages_update_page: ArticleIssuePage },
        { id: number; issue_page_id: number }
    >(ARTICLE_ISSUE_PAGE_UPDATE_PAGE, '', {
        onSuccess: () => {
            setShowModalIssuePage(false);
            setIssuePageSelectedId(null);
            setArticleIssuePageId(null);
            showToast(true, ['Cập nhật trang thành công']);
            refetchIssuePages();
        },
    });

    const handleConfirmMovePage = (selectedIssuePageId: number) => {
        if (issuePageSelectedId && articleIssuePageId) {
            updatePageForArticleIssuePageMutation.mutate({
                id: articleIssuePageId,
                issue_page_id: selectedIssuePageId,
            });
        }
    };

    const onCloneArticle = (articleId: number) => {
        setArticleSelectedId(articleId);
        setShowModalClone(true);
    };

    const cloneArticleMutation = useGraphQLMutation<{}, { id: number; article_type_id: number }>(ARTICLES_CLONE, '', {
        onSuccess: () => {
            setShowModalClone(false);
            setArticleSelectedId(null);
            showToast(true, ['Cập nhật thành công']);
            refetchIssuePages();
        },
    });

    const handleCloneArticle = () => {
        if (articleSelectedId) {
            cloneArticleMutation.mutate({
                id: articleSelectedId,
                article_type_id: ArticleType.ELECTRONIC,
            });
        }
    };

    const onDeleteArticleIssuePage = (articleIssuePageId: number) => {
        setArticleIssuePageId(articleIssuePageId);
        setShowDelete(true);
    };

    const deleteArticleIssuePageMutation = useGraphQLMutation<{ article_issue_pages_delete: boolean }, { id: number }>(
        ARTICLE_ISSUE_PAGE_DELETE,
        '',
        {
            onSuccess: () => {
                setShowDelete(false);
                setArticleIssuePageId(null);
                showToast(true, ['Xóa thành công']);
                refetchIssuePages();
            },
        }
    );

    const handleDeleteArticleIssuePage = () => {
        if (articleIssuePageId) {
            deleteArticleIssuePageMutation.mutate({
                id: articleIssuePageId,
            });
        }
    };

    const issuePageFileParamsConfig = omitBy(
        {
            limit: LIMIT_MAX,
            page: 1,
            issue_id: searchFormData.issue_id,
            'issue.press_publication_id': searchFormData.press_publication_id,
            search: searchFormData.title,
        },
        isUndefined
    );

    const {
        limit: issuePageFileLimit,
        page: issuePageFilePage,
        search: issuePageFileSearch,
        ...dataParamConfig
    } = issuePageFileParamsConfig;

    const filters = generateFilters(dataParamConfig, issueApproveFilterConfig);
    const { t } = useTranslation();

    const { data, refetch } = useGraphQLQuery<IssuePageFileQuery>(
        [QUERY_KEY.ISSUE_PAGE_FILES, issuePageFileParamsConfig, filters],
        ISSUE_PAGE_FILE_LIST,
        {
            page: issuePageFilePage,
            limit: issuePageFileLimit,
            search: issuePageFileSearch,
            filters: filters.length > 0 ? filters : undefined,
        },
        '',
        {
            placeholderData: keepPreviousData,
            enabled: activeTab === PlanTypes.APPROVE,
        }
    );

    const issuePageFiles = data?.issue_page_files_list.data ?? [];

    const saveMutation = useGraphQLMutation<{}, { id?: number; body: Partial<IssuePageFile> }>(
        itemId > 0 ? ISSUE_PAGE_FILE_UPDATE : ISSUE_PAGE_FILE_CREATE,
        '',
        {
            onSuccess: () => {
                showToast(true, [t('success.update')]);
                setShowUpdate(false);
                setItemId(0);
                refetch();
            },
            onError: () => showToast(false, [t('error.common')]),
        }
    );

    const deleteMutation = useGraphQLMutation(ISSUE_PAGE_FILE_DETELE, '', {
        onSuccess: () => {
            showToast(true, [t('success.delete')]);
            setShowDeleteIssuePageFile(false);
            setItemId(0);
            refetch();
        },
        onError: () => showToast(false, [t('error.common')]),
    });

    const handleEdit = (id: number) => {
        saveMutation.reset();
        setItemId(id);
        setShowUpdate(true);
    };

    const handleDelete = (id: number) => {
        deleteMutation.reset();
        setItemId(id);
        setShowDeleteIssuePageFile(true);
    };

    const updateItem = async (body: IssuePageFile) => {
        delete body.id;
        if (itemId === 0) {
            saveMutation.mutate({ body });
        } else {
            saveMutation.mutate({ id: itemId, body });
        }
    };

    const deleteIssuePageFile = () => {
        if (itemId) {
            deleteMutation.mutate({ id: itemId });
        }
    };

    const handleAdd = () => {
        saveMutation.reset();
        setShowUpdate(true);
        setItemId(0);
    };

    const onViewArticleLogs = (articleId: number) => {
        setArticleSelectedId(articleId);
        setShowModalArticleLogs(true);
    };

    const onCloseModalArticleLogs = (s: boolean) => {
        setShowModalArticleLogs(s);
        setArticleSelectedId(null);
    };

    return (
        <>
            <Helmet>
                <title>{'Quản lý dàn trang'}</title>
            </Helmet>
            <div className="content-body">
                <div className="col-12">
                    <SearchPaperPagePlanForm
                        pressPublications={pressPublications}
                        issues={issues}
                        searchFormData={searchFormData}
                        onSearchFormChange={handleSearchFormChange}
                    />
                    <div className="card">
                        <div className="card-body">
                            <div className="nav-align-top nav-tabs-shadow">
                                <PaperTab activeTab={activeTab} onTabChange={handleTabChange} />
                                {(!issuePageData || !issuePages || issuePages.length === 0) && (
                                    <div className="text-center text-muted py-3">Chưa có dữ liệu</div>
                                )}
                                {issuePages && issuePages.length > 0 && (
                                    <PaperTabContent
                                        activeTab={activeTab}
                                        onOpenModal={(id: number) => {
                                            setShowModalArticle(true);
                                            setIssuePageSelectedId(id);
                                        }}
                                        issuePages={issuePages}
                                        issues={issues}
                                        issuePageFiles={issuePageFiles}
                                        setArticleTypeSettingStatusTo={setArticleTypeSettingStatusTo}
                                        setArticleSelectedId={setArticleSelectedId}
                                        setShowModalChangeStatus={setShowModalChangeStatus}
                                        currentUser={currentUser}
                                        handleMoveIssuePage={handleMoveIssuePage}
                                        onCloneArticle={onCloneArticle}
                                        onDeleteArticleIssuePage={onDeleteArticleIssuePage}
                                        handleEdit={handleEdit}
                                        handleDelete={handleDelete}
                                        handleAdd={handleAdd}
                                        onViewArticleLogs={onViewArticleLogs}
                                    />
                                )}
                                {activeTab === PlanTypes.APPROVE ? (
                                    <PaginationTable
                                        countItem={data?.issue_page_files_list.totalCount}
                                        totalPage={data?.issue_page_files_list.totalPages}
                                        currentPage={data?.issue_page_files_list.currentPage}
                                        handlePageChange={handlePageChange}
                                    />
                                ) : (
                                    <PaginationTable
                                        countItem={issuePageData?.issue_pages_list.totalCount}
                                        totalPage={issuePageData?.issue_pages_list.totalPages}
                                        currentPage={issuePageData?.issue_pages_list.currentPage}
                                        handlePageChange={handlePageChange}
                                    />
                                )}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <ModalContent
                title="Chọn bài viết"
                show={showModalArticle}
                changeShow={(s: boolean) => setShowModalArticle(s)}
                content={
                    <ModalArticleContent
                        onSelectedArticles={(articleIds) => setArticleSelectedIds(articleIds)}
                        onCancelArticle={() => setShowModalArticle(false)}
                        onSubmitArticle={handleAddArticleIssuePages}
                    />
                }
                modalSize="xl"
            />
            <ModalConfirm
                show={showModalChangeStatus}
                text="Bạn có chắc chắn muốn chuyển trạng thái của bài viết này?"
                btnDisabled={updateArticleTypeSettingStatusMutation.isPending}
                changeShow={(s: boolean) => setShowModalChangeStatus(s)}
                submitAction={handleUpdateArticleTypeSettingStatus}
            />
            <ModalMoveIssuePage
                show={showModalIssuePage}
                issuePageInTab={issuePageInTab}
                changeShow={(s: boolean) => setShowModalIssuePage(s)}
                onConfirm={handleConfirmMovePage}
            />
            <ModalConfirm
                show={showModalClone}
                text="Bạn có chắc chắn muốn chuyển website?"
                btnDisabled={cloneArticleMutation.isPending}
                changeShow={(s: boolean) => setShowModalClone(s)}
                submitAction={handleCloneArticle}
            />
            <ModalConfirm
                show={showDelete}
                text="Bạn có chắc chắn muốn xóa bài viết này?"
                btnDisabled={deleteArticleIssuePageMutation.isPending}
                changeShow={(s: boolean) => setShowDelete(s)}
                submitAction={handleDeleteArticleIssuePage}
            />

            <ModalIssuePageFileUpdate
                show={showUpdate}
                issuePageFile={find(issuePageFiles, { id: itemId })}
                pressPublications={pressPublications}
                issues={issues}
                departmentId={departmentId}
                isLoading={saveMutation.isPending}
                changeShow={(s: boolean) => setShowUpdate(s)}
                submitAction={updateItem}
                issuePages={issuePages}
                selectedIssueId={searchFormData.issue_id ? searchFormData.issue_id.toString() : undefined}
            />

            <ModalConfirm
                show={showDeleteIssuePageFile}
                text={t('confirm.delete')}
                btnDisabled={deleteMutation.isPending}
                changeShow={(s: boolean) => setShowDeleteIssuePageFile(s)}
                submitAction={deleteIssuePageFile}
            />

            <ModalContent
                title="Lịch sử thay đổi"
                show={showModalArticleLogs}
                changeShow={onCloseModalArticleLogs}
                content={<ModalArticleLogsContent articleId={articleSelectedId} />}
                modalSize="lg"
            />
        </>
    );
}
