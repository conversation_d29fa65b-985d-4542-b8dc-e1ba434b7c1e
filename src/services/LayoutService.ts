import { gql } from 'graphql-request';

export const LAYOUT_LIST = gql`
    query Layouts_list($page: Int!, $limit: Int!, $filters: [String!], $sorts: [String!], $search: String) {
        layouts_list(body: { page: $page, limit: $limit, filters: $filters, sorts: $sorts, search: $search }) {
            totalCount
            totalPages
            currentPage
            data {
                id
                name
                desc
                content
                status_id
                layout_type_id
                is_default
                is_mobile
            }
        }
    }
`;

export const LAYOUT_DETAIL = gql`
    query Layouts_detail($id: Int!) {
        layouts_detail(id: $id) {
            id
            name
            desc
            content
            status_id
            layout_type_id
            is_default
            is_mobile
        }
    }
`;

export const LAYOUT_CREATE = gql`
    mutation Layouts_create($body: LayoutSaveInputDto!) {
        layouts_create(body: $body) {
            id
        }
    }
`;

export const LAYOUT_UPDATE = gql`
    mutation Layouts_update($id: Int!, $body: LayoutSaveInputDto!) {
        layouts_update(id: $id, body: $body) {
            id
        }
    }
`;

export const LAYOUT_DELETE = gql`
    mutation Layouts_delete($id: Int!) {
        layouts_delete(id: $id)
    }
`;
