import { gql } from 'graphql-request';

export const ROYALTY_PARAMS_LIST = gql`
    query Royalty_params_list($page: Int!, $limit: Int!, $filters: [String!], $sorts: [String!]) {
        royalty_params_list(body: { page: $page, limit: $limit, filters: $filters, sorts: $sorts }) {
            totalCount
            totalPages
            currentPage
            data {
                id
                name
                desc
                values
                status_id
                display_order
            }
        }
    }
`;

export const ROYALTY_PARAMS_CREATE = gql`
    mutation Royalty_params_create($body: RoyaltyParamSaveInputDto!) {
        royalty_params_create(body: $body) {
            id
            name
            desc
            values
            status_id
            display_order
        }
    }
`;

export const ROYALTY_PARAMS_UPDATE = gql`
    mutation Royalty_params_update($id: Int!, $body: RoyaltyParamSaveInputDto!) {
        royalty_params_update(id: $id, body: $body) {
            id
        }
    }
`;

export const ROYALTY_PARAMS_DELETE = gql`
    mutation Royalty_params_delete($id: Int!) {
        royalty_params_delete(id: $id)
    }
`;
