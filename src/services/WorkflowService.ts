import { gql } from 'graphql-request';

export const WORKFLOW_LIST = gql`
    query Workflows_list($page: Int!, $limit: Int!, $filters: [String!], $sorts: [String!], $search: String) {
        workflows_list(body: { page: $page, limit: $limit, filters: $filters, sorts: $sorts, search: $search }) {
            totalCount
            totalPages
            currentPage
            data {
                id
                name
                status_id
                desc
                display_order
                workflow_type_id
                department_id
            }
        }
    }
`;

export const WORKFLOW_CREATE = gql`
    mutation Workflows_create($body: WorkflowSaveInputDto!) {
        workflows_create(body: $body) {
            id
            name
            status_id
            desc
            display_order
            workflow_type_id
            department_id
        }
    }
`;

export const WORKFLOW_UPDATE = gql`
    mutation Workflows_update($id: Int!, $body: WorkflowSaveInputDto!) {
        workflows_update(id: $id, body: $body) {
            id
            name
            status_id
            desc
            display_order
            workflow_type_id
            department_id
        }
    }
`;

export const WORKFLOW_DELETE = gql`
    mutation Workflows_delete($id: Int!) {
        workflows_delete(id: $id)
    }
`;
