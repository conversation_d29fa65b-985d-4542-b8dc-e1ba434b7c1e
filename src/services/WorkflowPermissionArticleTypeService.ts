import { gql } from 'graphql-request';

export const WORKFLOW_PERMISSION_ARTICLE_TYPE_LIST = gql`
    query Workflow_permission_article_types_list(
        $page: Int!
        $limit: Int!
        $filters: [String!]
        $sorts: [String!]
        $search: String
    ) {
        workflow_permission_article_types_list(
            body: { page: $page, limit: $limit, filters: $filters, sorts: $sorts, search: $search }
        ) {
            id
            workflow_permission_id
            article_type_id
            user_department_id
        }
    }
`;

export const WORKFLOW_PERMISSION_ARTICLE_TYPE_CREATE = gql`
    mutation Workflow_permission_article_types_create($body: ArticleTypeSaveInputDto!) {
        workflow_permission_article_types_create(body: $body) {
            id
        }
    }
`;

export const WORKFLOW_PERMISSION_ARTICLE_TYPE_UPDATE = gql`
    mutation Workflow_permission_article_types_update($id: Int!, $body: ArticleTypeSaveInputDto!) {
        workflow_permission_article_types_update(id: $id, body: $body) {
            id
        }
    }
`;
