import { gql } from 'graphql-request';

export const TAG_LIST = gql`
    query Tags_list($page: Int!, $limit: Int!, $filters: [String!], $sorts: [String!], $search: String) {
        tags_list(body: { page: $page, limit: $limit, filters: $filters, sorts: $sorts, search: $search }) {
            totalCount
            totalPages
            currentPage
            data {
                id
                name
                slug
                status_id
                department_id
            }
        }
    }
`;

export const TAG_CREATE = gql`
    mutation Tags_create($body: TagSaveInputDto!) {
        tags_create(body: $body) {
            id
        }
    }
`;

export const TAG_UPDATE = gql`
    mutation Tags_update($id: Int!, $body: TagSaveInputDto!) {
        tags_update(id: $id, body: $body) {
            id
        }
    }
`;

export const TAG_DELETE = gql`
    mutation Tags_delete($id: Int!) {
        tags_delete(id: $id)
    }
`;
