import { gql } from 'graphql-request';

export const ADVERTISE_LIST = gql`
    query Advertises_list($page: Int!, $limit: Int!, $filters: [String!], $sorts: [String!], $search: String) {
        advertises_list(body: { page: $page, limit: $limit, filters: $filters, sorts: $sorts, search: $search }) {
            totalCount
            totalPages
            currentPage
            data {
                id
                code
                name
                desc
                type_id
                speed
                display_type_id
                status_id
                department_id
            }
        }
    }
`;

export const ADVERTISE_CREATE = gql`
    mutation Advertises_create($body: AdvertiseSaveInputDto!) {
        advertises_create(body: $body) {
            id
            code
            name
            desc
            type_id
            speed
            display_type_id
            status_id
            department_id
        }
    }
`;

export const ADVERTISE_UPDATE = gql`
    mutation Advertises_update($id: Int!, $body: AdvertiseSaveInputDto!) {
        advertises_update(id: $id, body: $body) {
            id
            code
            name
            desc
            type_id
            speed
            display_type_id
            status_id
            department_id
        }
    }
`;

export const ADVERTISE_DELETE = gql`
    mutation Advertises_delete($id: Int!) {
        advertises_delete(id: $id)
    }
`;
