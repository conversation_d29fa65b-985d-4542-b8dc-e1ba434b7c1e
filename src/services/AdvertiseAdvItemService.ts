import { gql } from 'graphql-request';

export const ADVERTISE_ADV_ITEMS_LIST = gql`
    query Advertise_adv_items_list($page: Int!, $limit: Int!, $filters: [String!], $sorts: [String!], $search: String) {
        advertise_adv_items_list(
            body: { page: $page, limit: $limit, filters: $filters, sorts: $sorts, search: $search }
        ) {
            totalCount
            totalPages
            currentPage
            data {
                id
                advertise_id
                adv_item_id
                display_order
            }
        }
    }
`;

export const ADVERTISE_ADV_ITEMS_CREATE = gql`
    mutation Advertise_adv_items_create($body: AdvertiseAdvItemSaveInputDto!) {
        advertise_adv_items_create(body: $body) {
            id
        }
    }
`;

export const ADVERTISE_ADV_ITEMS_UPDATE = gql`
    mutation Advertise_adv_items_update($id: Int!, $body: AdvertiseAdvItemSaveInputDto!) {
        advertise_adv_items_update(id: $id, body: $body) {
            id
        }
    }
`;

export const ADVERTISE_ADV_ITEMS_DELETE = gql`
    mutation Advertise_adv_items_delete($id: Int!) {
        advertise_adv_items_delete(id: $id)
    }
`;
