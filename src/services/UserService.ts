import { gql } from 'graphql-request';

export const LOGIN = gql`
    mutation Auth_login($body: LoginInputDto!) {
        auth_login(body: $body) {
            id
            full_name
            role_id
            avatar
            is_require_change_password
            token {
                access_token
                refresh_token
            }
        }
    }
`;

export const PROFILE = gql`
    query Auth_profile {
        auth_profile {
            id
            user_name
            full_name
            phone
            email
            address
            avatar_id
            gender_id
            role_id
            status_id
            telegram_id
            birthday
            login_at
            email_notify
            telegram_notify
            zalo_notify
            email_verified
            phone_verified
            require_change_password
            is_require_change_password
            avatar {
                id
                file_name
                file_url
            }
            groups {
                id
                department_id
                actions {
                    id
                    name
                    url
                    icon
                    parent_id
                    display_order
                }
            }
            userDepartments {
                id
                department {
                    id
                    name
                    parent_id
                    display_order
                }
            }
        }
    }
`;

export const LOGOUT = gql`
    query Auth_logout {
        auth_logout
    }
`;

export const REFRESH = gql`
    mutation Auth_refresh($refresh_token: String!) {
        auth_refresh(body: { refresh_token: $refresh_token }) {
            id
            full_name
            role_id
            avatar
            token {
                access_token
                refresh_token
            }
        }
    }
`;

export const USER_LIST = gql`
    query Users_list($page: Int!, $limit: Int!, $filters: [String!], $sorts: [String!], $search: String) {
        users_list(body: { page: $page, limit: $limit, filters: $filters, sorts: $sorts, search: $search }) {
            totalCount
            totalPages
            currentPage
            data {
                id
                user_name
                full_name
                phone
                email
                role_id
                status_id
                login_at
                created_at
                avatar {
                    id
                    file_name
                    file_url
                }
                groups {
                    id
                    name
                    type_id
                }
            }
        }
    }
`;

export const USER_DELETE = gql`
    mutation Users_delete($id: Int!) {
        users_delete(id: $id)
    }
`;

export const USER_CREATE = gql`
    mutation Users_create($body: UserCreateInputDto!) {
        users_create(body: $body) {
            id
        }
    }
`;

export const USER_UPDATE = gql`
    mutation Users_update($id: Int!, $body: UserUpdateInputDto!) {
        users_update(id: $id, body: $body) {
            id
        }
    }
`;

export const USER_DETAIL = gql`
    query Users_detail($id: Int!) {
        users_detail(id: $id) {
            id
            user_name
            full_name
            phone
            email
            address
            avatar_id
            gender_id
            role_id
            status_id
            telegram_id
            birthday
            email_notify
            telegram_notify
            zalo_notify
            email_verified
            phone_verified
            require_change_password
            created_at
            avatar {
                id
                file_name
                file_url
            }
            groups {
                id
                name
            }
            userDepartments {
                id
                department {
                    id
                }
            }
        }
    }
`;

export const USER_RESET_PASSWORD = gql`
    mutation Users_reset_pass($id: Int!, $password: String!, $confirm_password: String!) {
        users_reset_pass(id: $id, body: { password: $password, confirm_password: $confirm_password })
    }
`;

export const USERS_CHANGE_GROUPS = gql`
    mutation Users_change_groups($id: Int!, $body: ChangeGroupsInputDto!) {
        users_change_groups(id: $id, body: $body)
    }
`;

export const USERS_CHANGE_DEPARTMENTS = gql`
    mutation Users_change_departments($id: Int!, $body: ChangeDepartmentsInputDto!) {
        users_change_departments(id: $id, body: $body)
    }
`;

export const AUTH_UPDATE = gql`
    mutation Auth_update($body: ProfileInputDto!) {
        auth_update(body: $body) {
            id
        }
    }
`;

export const AUTH_CHANGE_PASSWORD = gql`
    mutation Auth_change_pass($old_password: String!, $password: String!, $confirm_password: String!) {
        auth_change_pass(
            body: { old_password: $old_password, password: $password, confirm_password: $confirm_password }
        )
    }
`;

export const USER_FORGOT_PASSWORD = gql`
    mutation Auth_forgot_pass($body: ForgotPasswordDto!) {
        auth_forgot_pass(body: $body)
    }
`;

export const USER_CHANGE_PASSWORD = gql`
    mutation Auth_reset_pass($body: ResetPasswordDto!) {
        auth_reset_pass(body: $body)
    }
`;

export const generateSecurePassword = () => {
    const upper = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    const lower = 'abcdefghijklmnopqrstuvwxyz';
    const number = '0123456789';
    const special = '!@#$%^&*()_+~`|}{[]\\:;?><,./-=';
    const all = upper + lower + number + special;

    let password = '';

    password += upper[Math.floor(Math.random() * upper.length)];
    password += lower[Math.floor(Math.random() * lower.length)];
    password += number[Math.floor(Math.random() * number.length)];
    password += special[Math.floor(Math.random() * special.length)];

    for (let i = 4; i < 12; i++) {
        password += all[Math.floor(Math.random() * all.length)];
    }

    return password
        .split('')
        .sort(() => 0.5 - Math.random())
        .join('');
};
