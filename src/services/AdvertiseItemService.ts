import { gql } from 'graphql-request';

export const ADVERTISE_ITEMS_LIST = gql`
    query Adv_items_list($page: Int!, $limit: Int!, $filters: [String!], $sorts: [String!], $search: String) {
        adv_items_list(body: { page: $page, limit: $limit, filters: $filters, sorts: $sorts, search: $search }) {
            totalCount
            totalPages
            currentPage
            data {
                id
                name
                desc
                content
                type_id
                file_id
                width
                height
                status_id
                start_date
                end_date
                department_id
                file {
                    id
                    file_name
                    file_url
                }
            }
        }
    }
`;

export const ADVERTISE_ITEMS_CREATE = gql`
    mutation Adv_items_create($body: AdvItemSaveInputDto!) {
        adv_items_create(body: $body) {
            id
        }
    }
`;

export const ADVERTISE_ITEMS_UPDATE = gql`
    mutation Adv_items_update($id: Int!, $body: AdvItemSaveInputDto!) {
        adv_items_update(id: $id, body: $body) {
            id
        }
    }
`;

export const ADVERTISE_ITEMS_DELETE = gql`
    mutation Adv_items_delete($id: Int!) {
        adv_items_delete(id: $id)
    }
`;
