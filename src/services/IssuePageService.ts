import { gql } from 'graphql-request';

export const ISSUE_PAGE_LIST = gql`
    query Issue_pages_list($page: Int!, $limit: Int!, $filters: [String!], $sorts: [String!], $search: String) {
        issue_pages_list(body: { page: $page, limit: $limit, filters: $filters, sorts: $sorts, search: $search }) {
            totalCount
            totalPages
            currentPage
            data {
                id
                created_by
                updated_by
                created_at
                updated_at
                name
                page_number
                status_id
                press_publication_id
                issue_id
                department_id
                approve_status_id
                file_id
                issue {
                    name
                }
                articleIssuePages {
                    id
                    created_by
                    updated_by
                    created_at
                    updated_at
                    article_id
                    issue_page_id
                    display_order
                    comment
                    article {
                        id
                        created_by
                        updated_by
                        created_at
                        updated_at
                        title
                        sub_title
                        brief_title
                        slug
                        desc
                        content
                        avatar1_id
                        avatar2_id
                        workflow_id
                        article_type_id
                        pseudonym_id
                        department_id
                        publish_date
                        source
                        file_id
                        root_article_id
                        lock_user_id
                        lock_at
                        is_sync
                        web_layout_id
                        mobile_layout_id
                        press_publication_id
                        issue_id
                        typesetting_status_id
                        workflow {
                            name
                            id
                            status_id
                            workflow_type_id
                            department_id
                        }
                        avatar1 {
                            id
                            file_url
                        }
                        articleCategories {
                            id
                            category {
                                id
                                category_type_id
                                name
                            }
                            article_id
                            category_id
                            display_order
                            is_major
                        }
                        pseudonym {
                            id
                            name
                        }
                    }
                }
            }
        }
    }
`;

export const ISSUE_PAGE_CREATE = gql`
    mutation Issue_pages_create($body: IssuePageSaveInputDto!) {
        issue_pages_create(body: $body) {
            id
        }
    }
`;

export const ISSUE_PAGE_UPDATE = gql`
    mutation Issue_pages_update($id: Int!, $body: IssuePageSaveInputDto!) {
        issue_pages_update(id: $id, body: $body) {
            id
        }
    }
`;

export const ISSUE_PAGE_DELETE = gql`
    mutation Issue_pages_delete($id: Int!) {
        issue_pages_delete(id: $id)
    }
`;

export const ISSUE_PAGE_FILES_DETAIL = gql`
    query Issue_page_files_detail($id: Int!) {
        issue_page_files_detail(id: $id) {
            id
            created_by
            updated_by
            created_at
            updated_at
            issue_id
            issue_page_id
            file_id
            file_ids
            comment
            file_comments
            status_id
            name
            file {
                id
                file_url
                file_name
            }
            files {
                id
                file_url
                file_name
                created_at
                issuePageFiles {
                    id
                    file_comments
                }
            }
        }
    }
`;

export const ISSUE_PAGE_FILES_UPDATE_COMMENT = gql`
    mutation Issue_page_files_update_comment($id: Int!, $file_id: Int!, $comment: String!) {
        issue_page_files_update_comment(id: $id, body: { file_id: $file_id, comment: $comment }) {
            id
        }
    }
`;

export const ISSUE_PAGE_DETAIL = gql`
    query Issue_pages_detail($id: Int!) {
        issue_pages_detail(id: $id) {
            id
            name
            page_number
            status_id
            press_publication_id
            issue_id
            department_id
            approve_status_id
            file_id
            articleIssuePages {
                id
                article_id
                issue_page_id
                display_order
                comment
                position
                article {
                    id
                    content
                }
            }
            pressPublication {
                id
                name
            }
            issue {
                id
                name
            }
        }
    }
`;
