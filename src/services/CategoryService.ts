import { gql } from 'graphql-request';
import Category from '../types/Category';

export const CATEGORY_LIST = gql`
    query Categories_list($page: Int!, $limit: Int!, $filters: [String!], $sorts: [String!], $search: String) {
        categories_list(body: { page: $page, limit: $limit, filters: $filters, sorts: $sorts, search: $search }) {
            totalCount
            totalPages
            currentPage
            data {
                id
                name
                status_id
                desc
                article_type_id
                category_type_id
                department_id
                parent_id
                display_order
                slug
                is_major
                mobile_layout_id
                web_layout_id
                article_web_layout_id
                article_mobile_layout_id
                children {
                    id
                    name
                    status_id
                    desc
                    article_type_id
                    category_type_id
                    department_id
                    parent_id
                    display_order
                    slug
                    mobile_layout_id
                    web_layout_id
                    is_major
                }
            }
        }
    }
`;

export const CATEGORY_LIST_WITHOUT_CHILDREN = gql`
    query Categories_list($page: Int!, $limit: Int!, $filters: [String!], $sorts: [String!], $search: String) {
        categories_list(body: { page: $page, limit: $limit, filters: $filters, sorts: $sorts, search: $search }) {
            totalCount
            totalPages
            currentPage
            data {
                id
                name
                status_id
                desc
                article_type_id
                category_type_id
                department_id
                parent_id
                display_order
                slug
                is_major
                mobile_layout_id
                web_layout_id
                article_web_layout_id
                article_mobile_layout_id
            }
        }
    }
`;

export const CATEGORY_CREATE = gql`
    mutation Categories_create($body: CategorySaveInputDto!) {
        categories_create(body: $body) {
            id
        }
    }
`;

export const CATEGORY_UPDATE = gql`
    mutation Categories_update($id: Int!, $body: CategorySaveInputDto!) {
        categories_update(id: $id, body: $body) {
            id
        }
    }
`;

export const CATEGORY_DELETE = gql`
    mutation Categories_delete($id: Int!) {
        categories_delete(id: $id)
    }
`;

// export function getHierarchyList(items: Category[]) {
//     const retVal: Category[] = [];
//     items.forEach((item) => {
//         if (!item.parent_id) {
//             const children = items.filter((child) => child.parent_id === item.id);
//             retVal.push({
//                 ...item,
//                 children,
//             });
//         }
//     });
//     return retVal;
// }

export function getFlatCategories(categories: Category[]) {
    const retVal: Category[] = [];
    categories.forEach((c1) => {
        if (!c1.parent_id) {
            retVal.push(c1);
            categories.forEach((c2) => {
                if (c2.parent_id === c1.id) {
                    retVal.push(c2);
                }
            });
        }
    });
    return retVal;
}
