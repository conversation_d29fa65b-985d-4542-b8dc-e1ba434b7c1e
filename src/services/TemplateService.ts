import { gql } from 'graphql-request';

export const TEMPLATE_LIST = gql`
    query Templates_list($page: Int!, $limit: Int!, $filters: [String!], $sorts: [String!], $search: String) {
        templates_list(body: { page: $page, limit: $limit, filters: $filters, sorts: $sorts, search: $search }) {
            totalCount
            totalPages
            currentPage
            data {
                id
                name
                desc
                content
                status_id
                avatar_id
                type_id
                avatar {
                    id
                    file_url
                }
            }
        }
    }
`;

export const TEMPLATE_DETAIL = gql`
    query Templates_detail($id: Float!) {
        templates_detail(id: $id) {
            id
            name
            desc
            content
            status_id
            avatar_id
            type_id
            avatar {
                id
                file_url
            }
        }
    }
`;

export const TEMPLATE_CREATE = gql`
    mutation Templates_create($body: TemplateSaveInputDto!) {
        templates_create(body: $body) {
            id
        }
    }
`;

export const TEMPLATE_UPDATE = gql`
    mutation Templates_update($id: Int!, $body: TemplateSaveInputDto!) {
        templates_update(id: $id, body: $body) {
            id
        }
    }
`;

export const TEMPLATE_DELETE = gql`
    mutation Templates_delete($id: Int!) {
        templates_delete(id: $id)
    }
`;
