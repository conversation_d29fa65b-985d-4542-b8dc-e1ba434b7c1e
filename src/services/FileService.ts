import { ItemFile } from 'types/common/Item';
import Http from './http.class';
import { gql } from 'graphql-request';

const http = new Http('multipart/form-data').instance;

const FileService = {
    async upload(
        fileObj: File,
        type: string = '',
        department_id?: number,
        folder_id?: number,
        onProgress?: (percent: number) => void,
        is_newsroom: boolean = true,
        parent_id?: number,
        file_tags?: string[],
        file_title?: string
    ) {
        const formData = new FormData();
        formData.append('file', fileObj);
        if (department_id !== undefined) {
            formData.append('department_id', department_id.toString());
        }
        formData.append('is_newsroom', is_newsroom.toString());
        if (folder_id) formData.append('folder_id', folder_id.toString());
        if (parent_id) formData.append('parent_id', parent_id.toString());
        if (file_tags && file_tags.length > 0) formData.append('file_tags', JSON.stringify(file_tags));
        if (file_title) formData.append('file_title', file_title);

        // Nếu có callback onProgress, sử dụng nó để theo dõi tiến trình
        if (onProgress) {
            const { data } = await http.post<{ upload: ItemFile }>(
                type === '' ? '/files/upload' : `/files/upload?type=${type}`,
                formData,
                {
                    onUploadProgress: (progressEvent) => {
                        if (progressEvent.total) {
                            const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);
                            onProgress(percentCompleted);
                        }
                    },
                }
            );
            return data;
        } else {
            // Nếu không có callback, sử dụng cách cũ
            const { data } = await http.post<{ upload: ItemFile }>(
                type === '' ? '/files/upload' : `/files/upload?type=${type}`,
                formData
            );
            return data;
        }
    },
};

export default FileService;

export const FILES_LIST = gql`
    query Files_list($page: Int!, $limit: Int!, $filters: [String!], $sorts: [String!], $search: String) {
        files_list(body: { page: $page, limit: $limit, filters: $filters, sorts: $sorts, search: $search }) {
            totalCount
            totalPages
            currentPage
            data {
                id
                file_name
                file_url
                file_size
                mime_type
                folder_id
                department_id
                created_by
                is_newsroom
                created_at
                updated_at
                file_title
                tags {
                    id
                    name
                }
            }
        }
    }
`;

export const FILE_CHANGE_NAME = gql`
    mutation File_change_name($file_id: Int!, $file_name: String!) {
        file_change_name(body: { file_id: $file_id, file_name: $file_name }) {
            id
        }
    }
`;

export const FILE_DELETE = gql`
    mutation File_delete($file_id: Int!) {
        file_delete(id: $file_id)
    }
`;

export const FILE_CHANGE_TITLE = gql`
    mutation File_change_title($file_id: Int!, $file_title: String!) {
        file_change_title(body: { file_id: $file_id, file_title: $file_title }) {
            id
        }
    }
`;

export const FILE_BY_URL = gql`
    query Files_by_url($file_url: String!) {
        files_by_url(file_url: $file_url) {
            id
            file_name
            file_url
            folder_id
            department_id
            parent_id
            is_newsroom
            file_title
            mime_type
            file_size
            parent {
                id
                file_name
                file_url
                folder_id
                department_id
                parent_id
                is_newsroom
                file_title
                mime_type
                file_size
            }
        }
    }
`;
