import { gql } from 'graphql-request';

export const PSEUDONYM_LIST = gql`
    query Pseudonyms_list($page: Int!, $limit: Int!, $filters: [String!], $sorts: [String!], $search: String) {
        pseudonyms_list(body: { page: $page, limit: $limit, filters: $filters, sorts: $sorts, search: $search }) {
            totalCount
            totalPages
            currentPage
            data {
                id
                name
                status_id
                is_default
                user_id
            }
        }
    }
`;

export const PSEUDONYM_CREATE = gql`
    mutation Pseudonyms_create($body: PseudonymSaveInputDto!) {
        pseudonyms_create(body: $body) {
            id
            name
            status_id
            is_default
            user_id
        }
    }
`;

export const PSEUDONYM_UPDATE = gql`
    mutation Pseudonyms_update($id: Int!, $body: PseudonymSaveInputDto!) {
        pseudonyms_update(id: $id, body: $body) {
            id
            name
            status_id
            is_default
            user_id
        }
    }
`;

export const PSEUDONYM_DELETE = gql`
    mutation Pseudonyms_delete($id: Int!) {
        pseudonyms_delete(id: $id)
    }
`;
