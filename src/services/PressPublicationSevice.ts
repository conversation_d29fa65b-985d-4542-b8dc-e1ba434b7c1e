import { gql } from 'graphql-request';

export const PRESS_PUBLICATIONS_LIST = gql`
    query PressPublicationsList($page: Int!, $limit: Int!, $filters: [String!], $sorts: [String!], $search: String) {
        press_publications_list(
            body: { page: $page, limit: $limit, filters: $filters, sorts: $sorts, search: $search }
        ) {
            totalCount
            totalPages
            currentPage
            data {
                id
                name
                status_id
                display_order
                avatar_id
                avatar {
                    id
                    file_url
                }
                issue_title
                issue_status_id
                issue_type_id
                issue_days
                issue_offset
                department_id
                page_count
                created_at
                issue_pre_created
                page_size_id
            }
        }
    }
`;

export const PRESS_PUBLICATIONS_CREATE = gql`
    mutation PressPublicationsCreate($body: PressPublicationSaveInputDto!) {
        press_publications_create(body: $body) {
            id
        }
    }
`;

export const PRESS_PUBLICATIONS_UPDATE = gql`
    mutation PressPublicationsUpdate($id: Int!, $body: PressPublicationSaveInputDto!) {
        press_publications_update(id: $id, body: $body) {
            id
        }
    }
`;

export const PRESS_PUBLICATIONS_DELETE = gql`
    mutation PressPublicationsDelete($id: Int!) {
        press_publications_delete(id: $id)
    }
`;
