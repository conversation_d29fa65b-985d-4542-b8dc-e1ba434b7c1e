import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';

export interface AppStore {
    departmentId: number;
    setDepartmentId: (departmentId: number) => void;
}

export const useAppStore = create<AppStore>()(
    devtools(
        persist(
            (set) => ({
                departmentId: 0,
                setDepartmentId: (departmentId: number) => set({ departmentId }),
            }),
            {
                name: 'app-storage',
            }
        )
    )
);
