"use strict";$((function(){var t="ltr",e=!1;"rtl"==$("html").data("textdirection")&&(t="rtl"),"rtl"===t&&(e=!0);var o=$(".sidebar-shop"),s=$(".btn-cart"),a=$(".body-content-overlay"),i=$(".shop-sidebar-toggler"),n=$(".grid-view-btn"),r=$(".list-view-btn"),l=document.getElementById("price-slider"),d=$("#ecommerce-products"),c=$(".dropdown-sort .dropdown-item"),v=$(".dropdown-toggle .active-sorting"),m=$(".btn-wishlist"),h="app-ecommerce-checkout.html";if("laravel"===$("body").attr("data-framework")){var g=$("body").attr("data-asset-path");h=g+"app/ecommerce/checkout"}c.length&&c.on("click",(function(){var t=$(this).text();v.text(t)})),i.length&&i.on("click",(function(){o.toggleClass("show"),a.toggleClass("show"),$("body").addClass("modal-open")})),a.length&&a.on("click",(function(t){o.removeClass("show"),a.removeClass("show"),$("body").removeClass("modal-open")})),void 0!==typeof l&&null!==l&&noUiSlider.create(l,{start:[1500,3500],direction:t,connect:!0,tooltips:[!0,!0],format:wNumb({decimals:0}),range:{min:51,max:5e3}}),n.length&&n.on("click",(function(){d.removeClass("list-view").addClass("grid-view"),r.removeClass("active"),n.addClass("active")})),r.length&&r.on("click",(function(){d.removeClass("grid-view").addClass("list-view"),n.removeClass("active"),r.addClass("active")})),s.length&&s.on("click",(function(t){var o=$(this),s=o.find(".add-to-cart");s.length>0&&t.preventDefault(),s.text("View In Cart").removeClass("add-to-cart").addClass("view-in-cart"),o.attr("href",h),toastr.success("","Added Item In Your Cart 🛒",{closeButton:!0,tapToDismiss:!1,rtl:e})})),m.length&&m.on("click",(function(){var t=$(this);t.find("svg").toggleClass("text-danger"),t.find("svg").hasClass("text-danger")&&toastr.success("","Added to wishlist ❤️",{closeButton:!0,tapToDismiss:!1,rtl:e})}))})),$(window).on("resize",(function(){$(window).outerWidth()>=991&&($(".sidebar-shop").removeClass("show"),$(".body-content-overlay").removeClass("show"))}));