$((function(){const e=$(".select2"),a=$("#editUserForm"),n=$(".phone-number-mask");e.length&&e.each((function(){var e=$(this);e.wrap('<div class="position-relative"></div>').select2({dropdownParent:e.parent()})})),n.length&&n.each((function(){new Cleave($(this),{phone:!0,phoneRegionCode:"US"})})),a.length&&a.validate({rules:{modalEditUserFirstName:{required:!0},modalEditUserLastName:{required:!0},modalEditUserName:{required:!0,minlength:6,maxlength:30}},messages:{modalEditUserName:{required:"Please enter your username",minlength:"The name must be more than 6 and less than 30 characters long",maxlength:"The name must be more than 6 and less than 30 characters long"}}})}));