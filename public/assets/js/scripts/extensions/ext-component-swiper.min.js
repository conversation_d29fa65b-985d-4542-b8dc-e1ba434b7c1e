$((function(){"use strict";var e=$(".slide-1"),i=$(".slide-250"),n=$(".slide-500"),t=$(".prepend-2-slides"),r=$(".append-slide"),a=new Swiper(".swiper-default"),p=(new Swiper(".swiper-navigations",{navigation:{nextEl:".swiper-button-next",prevEl:".swiper-button-prev"}}),new Swiper(".swiper-paginations",{pagination:{el:".swiper-pagination"}}),new Swiper(".swiper-progress",{pagination:{el:".swiper-pagination",type:"progressbar"},navigation:{nextEl:".swiper-button-next",prevEl:".swiper-button-prev"}}),new Swiper(".swiper-multiple",{slidesPerView:3,spaceBetween:30,pagination:{el:".swiper-pagination",clickable:!0}}),new Swiper(".swiper-multi-row",{slidesPerView:3,slidesPerColumn:2,spaceBetween:30,slidesPerColumnFill:"row",pagination:{el:".swiper-pagination",clickable:!0}}),new Swiper(".swiper-centered-slides",{slidesPerView:"auto",centeredSlides:!0,spaceBetween:30,navigation:{nextEl:".swiper-button-next",prevEl:".swiper-button-prev"}}),$(".swiper-slide").length);p&&(p=Math.floor(p/2));new Swiper(".swiper-centered-slides-2",{slidesPerView:"auto",initialSlide:p,centeredSlides:!0,spaceBetween:30,slideToClickedSlide:!0});function s(e){var i=a.slides[e],n=$(i).attr("id");$(".wrapper-content").removeClass("active"),$("[data-faq="+n+"]").addClass("active")}s(p),a.on("slideChange",(function(){s(a.realIndex)}));new Swiper(".swiper-fade-effect",{spaceBetween:30,effect:"fade",pagination:{el:".swiper-pagination",clickable:!0},navigation:{nextEl:".swiper-button-next",prevEl:".swiper-button-prev"}}),new Swiper(".swiper-cube-effect",{effect:"cube",grabCursor:!0,cubeEffect:{shadow:!0,slideShadows:!0,shadowOffset:20,shadowScale:.94},pagination:{el:".swiper-pagination"}}),new Swiper(".swiper-coverflow",{effect:"coverflow",grabCursor:!0,centeredSlides:!0,slidesPerView:"auto",coverflowEffect:{rotate:50,stretch:0,depth:100,modifier:1,slideShadows:!0},pagination:{el:".swiper-pagination"}}),new Swiper(".swiper-autoplay",{spaceBetween:30,centeredSlides:!0,autoplay:{delay:2500,disableOnInteraction:!1},pagination:{el:".swiper-pagination",clickable:!0},navigation:{nextEl:".swiper-button-next",prevEl:".swiper-button-prev"}});var l=new Swiper(".gallery-thumbs",{spaceBetween:10,slidesPerView:4,freeMode:!0,watchSlidesVisibility:!0,watchSlidesProgress:!0}),w=(new Swiper(".gallery-top",{spaceBetween:10,navigation:{nextEl:".swiper-button-next",prevEl:".swiper-button-prev"},thumbs:{swiper:l}}),new Swiper(".swiper-parallax",{speed:600,parallax:!0,pagination:{el:".swiper-pagination",clickable:!0},navigation:{nextEl:".swiper-button-next",prevEl:".swiper-button-prev"}}),new Swiper(".swiper-lazy-loading",{lazy:!0,pagination:{el:".swiper-pagination",clickable:!0},navigation:{nextEl:".swiper-button-next",prevEl:".swiper-button-prev"}}),new Swiper(".swiper-responsive-breakpoints",{slidesPerView:5,spaceBetween:50,pagination:{el:".swiper-pagination",clickable:!0},breakpoints:{1024:{slidesPerView:4,spaceBetween:40},768:{slidesPerView:3,spaceBetween:30},640:{slidesPerView:2,spaceBetween:20},320:{slidesPerView:1,spaceBetween:10}}}),600),o=1,c=new Swiper(".swiper-virtual",{slidesPerView:3,centeredSlides:!0,spaceBetween:30,pagination:{el:".swiper-pagination",type:"fraction"},navigation:{nextEl:".swiper-button-next",prevEl:".swiper-button-prev"},virtual:{slides:function(){for(var e=[],i=0;i<600;i+=1)e.push("Slide "+(i+1));return e}()}});e&&e.on("click",(function(e){e.preventDefault(),c.slideTo(0,0)})),i&&i.on("click",(function(e){e.preventDefault(),c.slideTo(249,0)})),n&&n.on("click",(function(e){e.preventDefault(),c.slideTo(499,0)})),t&&t.on("click",(function(e){e.preventDefault(),c.virtual.prependSlide(["Slide "+--o,"Slide "+--o])})),r&&r.on("click",(function(e){e.preventDefault(),c.virtual.appendSlide("Slide "+ ++w)}))}));