!function(e,t,o){"use strict";[].slice.call(t.querySelectorAll('[data-bs-toggle="popover"]')).map((function(e){return new bootstrap.Popover(e)}));var n=t.getElementById("show-popover");new bootstrap.Popover(n,{title:"Popover Show Event",content:"Bonbon chocolate cake. Pudding halvah pie apple pie topping marzipan pastry marzipan cupcake.",trigger:"click",placement:"right"});n.addEventListener("show.bs.popover",(function(){alert("Show event fired.")}));var p=t.getElementById("shown-popover");new bootstrap.Popover(p,{title:"Popover Shown Event",content:"Bonbon chocolate cake. Pudding halvah pie apple pie topping marzipan pastry marzipan cupcake.",trigger:"click",placement:"bottom"});p.addEventListener("shown.bs.popover",(function(){alert("Shown event fired.")}));var r=t.getElementById("hide-popover");new bootstrap.Popover(r,{title:"Popover Hide Event",content:"Bonbon chocolate cake. Pudding halvah pie apple pie topping marzipan pastry marzipan cupcake.",trigger:"click",placement:"bottom"});r.addEventListener("hide.bs.popover",(function(){alert("Hide event fired.")}));var a=t.getElementById("hidden-popover");new bootstrap.Popover(a,{title:"Popover Hidden Event",content:"Bonbon chocolate cake. Pudding halvah pie apple pie topping marzipan pastry marzipan cupcake.",trigger:"click",placement:"left"});a.addEventListener("hidden.bs.popover",(function(){alert("Hidden event fired.")}));var i=t.getElementById("inserted-popover");new bootstrap.Popover(i,{title:"Popover Inserted Event",content:"Bonbon chocolate cake. Pudding halvah pie apple pie topping marzipan pastry marzipan cupcake.",trigger:"click",placement:"left"});i.addEventListener("inserted.bs.popover",(function(){alert("Inserted event fired.")}));var c=t.getElementById("show-method"),d=new bootstrap.Popover(c);c.addEventListener("click",(function(){d.show()}));var l=t.getElementById("hide-method"),v=new bootstrap.Popover(l);l.addEventListener("mouseenter",(function(){v.show()})),l.addEventListener("click",(function(){v.hide()}));var s=t.getElementById("toggle-method"),g=new bootstrap.Popover(s);s.addEventListener("click",(function(){g.toggle()}));var h=t.getElementById("manual-popover"),m=new bootstrap.Popover(h);h.addEventListener("click",(function(){m.toggle()}))}(window,document,jQuery);