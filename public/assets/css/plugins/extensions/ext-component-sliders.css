.noUi-target {
  background-color: rgba(115, 103, 240, 0.12);
  border-width: 0;
  box-shadow: none;
  border-radius: 1rem; }

.noUi-target.noUi-connect {
  box-shadow: none; }

.noUi-horizontal {
  height: 6px; }

.noUi-horizontal .noUi-handle {
  width: 1rem;
  height: 1rem;
  cursor: pointer;
  right: -0.5rem;
  top: -4px; }

.noUi-horizontal .noUi-tooltip {
  bottom: 135% !important; }

.noUi-pips {
  color: #b9b9c3; }

.noUi-marker {
  background: #ebe9f1; }

.noUi-value {
  font-size: 0.857rem; }

.noUi-marker-horizontal.noUi-marker,
.noUi-marker-horizontal.noUi-marker-large {
  height: 8px; }

.noUi-handle {
  box-shadow: none;
  border: none;
  border-radius: 50%;
  background: #fff;
  border: 2px solid #a42c48; }

.noUi-handle:after, .noUi-handle:before {
  display: none; }

.noUi-connect {
  background: #a42c48;
  box-shadow: none; }

.noUi-tooltip {
  transform: translate(-10%, -50%);
  font-size: 0.857rem;
  border: none;
  color: #6e6b7b;
  background-color: #f8f8f8;
  padding: 0.1718rem 0.35rem;
  font-weight: 500; }

.noUi-vertical {
  display: inline-block;
  width: 6px; }

.noUi-vertical .noUi-origin {
  right: auto; }

.noUi-vertical .noUi-handle {
  width: 1rem;
  height: 1rem;
  top: -0.375rem;
  left: -0.3rem; }

.noUi-vertical .noUi-tooltip {
  right: 135% !important; }

.hover_val {
  font: 400 12px Arial;
  color: #6e6b7b;
  display: block;
  margin: 15px 0; }

.noUi-handle:focus {
  outline: 0; }

_:-ms-lang(x),
.slider-select {
  flex: 0 0 10%;
  max-width: 10%; }

.dark-layout .noUi-handle {
  background-color: #283046; }

.dark-layout .noUi-tooltip {
  background-color: #161d31;
  color: #b4b7bd; }

.dark-layout .noUi-pips,
.dark-layout .noUi-value {
  color: #b4b7bd; }

.dark-layout .noUi-marker {
  background: #3b4253; }

[data-textdirection='rtl'] .noUi-txt-dir-rtl.noUi-horizontal .noUi-origin {
  right: auto; }

[data-textdirection='rtl'] .noUi-txt-dir-rtl.noUi-horizontal .noUi-handle {
  left: -3px; }
